package publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.*;
import com.suncent.smc.SuncentSmcApplication;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.enums.EbayCategoryTreeEnum;
import com.suncent.smc.common.enums.PublishType;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.framework.web.service.DictService;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.api.domain.EbayCompatibilityProperties;
import com.suncent.smc.persistence.api.service.IEbayShippingDetailsTypeService;
import com.suncent.smc.persistence.api.service.IEbayShippingExcludeLocationService;
import com.suncent.smc.persistence.cdp.service.IBrandService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.cdp.service.ISiteService;
import com.suncent.smc.persistence.configuration.category.domain.entity.CategoryInfo;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.RenderTemplateDescriptionToHtml;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayDescription;
import com.suncent.smc.persistence.template.service.ITemplateEbayDescriptionService;
import com.suncent.smc.persistence.template.service.ITemplateWidgetService;
import com.suncent.smc.provider.biz.configuration.ViolateWordBiz;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import com.suncent.smc.provider.biz.publication.ListingInfoBiz;
import com.suncent.smc.provider.biz.template.TemplateEbayDescriptionBiz;
import com.suncent.smc.quartz.task.listing.eb.EbayVideoTask;
import com.suncent.smc.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.shiro.mgt.SecurityManager;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.thymeleaf.spring5.SpringTemplateEngine;

import java.io.IOException;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/2/1 13:50
 */
@Slf4j
@SpringBootTest(classes = {SuncentSmcApplication.class})
@RunWith(SpringRunner.class)

public class EbayListingPushTest {
    @Autowired
    IGoodsService goodsService;

    @Autowired
    IListingAttributeTempService listingAttributeTempService;


    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    IGoodsResourceService goodsResourceService;
    @Autowired
    IGoodsDescriptionService goodsDescriptionService;
    @Autowired
    IGoodsSpecificationService goodsSpecificationService;
    @Autowired
    IAmazonGoodsSafetyService amazonGoodsSafetyService;
    @Autowired
    IAmazonGoodsInfoLineService amazonGoodsInfoLineService;
    @Autowired
    IListingEbayLineService ebayGoodsLineService;
    @Autowired
    IAmazonGoodsDetailService amazonGoodsDetailService;
    @Autowired
    CreatePlatformCode createPlatformCode;
    @Autowired
    IShopService shopService;
    @Autowired
    DictService dictService;
    @Autowired
    IBrandService brandService;
    @Autowired
    ISiteService siteService;
    @Autowired
    IListingEbayValueService ebayValueService;
    @Autowired
    IListingAttributeTempService ebayValueTempService;
    @Autowired
    IListingAdaptiveTempService adaptiveTempService;
    @Autowired
    IListingEbayAdaptiveService adaptiveService;
    @Autowired
    ListingTemplateService listingTemplateService;
    @Autowired
    private IListingAmazonAttributeLineService listingAmazonAttributeLineService;
    @Autowired
    private IListingEbayPolicyService ebayPolicyService;
    @Autowired
    private IListingEbayShippingHeadService ebayShippingHeadService;
    @Autowired
    private IListingShippingLocationLineService shippingLocationLineService;
    @Autowired
    private IListingShippingTypeLineService shippingTypeLineService;
    @Autowired
    private IEbayShippingDetailsTypeService ebayShippingDetailsTypeService;

    @Autowired
    private ListingInfoBiz listingInfoBiz;
    @Autowired
    private SecurityManager securityManager;

    @Autowired
    private IEbayShippingExcludeLocationService ebayShippingExcludeLocationService;
    @Autowired
    private IAliOssService aliyunOssService;
    @Value("${aliyun.oss.suncentUrlPrefix}")
    public String suncentUrlPrefix;
    @Value("${aliyun.oss.defaultBucketName}")
    public String defaultBucketName;

    @Value("${pdm.add_pdm_goods_status}")
    private String ADD_PDM_GOODS_STATUS;

    @Autowired
    IListingLogService listingLogService;
    @Autowired
    private ViolateWordBiz violateWordBiz;

    @Autowired
    private ISysUserService userService;

    @Autowired
    private ICategoryInfoService categoryInfoService;
    @Autowired
    private ITemplateWidgetService templateWidgetService;

    @Autowired
    private SpringTemplateEngine templateEngine;
    @Autowired
    private ITemplateEbayDescriptionService templateEbayDescriptionService;

    @Autowired
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private TemplateEbayDescriptionBiz templateEbayDescriptionBiz;

    @Value("${api.ebay-getCompatibilityProperties-url}")
    private String GET_COMPATIBILITY_PROPERTIES_URL;

    final String PUSHING_URL_DEV = "http://127.0.0.1:9011/ebay/item/BU115/addItem";
    final String PUSHING_URL_PROD = "http://127.0.0.1:9012/item/";
    final String END_URL_PROD = "http://127.0.0.1:9012/item/BU115/endFixedPriceItem/";
    final String FIX_PDM_GOODS_STATUS = "http://127.0.0.1:8083/pdm/MappingGoods/createMapping";

    /**
     * @description: TODO 下架接口联调
     * @param: []
     * @return: void
     * <AUTHOR>
     * @date: 2023/2/3 16:28
     */
    @Test
    public void endFixedPriceItem() {
        String s = END_URL_PROD + "295526680740";
        String post = HttpUtils.post(s, "");
        System.out.println(post);
    }
    // 244
    @Test
    public void getListingFieldMap() {
        listingInfoBiz.getListingFieldMap(24);
    }

    /**
     * @description: 拼接参数的测试类
     */
    @Test
    public void pushEbayList() {
        GoodsHead goodsHead1 = new GoodsHead();
        goodsHead1.setPlatform("EB");
        goodsHead1.setPublishStatus(1);
        //1、查询出需要刊登的数据【状态为刊登中】
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead1);
        goodsHeads.stream().forEach(goodsHead -> {
            //2、新建对接使用的对象
            com.suncent.smc.quartz.domain.EbayItemVO ebayItemVO = new com.suncent.smc.quartz.domain.EbayItemVO();
            //3、new一个ebay SDK的对象
            ItemType itemType = new ItemType();
            Integer listingHeadId = goodsHead.getId();
            //4、查询刊登头数据
            GoodsHead goodsHeadById = goodsHeadService.selectListingGoodsHeadById(listingHeadId);
            Integer goodsHeadId = goodsHeadById.getId();
            //5、查询描述信息
            GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHeadId);
            //6、查询ebay行信息
            ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHeadId);
            Integer ebayLineId = listingEbayLine.getId();
            //7、查询物流行信息
            ListingEbayShippingHead listingEbayShippingHead = ebayShippingHeadService.selectListingEbayShippingHeadByLineId(ebayLineId);
            //查询适配信息
//            ListingEbayAdaptive adaptive = new ListingEbayAdaptive();
//            adaptive.setListingLineId(ebayLineId);
//            List<ListingEbayAdaptive> ebayAdaptiveList = adaptiveService.selectListingEbayAdaptiveList(adaptive);
            List<ListingEbayAdaptive> ebayAdaptiveList = adaptiveService.selectListByEbayLineId(ebayLineId);
            //8、查询付款方式信息
            ListingEbayPolicy listingEbayPolicy = ebayPolicyService.selectListingEbayPolicyByLineId(ebayLineId);
            //9、查询资源信息
            List<GoodsResource> goodsResourcesList = goodsResourceService.selectListingGoodsResourceByHeadId(goodsHeadId);
            //10、对资源数据进行换链
            this.replaceResourcesUrl(goodsResourcesList);

            if (ObjectUtils.isEmpty(goodsDescription) || ObjectUtils.isEmpty(listingEbayLine)
                    || ObjectUtils.isEmpty(listingEbayPolicy) || CollectionUtils.isEmpty(goodsResourcesList)
                    || ObjectUtils.isEmpty(listingEbayShippingHead)) {
                //修改数据状态 上架中—>刊登失败
                goodsHead.setPublishStatus(8);
                goodsHeadService.updateListingGoodsHead(goodsHead);
                //记录操作日志
                ListingLog listingLog = new ListingLog();
                listingLog.setStatus(1);
                listingLog.setDetails("ebay商品刊登本地组装数据失败,请重新编辑再次刊登");
                listingLog.setListingId(goodsHead.getId());
                listingLog.setErrorMsg("商品数据不完整,请检查数据");
                listingLog.setOperName(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                listingLog.setOperTime(new Date());
                listingLogService.insertListingLog(listingLog);
                return;
            }

            List<ListingShippingTypeLine> listingShippingTypeLines = shippingTypeLineService.selectListingShippingTypeLineByHeadId(listingEbayShippingHead.getId());
            List<ListingShippingLocationLine> listingShippingLocationLines = shippingLocationLineService.selectByShippingHeadId(listingEbayShippingHead.getId());
            List<ListingEbayValue> listingEbayValues = ebayValueService.selectListingEbayValueByLineId(ebayLineId);

            //违禁词处理
            try {
                violateWordBiz.checkViolateWord(true,
                        //描述
                        goodsDescription,
                        //属性
                        listingEbayValues.stream().map(listingEbayValue -> {
                                    ListingAmazonAttributeLine amazonAttributeLine = new ListingAmazonAttributeLine();
                                    amazonAttributeLine.setTableName(listingEbayValue.getName());
                                    amazonAttributeLine.setTableValue(listingEbayValue.getValue());
                                    amazonAttributeLine.setAttributeMemo(listingEbayValue.getAttributeMemo());
                                    return amazonAttributeLine;
                                }
                        ).collect(Collectors.toList()),
                        //头
                        goodsHead);
            } catch (Exception e) {
                log.error("商品id:{},商品name:{},违禁词校验失败", goodsHead.getId(), goodsHead.getTitle(), e);
                //记录操作日志
                ListingLog listingLog = new ListingLog();
                listingLog.setStatus(1);
                listingLog.setDetails("ebay商品刊登违禁词校验失败,请重新编辑再次刊登");
                listingLog.setListingId(goodsHead.getId());
                listingLog.setErrorMsg(e.getMessage());
                listingLog.setOperName(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                listingLog.setOperTime(new Date());
                listingLogService.insertListingLog(listingLog);

            }
            JSONObject jsonObject = null;
            JSONObject data = null;
            try {
                //10、开始组装数据基本数据
                ItemType itemType1 = setItemTypeBase(itemType, goodsHeadById, goodsDescription, listingEbayLine, listingEbayShippingHead, goodsResourcesList, listingEbayValues);
                //11、开始组装数据基本对象数据
                ItemType itemType2 = setItemTypeObj(itemType1, goodsHeadById, listingEbayLine, listingEbayPolicy, goodsResourcesList,
                        listingShippingTypeLines, listingShippingLocationLines, listingEbayValues, ebayAdaptiveList);

                ebayItemVO.setItemType(itemType2);
                //12、将组装好的对象数据进行定制化处理
                String s = JSONObject.toJSONString(ebayItemVO).replace("\"sKU\"", "\"sku\"")
                        .replace("\"mPN\"", "\"mpn\"")
                        .replace("\"uPC\"", "\"upc\"");
                log.info("ebay定时刊登商品---商品id: {}", goodsHeadById.getId());
                //13、发送请求进行 对接api服务进行刊登
                String post = HttpUtils.post(PUSHING_URL_PROD + goodsHead.getShopCode() + "/addItem", s);
                log.info("ebay定时刊登商品结束---商品id:{}", goodsHeadById.getId());
                jsonObject=JSONObject.parseObject(post);
                data = JSONObject.parseObject(jsonObject.getString("data"));
            } catch (Exception e) {
                //修改smc中的状态
                goodsHead.setPublishStatus(8);
                goodsHeadService.updateListingGoodsHead(goodsHead);

                ListingLog listingLog = new ListingLog();
                listingLog.setStatus(1);
                listingLog.setDetails("ebay商品上架失败到ebay平台");
                listingLog.setListingId(goodsHead.getId());
                listingLog.setErrorMsg(ObjectUtils.isNotEmpty(jsonObject) ? jsonObject.toJSONString() : "ebay商品上架失败到ebay平台,请联系管理员");
                listingLog.setOperName(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                listingLog.setOperTime(new Date());
                listingLogService.insertListingLog(listingLog);
                return;
            }
            //14、获取对应的反馈结果进行处理，成功失败修改对应的smc中的状态，以及pdm中的状态
            if ("success".equals(data.getString("pushStatus"))) {
                String json = "[{\"asin\": \""+ data.getString("pushItemId") +"\",\"platformSku\": \"" + goodsHead.getPlatformGoodsCode() + "\",\"shopCode\": \"" + goodsHead.getShopCode() + "\"}]";
                String operators = "738";
                String deptId = "380";
                try {
                    operators = Long.valueOf(goodsHead.getCreateBy()).toString();
                } catch (NumberFormatException e) {
                }
                SysUser sysUser = userService.selectUserById(Long.valueOf(operators));
                if (Objects.nonNull(sysUser)) {
                    deptId = String.valueOf(sysUser.getDeptId());
                }

                String goodsCode = goodsHead.getPdmGoodsCode();
                //修改smc中的状态
                goodsHead.setPublishStatus(2);
                goodsHead.setOnlineTime(new Date());
                goodsHead.setPlatformGoodsId(data.getString("pushItemId"));
                //修改 适配状态
                this.getAdaptationStatus(goodsHead, ebayAdaptiveList);
                goodsHeadService.updateListingGoodsHead(goodsHead);
                //15、修改pdm的库中的数据状态
                fixPdmStatus(json, operators, deptId, goodsCode, goodsHead);

                ListingLog listingLog = new ListingLog();
                listingLog.setStatus(0);
                listingLog.setDetails("ebay商品上架成功到ebay平台");
                listingLog.setListingId(goodsHead.getId());
                listingLog.setOperName(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                listingLog.setOperTime(new Date());
                listingLogService.insertListingLog(listingLog);
            } else {
                //修改smc中的状态
                goodsHead.setPublishStatus(8);
                goodsHeadService.updateListingGoodsHead(goodsHead);

                ListingLog listingLog = new ListingLog();
                listingLog.setStatus(1);
                listingLog.setDetails("ebay商品上架失败到ebay平台");
                listingLog.setListingId(goodsHead.getId());
                listingLog.setErrorMsg(data.getString("pushMessage"));
                listingLog.setOperName(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                listingLog.setOperTime(new Date());
                listingLogService.insertListingLog(listingLog);
            }
        });
    }
    /**
     * 获取适配状态
     *
     * @param goodsHead
     * @param ebayAdaptiveList
     */
    private void getAdaptationStatus(GoodsHead goodsHead, List<ListingEbayAdaptive> ebayAdaptiveList) {
        goodsHead.setAdaptationStatus("适配成功");
        if (ObjectUtils.isEmpty(ebayAdaptiveList)) {
            goodsHead.setAdaptationStatus("待适配");
            return;
        }
        ebayAdaptiveList.parallelStream().forEach(a -> {
            if (ObjectUtils.isEmpty(a.getMake()) || ObjectUtils.isEmpty(a.getYear()) || ObjectUtils.isEmpty(a.getModel())) {
                goodsHead.setAdaptationStatus("适配失败");
                return;
            }
            if (ObjectUtils.isEmpty(a.getSubmodel())) {
                if (ObjectUtils.isEmpty(a.getTrim()) || ObjectUtils.isEmpty(a.getEngine())) {
                    goodsHead.setAdaptationStatus("适配失败");
                    return;
                }
            }
        });
    }


    /**
     * 替换商品图片链接
     *
     * @param goodsResourcesList
     */
    public void replaceResourcesUrl(List<GoodsResource> goodsResourcesList) {
        if (ObjectUtils.isEmpty(goodsResourcesList)) {
            return;
        }
        Iterator<GoodsResource> iterator = goodsResourcesList.iterator();
        while (iterator.hasNext()){
            GoodsResource g = iterator.next();
            if (StringUtils.isEmpty(g.getResourceUrl()) || !g.getResourceUrl().contains("http")) {
                goodsResourceService.deleteListingGoodsResourceById(g.getId());
                iterator.remove();
                continue;
            }
            String newFileName = System.currentTimeMillis() + ".jpg";
            try {
                String url = "smc/files/" + DateUtils.getDate() + "/" + newFileName;
                if (g.getResourceUrl().contains("?")) {
                    g.setResourceUrl(g.getResourceUrl().substring(0, g.getResourceUrl().indexOf("?")));
                }
                aliyunOssService.putObjectByUrl(defaultBucketName, url, g.getResourceUrl());
                g.setResourceUrl(suncentUrlPrefix + url);
            } catch (IOException e) {
                log.error("Ebay商品图片换链异常", e);
            }

        }
    }


    private void fixPdmStatus(String json, String operators, String deptId, String goodsCode, GoodsHead goodsHead) {
        try {
            Map<String, Object> pdmMap = new HashMap<>();
            pdmMap.put("json", json);
            pdmMap.put("operators", operators);
            pdmMap.put("deptId", deptId);
            pdmMap.put("goodsCode", goodsCode);
            log.info("操作Ebay商品刊登完成,准备添加PDM状态映射:{}", JSON.toJSONString(pdmMap));
            String pdm = HttpUtils.post(ADD_PDM_GOODS_STATUS, pdmMap);
            AjaxResult ajaxResult = JSONObject.parseObject(pdm, AjaxResult.class);
            Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);
            if (ajaxResult.isSuccess()) {
                goodsHead.setPdmStatus(1);
                goodsHeadService.updateListingGoodsHead(goodsHead);
            } else {
                //为10000为编码重复 也为已映射
                if ("10000".equals(code.toString())) {
                    goodsHead.setPdmStatus(1);
                    goodsHeadService.updateListingGoodsHead(goodsHead);
                }
            }
            log.info("操作Ebay商品刊登完成,完成添加PDM状态映射:{}", JSON.toJSONString(pdm));
        } catch (Exception e) {
            log.warn("新增pdm状态失败", e);
        }
    }
    public ItemType setItemTypeBase(ItemType itemType, GoodsHead goodsHead, GoodsDescription goodsDescription, ListingEbayLine listingEbayLine, ListingEbayShippingHead listingEbayShippingHead, List<GoodsResource> goodsResourcesList, List<ListingEbayValue> listingEbayValues) {


        //国家-           -取值逻辑-  自动生成根据location
        itemType.setCountry(CountryCodeType.US);

        //币种-           -取值逻辑-  自动生成根据country
        itemType.setCurrency(CurrencyCodeType.USD);

        //地区-           -取值逻辑-  sc_smc_listing_ebay_line行表中的-location
        itemType.setLocation(listingEbayLine.getLocation());

        //邮政编码-        -取值逻辑-   自动生成根据location
        itemType.setPostalCode(listingEbayLine.getPostcode());

        //描述-           -取值逻辑-sc_smc_listing_goods_description   若有descriptionID需要根据模板id组装描述信息，若无则取detail_description
        itemType.setDescription(buildDescription(goodsResourcesList, goodsDescription,listingEbayValues,goodsHead.getTitle()));

        //活动天数-        -取值逻辑-   sc_smc_listing_ebay_line行表中的-sell_day
        itemType.setListingDuration(getListingDuration(goodsHead,listingEbayLine));

        //销售方式-       -取值逻辑-    sc_smc_listing_goods_head头表-publish_type【暂时固定为固定售卖类型】
        itemType.setListingType(getListingType(goodsHead));

        //专用电话号码表-  -取值逻辑-  默认false
        itemType.setPrivateListing(false);

        //库存数量-       -取值逻辑-    头表中-stock_on_sales_qty
        itemType.setQuantity(getQuantity(goodsHead));

        //站点-           -取值逻辑-  默认ebay摩托
        itemType.setSite(getSite(goodsHead));

        //副标题【收费】   -取值逻辑-  头表中-subtitle
//        itemType.setSubTitle("");


        //标题-           -取值逻辑-  头表中-title
        itemType.setTitle(goodsHead.getTitle());

        //SKU-            -取值逻辑-    头表中-platform_goods_code
        itemType.setSKU(goodsHead.getPlatformGoodsCode());

        //最大调度时间-    -取值逻辑-     sc_smc_listing_ebay_shipping_head   -  handling_time
        itemType.setDispatchTimeMax(Integer.valueOf(listingEbayShippingHead.getHandlingTime()));

        //数字标识符-     -取值逻辑-
        itemType.setConditionID(1000);

        //车辆列表的描述性自由文本标题【收费】-   -取值逻辑-
//        itemType.setSellerProvidedTitle("2017 GMC Yukon Denali 5***4 Miles Mineral Metallic");

        return itemType;
    }

    /**
     * 映射站点
     * @param goodHead
     * @return
     */
    private SiteCodeType getSite(GoodsHead goodHead) {
        CategoryInfo category= new CategoryInfo();
        category.setPlatformCategoryId(String.valueOf(goodHead.getCategoryId()));
        List<CategoryInfo> categoryInfoList = categoryInfoService.selectCategoryInfoList(category);
        if(ObjectUtils.isEmpty(categoryInfoList)){
            return SiteCodeType.E_BAY_MOTORS;
        }
        if(categoryInfoList.get(0).getIsDistribution()==0){
            return SiteCodeType.E_BAY_MOTORS;
        }
        if(categoryInfoList.get(0).getIsDistribution()==1){
            return SiteCodeType.US;
        }
        return  null;
    }
    /**
     * 映射库存数量
     * @param goodsHead
     * @return
     */
    private Integer getQuantity(GoodsHead goodsHead) {
        if(PublishType.FIXED.getType().equals((goodsHead.getPublishType()))){
            return ObjectUtils.isNotEmpty(goodsHead.getStockOnSalesQty()) ? goodsHead.getStockOnSalesQty().intValue() : 0;
        }
        if(PublishType.CHINESE.getType().equals((goodsHead.getPublishType()))){
            return 1;
        }
        return 0;
    }

    /**
     * 映射ebay刊登类型
     * @param goodsHead
     * @return
     */
    private ListingTypeCodeType getListingType(GoodsHead goodsHead) {
        //2为多变体 3为固定 4为拍卖   	https://developer.ebay.com/Devzone/XML/docs/Reference/eBay/types/ListingTypeCodeType.html
        switch (goodsHead.getPublishType()){
            case (2):
                return ListingTypeCodeType.CHINESE;
            case (4):
                return ListingTypeCodeType.CHINESE;
            case (3):
            default:
                return ListingTypeCodeType.FIXED_PRICE_ITEM;
        }
    }
    /**
     * 映射刊登日期
     * @param listingEbayLine
     * @return
     */
    private String getListingDuration(GoodsHead goodsHead,ListingEbayLine listingEbayLine) {
        if(PublishType.FIXED.getType().equals((goodsHead.getPublishType()))){
            return "GTC";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("Days_");
        if (PublishType.CHINESE.getType().equals((goodsHead.getPublishType()))&&ObjectUtils.isNotEmpty(listingEbayLine.getSellDay())) {
            sb.append(listingEbayLine.getSellDay());
        }
        return sb.toString();
    }
    /**
     * 组装描述
     *
     * @param goodsDescription
     */
    public String buildDescription(List<GoodsResource> goodsResourcesList, GoodsDescription goodsDescription,List<ListingEbayValue> listingEbayValues,String title) {
        if (ObjectUtils.isEmpty(goodsDescription.getDescriptionId())) {
            return goodsDescription.getDetailDescription();
        }
        //找到对应的模板
        TemplateEbayDescription ebayDescription = templateEbayDescriptionService.selectTemplateEbayDescriptionById(Long.valueOf(goodsDescription.getDescriptionId()));

        //新模板处理逻辑
//        if (Objects.equals(ebayDescription.getIsNew(),"1")) {
            RenderTemplateDescriptionToHtml toHtmlDTO = new RenderTemplateDescriptionToHtml();
            toHtmlDTO.setTemplateId( String.valueOf(goodsDescription.getDescriptionId()) );
            toHtmlDTO.setImageSrcArr( CollUtil.isNotEmpty(goodsResourcesList) ? goodsResourcesList.stream().map(GoodsResource::getResourceUrl).collect(Collectors.toList()) : null );
            toHtmlDTO.setOnlineDesc( goodsDescription.getShortDescription() );
            toHtmlDTO.setAttributeJsonArr( listingEbayValues );
            toHtmlDTO.setTitle( title );
            String html = templateEbayDescriptionBiz.renderTemplateDescriptionToHtml(toHtmlDTO);
            return html;
//        }


        //处理模板的html
//        List<TemplateWidgetDTO> widgetList = JSON.parseArray(ebayDescription.getWidgetConfig(), TemplateWidgetDTO.class);
//        List<TemplateWidget> templateWidgets = templateWidgetService.selectTemplateWidgetByIds(widgetList.stream().map(TemplateWidgetDTO::getId).collect(Collectors.toList()));
//        StringBuilder html = new StringBuilder();
//        StringBuilder mainImgHtml = new StringBuilder();
//        StringBuilder imgHtml = new StringBuilder();
//        //根据sort排序，小的在前面
//        templateWidgets.stream().sorted(Comparator.comparingInt(TemplateWidget::getSort)).forEach(templateWidget -> {
//            widgetList.stream().forEach(templateWidgetDTO -> {
//                if (templateWidget.getId() == (templateWidgetDTO.getId())) {
//                    //处理图片
//                    if (templateWidget.getType().equals("image")) {
//                        //拿到listing的对应图片数据
//                        mainImgHtml.append("<div class=\"relative flex mt-2 image-box widget justify-center\" id=\"template-images\">");
//                        for (GoodsResource goodsResource : goodsResourcesList) {
//                            if (ObjectUtils.isNotEmpty(goodsResource.getResourceUrl())&&goodsResource.getIsMain() == 1) {
//                                mainImgHtml.append("<div class=\"overflow-hidden rounded master\">\n" +
//                                        "  <img  class=\"object-cover\" src=\"" + goodsResource.getResourceUrl() + "\" >\n" +
//                                        "  </div>");
//
//                            }
//                            imgHtml.append("<div class=\"overflow-hidden rounded cursor-pointer img-item\">\n" +
//                                    "  <img class=\"object-cover thumbnail\" src=\"" + goodsResource.getResourceUrl() + "\">\n" +
//                                    "  <div class=\"absolute top-0 left-0 overflow-hidden rounded img-preview\">\n" +
//                                    "  <img  class=\"object-cover\" src=\"" + goodsResource.getResourceUrl() + "\" >\n" +
//                                    "  </div> \n" +
//                                    "  </div>");
//
//
//                        }
//                        mainImgHtml.append("<div class=\"overflow-y-auto slave\">\n" +
//                                "  <div class=\"flex flex-row flex-wrap justify-start gap-2 px-4\">\n" +
//                                imgHtml +
//                                "  </div>\n" +
//                                "</div>" +
//                                "</div>");
//
//                        html.append(mainImgHtml);
//                    }
//                    //描述文案
//                    else if(templateWidget.getType().equals("descriptor")){
//                        templateWidgetDTO.getConfigs().get(0).setDefaultValue(goodsDescription.getShortDescription());
//                        templateWidget.setRole("{configs:" + JSON.toJSONString(templateWidgetDTO.getConfigs()) + "}");
////                        html.append(templateWidgetService.renderWidgetHtml(templateWidget));
//                    }
//                    else  {
//                        templateWidget.setRole("{configs:" + JSON.toJSONString(templateWidgetDTO.getConfigs()) + "}");
////                        html.append(templateWidgetService.renderWidgetHtml(templateWidget));
//                    }
//
//                }
//            });
//        });
//
//        //对应模板的配置
//        String config = ebayDescription.getConfig();
//        TemplateWidgetConfigDTO configObj = JSON.parseObject(config, TemplateWidgetConfigDTO.class);
//        String resp = templateEngine.process("template/widget/build", new Context());
//        String divstyle = "<div style=" + configObj.toStyle() + ">"
//                + resp + html +
//                "</div>";
//        return divstyle;
//        List<TemplateWidgetDTO> widgetList = JSON.parseArray(ebayDescription.getWidgetConfig(), TemplateWidgetDTO.class);
//        List<TemplateWidget> templateWidgets = templateWidgetService.selectTemplateWidgetByIds(widgetList.stream().map(TemplateWidgetDTO::getId).collect(Collectors.toList()));
//        StringBuilder html = new StringBuilder();
//        StringBuilder mainImgHtml = new StringBuilder();
//        StringBuilder imgHtml = new StringBuilder();
//        //根据sort排序，小的在前面
//        templateWidgets.stream().forEach(templateWidget -> {
//            widgetList.stream().forEach(templateWidgetDTO -> {
//                if (templateWidget.getId() == (templateWidgetDTO.getId())) {
//                    //处理图片
//                    if (templateWidget.getType().equals("image")) {
//                        //拿到listing的对应图片数据
//                        mainImgHtml.append("<div class=\"relative flex mt-2 image-box widget justify-center\" id=\"template-images\">");
//                        for (GoodsResource goodsResource : goodsResourcesList) {
//                            if (ObjectUtils.isNotEmpty(goodsResource.getResourceUrl())&&goodsResource.getIsMain() == 1) {
//                                mainImgHtml.append("<div class=\"overflow-hidden rounded master\">\n" +
//                                        "  <img  class=\"object-cover\" src=\"" + goodsResource.getResourceUrl() + "\" >\n" +
//                                        "  </div>");
//
//                            }
//                            imgHtml.append("<div class=\"overflow-hidden rounded cursor-pointer img-item\">\n" +
//                                    "  <img class=\"object-cover thumbnail\" src=\"" + goodsResource.getResourceUrl() + "\">\n" +
//                                    "  <div class=\"absolute top-0 left-0 overflow-hidden rounded img-preview\">\n" +
//                                    "  <img  class=\"object-cover\" src=\"" + goodsResource.getResourceUrl() + "\" >\n" +
//                                    "  </div> \n" +
//                                    "  </div>");
//
//
//                        }
//                        mainImgHtml.append("<div class=\"overflow-y-auto slave\">\n" +
//                                "  <div class=\"flex flex-row flex-wrap justify-start gap-2 px-4\">\n" +
//                                imgHtml +
//                                "  </div>\n" +
//                                "</div>" +
//                                "</div>");
//
//                        html.append(mainImgHtml);
//                    }
//                    //描述文案
//                    else if(templateWidget.getType().equals("descriptor")){
//                        templateWidgetDTO.getConfigs().get(0).setDefaultValue(goodsDescription.getShortDescription());
//                        templateWidget.setRole("{configs:" + JSON.toJSONString(templateWidgetDTO.getConfigs()) + "}");
////                        html.append(templateWidgetService.renderWidgetHtml(templateWidget));
//                    }
//                    else  {
//                        templateWidget.setRole("{configs:" + JSON.toJSONString(templateWidgetDTO.getConfigs()) + "}");
////                        html.append(templateWidgetService.renderWidgetHtml(templateWidget));
//                    }
//
//                }
//            });
//        });
//
//        //对应模板的配置
//        String config = ebayDescription.getConfig();
//        TemplateWidgetConfigDTO configObj = JSON.parseObject(config, TemplateWidgetConfigDTO.class);
//        String resp = templateEngine.process("template/widget/build", new Context());
//        String divstyle = "<div style=" + configObj.toStyle() + ">"
//                + resp + html +
//                "</div>";
//        return divstyle;


    }


    public ItemType setItemTypeObj(ItemType itemType, GoodsHead goodsHead, ListingEbayLine listingEbayLine, ListingEbayPolicy listingEbayPolicy,
                                   List<GoodsResource> goodsResourcesList, List<ListingShippingTypeLine> listingShippingTypeLines,
                                   List<ListingShippingLocationLine> listingShippingLocationLines, List<ListingEbayValue> listingEbayValues, List<ListingEbayAdaptive> ebayAdaptiveList) {

        //立即购买价格              -取值逻辑 - 取根据刊登类型为拍卖时填写
//        itemType.setBuyItNowPrice((goodsHead.getPublishType()== PublishType.CHINESE.getType())?getAmountType(Double.valueOf(listingEbayLine.getReservePrice()), CurrencyCodeType.USD):null);
        itemType.setBuyItNowPrice((goodsHead.getPublishType()== PublishType.CHINESE.getType()) ?
                ( StrUtil.isEmpty(listingEbayLine.getReservePrice())
                        ?  null
                        : getAmountType(Double.valueOf(listingEbayLine.getReservePrice()),CurrencyCodeType.USD) )
                : null );


        //起拍价                   -取值逻辑 - 取头表中的sc_smc_listing_goods_head-   ebay_price
        itemType.setStartPrice(getAmountType(Double.valueOf(goodsHead.getStandardPrice()), CurrencyCodeType.USD));

        //支付方式                  -取值逻辑 - 取sc_smc_template_ebay_policy   - payment_method
        HashMap<String, BuyerPaymentMethodCodeType> buyerPaymentHashMapEnum = new HashMap<>();
        buyerPaymentHashMapEnum.put(listingEbayPolicy.getPaymentPolicy(), BuyerPaymentMethodCodeType.fromValue(listingEbayPolicy.getPaymentPolicy()));
        BuyerPaymentMethodCodeType buyerPaymentMethodCodeType = buyerPaymentHashMapEnum.get(listingEbayPolicy.getPaymentPolicy());
        itemType.setPaymentMethods(new BuyerPaymentMethodCodeType[]{buyerPaymentMethodCodeType});

        //主要分类                  -取值逻辑 - 取sc_smc_platform_category category_id
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));
        itemType.setPrimaryCategory(getCategoryType(platformCategory.getCategoryId(), platformCategory.getCategoryName()));

        //运费对象                  -取值逻辑 - 取sc_smc_listing_ebay_shipping_head表中关联的数据
        itemType.setShippingDetails(getShippingDetailsType(listingShippingTypeLines, listingShippingLocationLines));

        //最佳报价详情-是否开启议价   -取值逻辑 - 取sc_smc_listing_ebay_line-best_offer_flag
        itemType.setBestOfferDetails(getBestOfferDetailsType("1".equals((goodsHead.getPublishType()== PublishType.CHINESE.getType()) ? "0"
                :listingEbayLine.getBestOfferFlag()) ? true : false, 0));

        //照片信息对象               -取值逻辑 - 取资源表中sc_smc_listing_goods_resource的字段
        itemType.setPictureDetails(getPictureDetailsType(goodsResourcesList));

        //适配车型属性数据           -取值逻辑 -  取适配信息表中的数据sc_smc_listing_ebay_adaptive
        String get = HttpUtils.get(GET_COMPATIBILITY_PROPERTIES_URL.replace("{accountCode}", goodsHead.getShopCode()).replace("{categoryTreeId}", EbayCategoryTreeEnum.MOTORS_US.getCode()).replace("{categoryId}", platformCategory.getCategoryId()));
        AjaxResult result = JSONObject.parseObject(get, AjaxResult.class);
        EbayCompatibilityProperties ebayCompatibilityProperties = JSONObject.parseObject(JSON.toJSONString(result.get(AjaxResult.DATA_TAG)), EbayCompatibilityProperties.class);
        itemType.setItemCompatibilityList(getItemCompatibilityListType(ebayCompatibilityProperties, ebayAdaptiveList));

        //物品规格属性               -取值逻辑 -  取属性表中的数据sc_smc_listing_ebay_value
        itemType.setItemSpecifics(getNameValueListArrayType(listingEbayValues));
        //特定的产品列表详情  品牌 以及upc mpn等
        itemType.setProductListingDetails(getProductListingDetailsType(listingEbayValues));

        //退货政策                  -取值逻辑 -  取付款&退货表中数据sc_smc_listing_ebay_policy
        itemType.setReturnPolicy(getReturnPolicyType(listingEbayPolicy));
//        itemType.setReturnPolicy(getReturnPolicyType());
        //付款政策
        BuyerPaymentMethodCodeType[] nameValueListTypes = {BuyerPaymentMethodCodeType.PERSONAL_CHECK};
        itemType.setPaymentMethods(nameValueListTypes);
//        PaymentDetailsType paymentDetailsType = new PaymentDetailsType();
//        paymentDetailsType.setDepositAmount(getAmountType(111d,CurrencyCodeType.USD));
//        itemType.setPaymentDetails(paymentDetailsType);


        //卖方配置                  -取值逻辑 -  取付款&退货表中数据sc_smc_listing_ebay_policy
//        itemType.setSellerProfiles(getSellerProfilesType());

        return itemType;
    }

    /**
     * @description: TODO ebay刊登暂时不需要的字段
     * <AUTHOR>
     * @date 2023/2/1 16:43
     * @version 1.0
     */
    public ItemType setItemTypeOther(ItemType itemType) {
        itemType.setApplicationData("");
        itemType.setAutoPay(false);
        itemType.setBuyerProtection(BuyerProtectionCodeType.ITEM_INELIGIBLE);
        itemType.setCategoryMappingAllowed(false);
        itemType.setCharity(new CharityType());
        itemType.setCrossPromotion(new CrossPromotionsType());
        itemType.setDescriptionReviseMode(DescriptionReviseModeCodeType.REPLACE);
        itemType.setDistance(new DistanceType());
        itemType.setHitCounter(HitCounterCodeType.NO_HIT_COUNTER);
        itemType.setItemID("");
        itemType.setListingDetails(new ListingDetailsType());
        itemType.setListingDesigner(new ListingDesignerType());
//        itemType.setListingEnhancement(new ListingEnhancementsCodeType[]());
        itemType.setListingEnhancement(0, ListingEnhancementsCodeType.BORDER);
        itemType.setLotSize(0);
        itemType.setPartnerCode("");
        itemType.setPartnerName("");
        itemType.setPaymentDetails(new PaymentDetailsType());
        itemType.setPayPalEmailAddress("");
        itemType.setProductListingDetails(new ProductListingDetailsType());
        itemType.setPrivateNotes("");
        itemType.setRegionID("");
        itemType.setRelistLink(false);
        itemType.setReservePrice(new AmountType());
        itemType.setReviseStatus(new ReviseStatusType());
//        itemType.setScheduleTime(new Calendar());
        itemType.setSecondaryCategory(new CategoryType());
        itemType.setFreeAddedCategory(new CategoryType());
        itemType.setSeller(new UserType());
        itemType.setSellingStatus(new SellingStatusType());
        itemType.setUUID("");
        itemType.setVATDetails(new VATDetailsType());
        itemType.setSellerVacationNote("");
        itemType.setWatchCount(0L);
        itemType.setHitCount(0L);
        itemType.setDisableBuyerRequirements(false);
        itemType.setLocationDefaulted(false);
        itemType.setUseTaxTable(false);
        itemType.setGetItFast(false);
        itemType.setBuyerResponsibleForShipping(false);
        itemType.setLimitedWarrantyEligible(false);
        itemType.setEBayNotes("");
        itemType.setQuestionCount(0L);
        itemType.setRelisted(false);
        itemType.setQuantityAvailable(0);
        itemType.setSearchDetails(new SearchDetailsType());
        itemType.setBestOfferEnabled(false);
        itemType.setLocalListing(false);
        itemType.setSellerContactDetails(new AddressType());
        itemType.setTotalQuestionCount(0L);
        itemType.setProxyItem(false);
        itemType.setExtendedSellerContactDetails(new ExtendedContactDetailsType());
        itemType.setLeadCount(0);
        itemType.setNewLeadCount(0);
        itemType.setGroupCategoryID("");
        itemType.setClassifiedAdPayPerLeadFee(new AmountType());
        itemType.setBidGroupItem(false);
        itemType.setApplyBuyerProtection(new BuyerProtectionDetailsType());
        itemType.setListingSubtype2(ListingSubtypeCodeType.CLASSIFIED_AD);
        itemType.setMechanicalCheckAccepted(false);
        itemType.setUpdateSellerInfo(false);
        itemType.setUpdateReturnPolicy(false);
        itemType.setItemPolicyViolation(new ItemPolicyViolationType());
//        itemType.setCrossBorderTrade(new String[]());
        itemType.setCrossBorderTrade(0, "");
        itemType.setBusinessSellerDetails(new BusinessSellerDetailsType());
        itemType.setBuyerGuaranteePrice(new AmountType());
        itemType.setBuyerRequirementDetails(new BuyerRequirementDetailsType());
//        itemType.setPaymentAllowedSite(new SiteCodeType[]());
        itemType.setPaymentAllowedSite(0, SiteCodeType.US);
        itemType.setInventoryTrackingMethod(InventoryTrackingMethodCodeType.ITEM_ID);
        itemType.setIntegratedMerchantCreditCardEnabled(false);
        itemType.setVariations(new VariationsType());
        itemType.setItemCompatibilityCount(0);
        itemType.setConditionDescription("");
        itemType.setConditionDisplayName("");
        itemType.setTaxCategory("");
        itemType.setQuantityAvailableHint(QuantityAvailableHintCodeType.LIMITED);
        itemType.setQuantityThreshold(0);
        itemType.setDiscountPriceInfo(new DiscountPriceInfoType());
        itemType.setVINLink("");
        itemType.setVRM("");
        itemType.setVRMLink("");
        itemType.setQuantityInfo(new QuantityInfoType());
        itemType.setShippingServiceCostOverrideList(new ShippingServiceCostOverrideListType());
        itemType.setShippingOverride(new ShippingOverrideType());
        itemType.setShippingPackageDetails(new ShipPackageDetailsType());
        itemType.setTopRatedListing(false);
        itemType.setQuantityRestrictionPerBuyer(new QuantityRestrictionPerBuyerInfoType());
        itemType.setFloorPrice(new AmountType());
        itemType.setCeilingPrice(new AmountType());
        itemType.setIsIntermediatedShippingEligible(false);
        itemType.setUnitInfo(new UnitInfoType());
        itemType.setRelistParentID(0L);
        itemType.setConditionDefinition("");
        itemType.setHideFromSearch(false);
        itemType.setReasonHideFromSearch(ReasonHideFromSearchCodeType.DUPLICATE_LISTING);
        itemType.setIncludeRecommendations(false);
        itemType.setPickupInStoreDetails(new PickupInStoreDetailsType());
        itemType.setSiteId(0);
        itemType.setIgnoreQuantity(false);
        itemType.setAvailableForPickupDropOff(false);
        itemType.setEligibleForPickupDropOff(false);
        itemType.setLiveAuction(false);
        itemType.setDigitalGoodInfo(new DigitalGoodInfoType());
        itemType.setEBayPlus(false);
        itemType.setEBayPlusEligible(false);
        itemType.setEMailDeliveryAvailable(false);
        itemType.setIsSecureDescription(false);
//        itemType.setAny(new Object[]());
        itemType.setAny(0, new Object());

        //第二次筛选出来的额外数据-------------------小黑屋-------start
        //车辆识别号--取值逻辑-
        itemType.setVIN("1214521580");
        //卖方排除船舶到地点优先
        itemType.setShipToLocations(new String[]{"US"});

//        //铺面信息
//        itemType.setStorefront(new StorefrontType());
//        StorefrontType storefrontType = new StorefrontType();
//        storefrontType.setStoreCategoryID(36891858016L);
//        storefrontType.setStoreCategory2ID(0L);
//        storefrontType.setStoreURL("https://stores.ebay.com.hk/headlightsassembly");
//        itemType.setStorefront(storefrontType);
        //第二次筛选出来的额外数据-------------------小黑屋-------end
        return itemType;
    }


    /**
     * @description: TODO 封装卖家卖方配置对象的方法
     * @param: []
     * @return: com.ebay.soap.eBLBaseComponents.SellerProfilesType
     * <AUTHOR>
     * @date: 2023/2/1 17:11
     */
    private static SellerProfilesType getSellerProfilesType() {
        SellerProfilesType sellerProfilesType = new SellerProfilesType();

        SellerShippingProfileType sellerShippingProfileType = new SellerShippingProfileType();
        sellerShippingProfileType.setShippingProfileID(230713653019L);
        sellerShippingProfileType.setShippingProfileName("Flat:Standard Shipp(Free),UPS Ground,1 busine");

        SellerReturnProfileType sellerReturnProfileType = new SellerReturnProfileType();
        sellerReturnProfileType.setReturnProfileID(232578164019L);
        sellerReturnProfileType.setReturnProfileName("Returns Accepted,Seller,30 Days,Money Back,In");

        SellerPaymentProfileType sellerPaymentProfileType = new SellerPaymentProfileType();
        sellerPaymentProfileType.setPaymentProfileID(230024117019L);
        sellerPaymentProfileType.setPaymentProfileName("eBay Payments:Immediate pay");

        sellerProfilesType.setSellerShippingProfile(sellerShippingProfileType);
        sellerProfilesType.setSellerReturnProfile(sellerReturnProfileType);
        sellerProfilesType.setSellerPaymentProfile(sellerPaymentProfileType);
        return sellerProfilesType;
    }

    /**
     * @param listingEbayPolicy
     * @description: TODO  封退货政策的方法
     * @param: []
     * @return: com.ebay.soap.eBLBaseComponents.ReturnPolicyType
     * <AUTHOR>
     * @date: 2023/2/1 17:09
     */
    private static ReturnPolicyType getReturnPolicyType(ListingEbayPolicy listingEbayPolicy) {
        ReturnPolicyType returnPolicyType = new ReturnPolicyType();
//        returnPolicyType.setDescription(listingEbayPolicy.getReturnsDescription());

        //指定买家退货的时间量 domestic_return_day
        //适用值为或。设置为 时，此选项表示卖家允许退回商品。如果不接受退货，请为商品指定退货不接受 domestic_return_flag
        returnPolicyType.setReturnsAcceptedOption("1".equals(listingEbayPolicy.getDomesticReturnFlag()) ? "ReturnsAccepted" : "ReturnsNotAccepted");
//        returnPolicyType.setReturnsAccepted("1".equals(listingEbayPolicy.getDomesticReturnFlag()) ? "Returns Accepted" : "Returns Not Accepted");
        if("1".equals(listingEbayPolicy.getDomesticReturnFlag())) {
            returnPolicyType.setReturnsWithinOption("Days_" + listingEbayPolicy.getDomesticReturnDay());
            returnPolicyType.setReturnsWithin(listingEbayPolicy.getDomesticReturnDay() + " Days");
        }
        //指定卖家支付运费还是买家支付运费          domestic_return_bearer
        returnPolicyType.setInternationalReturnsAcceptedOption("1".equals(listingEbayPolicy.getInternationalReturnFlag()) ? "ReturnsAccepted" : "ReturnsNotAccepted");
       if("1".equals(listingEbayPolicy.getInternationalReturnFlag())) {
           returnPolicyType.setShippingCostPaidByOption(listingEbayPolicy.getDomesticReturnBearer());
           returnPolicyType.setShippingCostPaidBy(listingEbayPolicy.getInternationalReturnBearer());
           returnPolicyType.setInternationalReturnsWithinOption("Days_" + listingEbayPolicy.getInternationalReturnDay());
           returnPolicyType.setInternationalShippingCostPaidByOption(listingEbayPolicy.getInternationalReturnBearer());
       }
        return returnPolicyType;
    }
    /**
     * 产品列表详情指定
     * @param listingEbayValues
     *
     * <ProductListingDetails>
     *      <UPC>997750800267</UPC>
     *      <BrandMPN>
     *       <Brand>KAC</Brand>
     *       <MPN>LNM800267</MPN>
     *      </BrandMPN>
     *      <IncludeeBayProductDetails>true</IncludeeBayProductDetails>
     *     </ProductListingDetails>
     * @return
     */
    private ProductListingDetailsType getProductListingDetailsType(List<ListingEbayValue> listingEbayValues) {
        String upc = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("UPC")).findFirst().get().getValue();
        String brand = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("Brand")).findFirst().get().getValue();
        String mpn = listingEbayValues.stream().filter(listingEbayValue -> listingEbayValue.getName().contains("Manufacturer Part Number")).findFirst().get().getValue();
        ProductListingDetailsType productListingDetailsType = new ProductListingDetailsType();
        productListingDetailsType.setUPC(upc);
        BrandMPNType brandMPNType = new BrandMPNType();
        brandMPNType.setBrand(brand);
        brandMPNType.setMPN(mpn);
        productListingDetailsType.setBrandMPN(brandMPNType);
        productListingDetailsType.setIncludeeBayProductDetails(true);
        return productListingDetailsType;
    }
    /**
     * @param listingEbayValues
     * @description: TODO 封装配置属性对象的方法
     * @param: []
     * @return: com.ebay.soap.eBLBaseComponents.NameValueListArrayType
     * <AUTHOR>
     * @date: 2023/2/1 17:07
     */
    private static NameValueListArrayType getNameValueListArrayType(List<ListingEbayValue> listingEbayValues) {
        NameValueListArrayType nameValueListArrayType = new NameValueListArrayType();

        NameValueListType[] nameValueListTypes = new NameValueListType[listingEbayValues.size()];

//        NameValueListType nameValueListType1 = new NameValueListType();
//
//        nameValueListType1.setName("Brand");
//        String[] value = {"KAC"};
//        nameValueListType1.setValue(value);

        for (int i = 0; i < listingEbayValues.size(); i++) {
            NameValueListType nameValueListType1 = new NameValueListType();
            nameValueListType1.setName(listingEbayValues.get(i).getName());
            nameValueListType1.setValue(new String[]{listingEbayValues.get(i).getValue()});
            nameValueListTypes[i] = nameValueListType1;
        }

//
//        NameValueListType nameValueListType2 = new NameValueListType();
//        nameValueListType2.setName("Placement on Vehicle");
//        String[] value2 = {"Front"};
//        nameValueListType2.setValue(value2);
//
//        NameValueListType nameValueListType3 = new NameValueListType();
//        nameValueListType3.setName("Manufacturer Warranty");
//        String[] value3 = {"2 Years"};
//        nameValueListType3.setValue(value3);
//
//        NameValueListType nameValueListType4 = new NameValueListType();
//        nameValueListType4.setName("Material");
//        String[] value4 = {"Stainless Steel"};
//        nameValueListType4.setValue(value4);
//
//        NameValueListType nameValueListType5 = new NameValueListType();
//        nameValueListType5.setName("Primary Tube Size");
//        String[] value5 = {"1.75\""};
//        nameValueListType5.setValue(value5);
//
//        NameValueListType nameValueListType6 = new NameValueListType();
//        nameValueListType6.setName("Outlet Flange Thickness");
//        String[] value6 = {"3/8\""};
//        nameValueListType6.setValue(value6);
//
//        NameValueListType nameValueListType7 = new NameValueListType();
//        nameValueListType7.setName("UPC");
//        String[] value7 = {"635861332634"};
//        nameValueListType7.setValue(value7);
//
//        NameValueListType nameValueListType8 = new NameValueListType();
//        nameValueListType8.setName("Manufacturer Part Number");
//        String[] value8 = {"21945704"};
//        nameValueListType8.setValue(value8);
//
//        NameValueListType nameValueListType9 = new NameValueListType();
//        nameValueListType9.setName("Surface Finish");
//        String[] value9 = {"Chrome Polished Stainless Steel"};
//        nameValueListType9.setValue(value9);
//
//        NameValueListType nameValueListType10 = new NameValueListType();
//        nameValueListType10.setName("Fitment Type");
//        String[] value10 = {"Direct Replacement"};
//        nameValueListType10.setValue(value10);
//
//        NameValueListType nameValueListType11 = new NameValueListType();
//        nameValueListType11.setName("Outlet Size");
//        String[] value11 = {"2.25\""};
//        nameValueListType11.setValue(value11);
//
//        NameValueListType nameValueListType12 = new NameValueListType();
//        nameValueListType12.setName("Head Flange Thickness");
//        String[] value12 = {"7/16\""};
//        nameValueListType12.setValue(value12);
//
//        NameValueListType nameValueListType13 = new NameValueListType();
//        nameValueListType13.setName("Type");
//        String[] value13 = {"Exhaust Manifold Headers"};
//        nameValueListType13.setValue(value13);

//        NameValueListType[] nameValueListTypes = {nameValueListType1,nameValueListType2,nameValueListType3,nameValueListType4,nameValueListType5,nameValueListType6,nameValueListType7,nameValueListType8,nameValueListType9,nameValueListType10,nameValueListType11,nameValueListType12,nameValueListType13};
//        NameValueListType[] nameValueListTypes = {nameValueListType1};

        nameValueListArrayType.setNameValueList(nameValueListTypes);
        return nameValueListArrayType;
    }


    /**
     * @description: TODO 封装适配车型对象的方法
     * @param: []
     * @return: com.ebay.soap.eBLBaseComponents.ItemCompatibilityListType
     * <AUTHOR>
     * @date: 2023/2/1 17:06
     */
    public static ItemCompatibilityListType getItemCompatibilityListType(EbayCompatibilityProperties ebayCompatibilityProperties, List<ListingEbayAdaptive> ebayAdaptiveList) {
        ItemCompatibilityListType itemCompatibilityListType = new ItemCompatibilityListType();
        List<ItemCompatibilityType> itemCompatibilityTypeList = new ArrayList<ItemCompatibilityType>();
        if (CollectionUtils.isEmpty(ebayAdaptiveList)) {
            return itemCompatibilityListType;
        }
        for (ListingEbayAdaptive e : ebayAdaptiveList) {
            ItemCompatibilityType itemCompatibilityType = new ItemCompatibilityType();
            ArrayList<NameValueListType> typeArrayList = new ArrayList<>();
            //从api获取该类目适配属性
            if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties) && ObjectUtils.isNotEmpty(ebayCompatibilityProperties.getCompatibilityProperties())) {
                for (EbayCompatibilityProperties.CompatibilityPropertiesBean compatibilityProperty : ebayCompatibilityProperties.getCompatibilityProperties()) {
                    if (compatibilityProperty.getName().equals("Year")) {
                        NameValueListType nameValueListType1 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getYear()});
                        typeArrayList.add(nameValueListType1);
                    }
                    if (compatibilityProperty.getName().equals("Make")) {
                        NameValueListType nameValueListType2 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getMake()});
                        typeArrayList.add(nameValueListType2);
                    }
                    if (compatibilityProperty.getName().equals("Model")) {
                        NameValueListType nameValueListType3 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getModel()});
                        typeArrayList.add(nameValueListType3);
                    }
//                    if (compatibilityProperty.getName().equals("Submodel")) {
//                        NameValueListType nameValueListType4 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getSubmodel()});
//                        typeArrayList.add(nameValueListType4);
//                    }
                    if (compatibilityProperty.getName().equals("Engine")) {
                        NameValueListType nameValueListType5 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getEngine()});
                        typeArrayList.add(nameValueListType5);
                    }
                    if (compatibilityProperty.getName().equals("Trim")) {
                        NameValueListType nameValueListType6 = setNameValueListType(compatibilityProperty.getName(), new String[]{e.getTrim()});
                        typeArrayList.add(nameValueListType6);
                    }

                }
            }

//            NameValueListType nameValueListType1 = setNameValueListType("Year", new String[]{e.getYear()});
//            NameValueListType nameValueListType2 = setNameValueListType("Make", new String[]{e.getMake()});
//            NameValueListType nameValueListType3 = setNameValueListType("Model", new String[]{e.getModel()});
//            typeArrayList.add(nameValueListType1);
//            typeArrayList.add(nameValueListType2);
//            typeArrayList.add(nameValueListType3);
//            if (ObjectUtils.isNotEmpty(e.getSubmodel())) {
//                NameValueListType nameValueListType4 = setNameValueListType("Submodel", new String[]{e.getSubmodel()});
//                typeArrayList.add(nameValueListType4);
//            } else {
//                NameValueListType nameValueListType5 = setNameValueListType("Engine", new String[]{e.getEngine()});
//                NameValueListType nameValueListType6 = setNameValueListType("Trim", new String[]{e.getTrim()});
//                typeArrayList.add(nameValueListType5);
//                typeArrayList.add(nameValueListType6);
//            }
            NameValueListType[] nameValueListTypes = typeArrayList.toArray(new NameValueListType[typeArrayList.size()]);
            itemCompatibilityType.setNameValueList(nameValueListTypes);
            itemCompatibilityTypeList.add(itemCompatibilityType);
        }
        ItemCompatibilityType[] itemCompatibilityTypearr = itemCompatibilityTypeList.toArray(new ItemCompatibilityType[itemCompatibilityTypeList.size()]);
        itemCompatibilityListType.setCompatibility(itemCompatibilityTypearr);
        return itemCompatibilityListType;
    }


    /**
     * @description: TODO 封装适配属性对象中组装对象的方法
     * @param: [name, value]
     * @return: com.ebay.soap.eBLBaseComponents.NameValueListType
     * <AUTHOR>
     * @date: 2023/2/1 17:09
     */
    private static NameValueListType setNameValueListType(String name, String[] value) {
        NameValueListType nameValueListType = new NameValueListType();
        nameValueListType.setName(name);
        nameValueListType.setValue(value);
        return nameValueListType;
    }

    /**
     * @param goodsResourcesList
     * @description: TODO 封装配置图片对象的方法
     * @param: []
     * @return: com.ebay.soap.eBLBaseComponents.PictureDetailsType
     * <AUTHOR>
     * @date: 2023/2/1 17:05
     */
    private static PictureDetailsType getPictureDetailsType(List<GoodsResource> goodsResourcesList) {
        PictureDetailsType pictureDetailsType = new PictureDetailsType();
        String[] paths = {"https://i.ebayimg.com/00/s/MTYwMFgxNjAw/z/vW0AAOSweNVjkZlz/$_57.JPG?set_id=880000500F",
                "https://i.ebayimg.com/00/s/MTYwMFgxNjAw/z/niAAAOSwEkljkZlg/$_57.JPG?set_id=880000500F",
                "https://i.ebayimg.com/00/s/MTYwMFgxNjAw/z/s2sAAOSw1PFjkZlv/$_57.JPG?set_id=880000500F"};

        List<String> pictureUrlList = new ArrayList<>();
        Map<String, List<GoodsResource>> resource = goodsResourcesList.stream().filter(p -> ObjectUtils.isNotEmpty(p.getResourceType())).collect(Collectors.groupingBy(GoodsResource::getResourceType));
        if (resource.size() != 0) {
            //视频数据
            List<GoodsResource> videoResource = resource.get("0");
            //图片数据
            List<GoodsResource> imgResource = resource.get("1");
            if (ObjectUtils.isNotEmpty(videoResource) && ObjectUtils.isNotEmpty(imgResource)) {
                if (imgResource.size() != 0 && videoResource.size() != 0) {
                    pictureUrlList = imgResource.stream().collect(Collectors.groupingBy(GoodsResource::getIsMain)).get(0)
                            .stream().map(GoodsResource::getResourceUrl).collect(Collectors.toList());
                }
            }
        }

        List<String> arrayList = new ArrayList(new HashSet(pictureUrlList));
        pictureDetailsType.setPictureURL(arrayList.toArray(new String[pictureUrlList.size()]));
        pictureDetailsType.setGalleryType(GalleryTypeCodeType.GALLERY);
        pictureDetailsType.setPhotoDisplay(PhotoDisplayCodeType.PICTURE_PACK);
        pictureDetailsType.setPictureSource(PictureSourceCodeType.EPS);
        return pictureDetailsType;
    }

    /**
     * @description: TODO 封装是否开启议价对象的方法
     * @param: [bestOfferEnabled, count]
     * @return: com.ebay.soap.eBLBaseComponents.BestOfferDetailsType
     * <AUTHOR>
     * @date: 2023/2/1 17:03
     */
    private static BestOfferDetailsType getBestOfferDetailsType(Boolean bestOfferEnabled, Integer count) {
        BestOfferDetailsType bestOfferDetailsType = new BestOfferDetailsType();
        bestOfferDetailsType.setBestOfferEnabled(bestOfferEnabled);
        bestOfferDetailsType.setBestOfferCount(count);
        return bestOfferDetailsType;
    }

    /**
     * @description: TODO 封装金额对象的方法
     * @param: [value, currencyCodeType]
     * @return: com.ebay.soap.eBLBaseComponents.AmountType
     * <AUTHOR>
     * @date: 2022/12/8 17:52
     */

    private static AmountType getAmountType(double value, CurrencyCodeType currencyCodeType) {
        AmountType amountType = new AmountType();
        amountType.setValue(value);
        if (ObjectUtil.isNotEmpty(currencyCodeType)) {
            amountType.setCurrencyID(currencyCodeType);
        }
        return amountType;
    }

    /**
     * @description: TODO 封装类别对象的方法
     * @param: [categoryID, categoryName]
     * @return: com.ebay.soap.eBLBaseComponents.CategoryType
     * <AUTHOR>
     * @date: 2023/2/1 16:53
     */
    private static CategoryType getCategoryType(String categoryID, String categoryName) {
        CategoryType categoryType = new CategoryType();
        categoryType.setCategoryID(categoryID);
        categoryType.setCategoryName(categoryName);
        return categoryType;
    }

    /**
     * @param listingEbayShippingHead
     * @param listingShippingLocationLines
     * @description: TODO 封装运输对象的方法
     * @param: []
     * @return: com.ebay.soap.eBLBaseComponents.ShippingDetailsType
     * <AUTHOR>
     * @date: 2023/2/1 16:55
     */
    private static ShippingDetailsType getShippingDetailsType(List<ListingShippingTypeLine> listingEbayShippingHead, List<ListingShippingLocationLine> listingShippingLocationLines) {
        ShippingDetailsType shippingDetailsType = new ShippingDetailsType();
        List<ShippingServiceOptionsType> shippingServiceOptionsType = new ArrayList<>();
        Stream.iterate(1, i -> i + 1).limit(listingEbayShippingHead.size()).forEach(index -> {
            ListingShippingTypeLine p = listingEbayShippingHead.get(index - 1);
            shippingServiceOptionsType.add(
                    getShippingServiceOptionsType(p.getShippingService(),
                            getAmountType(Double.valueOf(String.valueOf(ObjectUtils.isNotEmpty(p.getShippingCost()) ? p.getShippingCost() : BigDecimal.ONE)), CurrencyCodeType.USD), index,
                            Boolean.FALSE,
                            Integer.valueOf(p.getShippingTimeMin()),
                            Integer.valueOf(p.getShippingTimeMax()),
                            "1".equals(p.getFreeShippingFlag()) ? Boolean.TRUE : Boolean.FALSE));
        });
//        ShippingServiceOptionsType us_standardSppedPAK1 = getShippingServiceOptionsType("ShippingMethodStandard", getAmountType(0.0d, CurrencyCodeType.USD), 1, Boolean.FALSE, 1, 5, Boolean.FALSE);
//        ShippingServiceOptionsType us_standardSppedPAK2 = getShippingServiceOptionsType("UPSGround", getAmountType(0.0d, CurrencyCodeType.USD), 2, Boolean.FALSE, 1, 5, Boolean.FALSE);

//        shippingServiceOptionsTypes = ArrayUtil.newArray(ShippingServiceOptionsType.class, shippingServiceOptionsType.size());

        ShippingServiceOptionsType[] shippingServiceOptionsTypes = new ShippingServiceOptionsType[shippingServiceOptionsType.size()];
        Stream.iterate(1, i -> i + 1).limit(shippingServiceOptionsType.size()).forEach(index -> {
            shippingServiceOptionsTypes[index - 1] = shippingServiceOptionsType.get(index - 1);
        });

        List<String> excldeLocations = new ArrayList<>();
        listingShippingLocationLines.stream().forEach(lo -> {
            excldeLocations.add(lo.getLocation());
        });
        List<String> arrayList = new ArrayList(new HashSet(excldeLocations));
        String[] excludeShipToLocation = arrayList.toArray(new String[arrayList.size()]);
//        String[] excludeShipToLocation = {"Alaska/Hawaii", "APO/FPO", "US Protectorates", "Africa", "Asia", "Central America and Caribbean", "Europe", "Middle East", "North America", "Oceania", "Southeast Asia", "South America", "PO Box"};

        //配置物流服务方式
        shippingDetailsType.setShippingServiceOptions(shippingServiceOptionsTypes);
        //物流方式   sc_smc_listing_shipping_type_line    shipping_type 暂时写死
        shippingDetailsType.setShippingType(ShippingTypeCodeType.FLAT);
        //排除地区国家
        shippingDetailsType.setExcludeShipToLocation(excludeShipToLocation);
        // 刊登时用不到
//        SalesTaxType salesTaxType = new SalesTaxType();
//        AmountType amountType3 = getAmountType(0.0d, null);
//        salesTaxType.setShippingIncludedInTax(Boolean.FALSE);
//        salesTaxType.setSalesTaxAmount(amountType3);
//        shippingDetailsType.setSalesTax(salesTaxType);
//        // 输入时，这是为国内配送服务提供的运费折扣的 ID（其中运费折扣类型为“固定运费折扣”或“计算运费折扣”类型）。
//        shippingDetailsType.setShippingDiscountProfileID("0");
//        //输入时，这是为国际运输服务提供的运输折扣的 ID（其中运输折扣类型为“固定运输折扣”或“计算运输折扣”类型）。
//        shippingDetailsType.setInternationalShippingDiscountProfileID("0");
        //新增时不用此字段
//        shippingDetailsType.setSellerExcludeShipToLocationsPreference(Boolean.TRUE);
        return shippingDetailsType;
    }

    /**
     * @description: TODO 封装运输对象中的【运输服务】对象选项方法
     * @param: [shippingService, shippingServiceCost, shippingServicePriority, expeditedService, shippingTimeMin, shippingTimeMax, freeShipping]
     * @return: com.ebay.soap.eBLBaseComponents.ShippingServiceOptionsType
     * <AUTHOR>
     * @date: 2023/2/1 16:56
     */
    private static ShippingServiceOptionsType getShippingServiceOptionsType(String shippingService, AmountType shippingServiceCost,
                                                                            Integer shippingServicePriority, Boolean expeditedService,
                                                                            Integer shippingTimeMin, Integer shippingTimeMax,
                                                                            Boolean freeShipping) {
        ShippingServiceOptionsType shippingServiceOptionsType = new ShippingServiceOptionsType();
        //此枚举值表示卖方提供的特定国内运输服务选项，用于将项目运送到与该项目位于同一国家的买方
        shippingServiceOptionsType.setShippingService(shippingService);
        //费用
        shippingServiceOptionsType.setShippingServiceCost(shippingServiceCost);
        //控制订单(相对于其他运输服务)，其中对应的ShippingService将出现在“查看项目”和“结付”页中。
        shippingServiceOptionsType.setShippingServicePriority(shippingServicePriority);
        //是否加急订单，默认false
        shippingServiceOptionsType.setExpeditedService(expeditedService);
        //
        shippingServiceOptionsType.setShippingTimeMin(shippingTimeMin);
        //
        shippingServiceOptionsType.setShippingTimeMax(shippingTimeMax);
        //是否免费
        shippingServiceOptionsType.setFreeShipping(freeShipping);

        shippingServiceOptionsType.setShippingServiceCost(getAmountType(0d, CurrencyCodeType.USD));
        shippingServiceOptionsType.setShippingServiceAdditionalCost(getAmountType(0d,CurrencyCodeType.USD));
        return shippingServiceOptionsType;
    }

    @Autowired
    EbayVideoTask ebayVideoTask;
    @Test
    public  void testEbayVideoTask(){
        ebayVideoTask.ebayVideoTask();
    }







}


class EbayItemVO implements Serializable {
    private static final long serialVersionUID = 12343L;
    protected ItemType itemType;

    public ItemType getItemType() {
        return itemType;
    }

    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }
}

