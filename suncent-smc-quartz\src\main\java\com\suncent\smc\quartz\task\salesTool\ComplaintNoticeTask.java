package com.suncent.smc.quartz.task.salesTool;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.suncent.smc.common.config.RuoYiConfig;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.file.FileUtils;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.salesTool.dto.ComplaintCollectDTO;
import com.suncent.smc.persistence.salesTool.dto.ComplaintRecordExcelDTO;
import com.suncent.smc.persistence.salesTool.service.IComplaintRecordService;
import com.suncent.smc.provider.dingding.domain.ActionCardMsgDto;
import com.suncent.smc.provider.dingding.service.IDingAsyncSendService;
import com.suncent.smc.system.service.ISysUserService;
import com.suncent.smc.system.vo.OperationManagerVO;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class ComplaintNoticeTask {
    @Autowired
    private IComplaintRecordService complaintRecordService;

    @Autowired
    private ISysUserService sysUserService;

    @Autowired
    private IDingAsyncSendService dingAsyncSendService;

    @Autowired
    private IAliOssService aliOssService;

    @Value("${aliyun.oss.defaultBucketName}")
    private String bucketName;

    @Value("${aliyun.oss.suncentUrlPrefix}")
    private String aliOssAddress;

    @XxlJob("complaintNoticeTask")
    public void complaintNoticeTask() {
        log.info("投诉定时通知任务开始执行");

        try {
            List<ComplaintRecordExcelDTO> complaintList = complaintRecordService.selectComplaintAlarms();
            if (CollUtil.isEmpty(complaintList)) {
                log.info("没有符合条件的投诉记录");
                return;
            }
            // 按创建人分组
            Map<String, List<ComplaintRecordExcelDTO>> creatorRecordsMap = complaintList.stream()
                    .filter(record -> StrUtil.isNotBlank(record.getCreateBy()))
                    .collect(Collectors.groupingBy(ComplaintRecordExcelDTO::getCreateBy));

            if (creatorRecordsMap.isEmpty()) {
                log.error("没有需要通知的创建人");
                return;
            }

            // 按部门人分组， 先构建 deptId -> List<ComplaintRecordExcelDTO> 映射
            Map<Long, List<ComplaintRecordExcelDTO>> deptRecordsMap = new HashMap<>();

            for (ComplaintRecordExcelDTO record : complaintList) {
                if (StrUtil.isBlank(record.getCreateBy())) {
                    continue;
                }
                SysUser user = sysUserService.selectUserById(Convert.toLong(record.getCreateBy()));
                if (user == null || user.getDeptId() == null) {
                    continue;
                }
                Long deptId = user.getDeptId();
                deptRecordsMap.computeIfAbsent(deptId, k -> new ArrayList<>()).add(record);
            }

            // 2. 遍历每个部门
            for (Map.Entry<Long, List<ComplaintRecordExcelDTO>> entry : deptRecordsMap.entrySet()) {
                Long deptId = entry.getKey();
                List<ComplaintRecordExcelDTO> deptRecords = entry.getValue();

                // 查询部门负责人
                OperationManagerVO leaderUser = sysUserService.selectLeaderByDeptId(String.valueOf(deptId));
                if (leaderUser == null || StrUtil.isBlank(leaderUser.getUserCode())) {
                    log.error("部门 {} 无负责人或未配置 userCode", deptId);
                    continue;
                }

                List<String> userIdList = deptRecords.stream()
                        .map(ComplaintRecordExcelDTO::getCreateBy)
                        .filter(StrUtil::isNotBlank)
                        .filter(Objects::nonNull)
                        .distinct()
                        .collect(Collectors.toList());
                // 获取该部门的所有创建人 ID（用于统计）
                if (CollUtil.isEmpty(userIdList)) {
                    log.error("部门 {} {}下无有效创建人",deptRecords, deptId);
                    continue;
                }
                ComplaintCollectDTO totalCollect = complaintRecordService.selectCollectComplaintByUserIds(userIdList);
                int totalSuccess = totalCollect.getSuccessVariantViolationCount()
                        + totalCollect.getSuccessRestrictedProductCount()
                        + totalCollect.getSuccessDetailPageViolationCount()
                        + totalCollect.getSuccessNodeViolationCount();
                int totalFail = totalCollect.getFailSendCount() + totalCollect.getThreeFailCount();

                String collectMessage = String.format(
                        "【部门投诉记录汇总】\n时间：前24小时\n" +
                                "总计投诉任务成功 %d 条（变体违规 %d 条，受限商品 %d 条，详情页面违规 %d 条，节点违规 %d 条）\n" +
                                "总计投诉任务失败 %d 条（任务没发出 %d 条，投诉三次不成功 %d 条）",
                        totalSuccess,
                        totalCollect.getSuccessVariantViolationCount(),
                        totalCollect.getSuccessRestrictedProductCount(),
                        totalCollect.getSuccessDetailPageViolationCount(),
                        totalCollect.getSuccessNodeViolationCount(),
                        totalFail,
                        totalCollect.getFailSendCount(),
                        totalCollect.getThreeFailCount()
                );
                log.info("collectMessage ：{}", collectMessage);

                SysUser sysUser = new SysUser();
                sysUser.setUserId(leaderUser.getUserId());
                sysUser.setUserName(leaderUser.getUserName());
                sysUser.setUserCode(leaderUser.getUserCode());
                // 更改文件中里面的创建人显示
                for (ComplaintRecordExcelDTO record : deptRecords) {
                    SysUser subUser = sysUserService.selectUserById(Convert.toLong(record.getCreateBy()));
                    if (subUser == null || StrUtil.isBlank(subUser.getUserName())) {
                        log.warn("创建人 {} 不存在或没有设置用户编码", record.getCreateBy());
                        continue;
                    }
                    record.setCreateBy(subUser.getUserName());
                }
                sendExcelNotificationToUser(sysUser, deptRecords, collectMessage,"部门");
            }
            // 获取所有创建人用户信息
            for (Map.Entry<String, List<ComplaintRecordExcelDTO>> entry : creatorRecordsMap.entrySet()) {
                String creator = entry.getKey();
                List<ComplaintRecordExcelDTO> creatorRecords = entry.getValue();

                // 通过登录名查询用户
                SysUser user = sysUserService.selectUserById(Convert.toLong(creator));

                if (user == null || StrUtil.isBlank(user.getUserCode())) {
                    log.warn("创建人 {} 不存在或没有设置用户编码", creator);
                    continue;
                }

                // 更改显示名字
                creatorRecords.forEach(record -> record.setCreateBy(user.getUserName()));
                ComplaintCollectDTO collect = complaintRecordService.selectCollectComplaint(String.valueOf(user.getUserId()));
                int totalSuccess = collect.getSuccessVariantViolationCount()
                        + collect.getSuccessRestrictedProductCount()
                        + collect.getSuccessDetailPageViolationCount()
                        + collect.getSuccessNodeViolationCount();

                int totalFail = collect.getFailSendCount() + collect.getThreeFailCount();

                String collectMessage = String.format(
                        "投诉记录\n时间：前24小时\n" +
                                "总计投诉任务成功 %d 条（变体违规 %d 条，受限商品 %d 条，详情页面违规 %d 条，节点违规 %d 条）\n" +
                                "总计投诉任务失败 %d 条（任务没发出 %d 条，投诉三次不成功 %d 条）",
                        totalSuccess,
                        collect.getSuccessVariantViolationCount(),
                        collect.getSuccessRestrictedProductCount(),
                        collect.getSuccessDetailPageViolationCount(),
                        collect.getSuccessNodeViolationCount(),
                        totalFail,
                        collect.getFailSendCount(),
                        collect.getThreeFailCount()
                );
                log.info("collectMessage ：{}", collectMessage);
                // 发送 Excel  通知
                sendExcelNotificationToUser(user, creatorRecords, collectMessage, "个人");
            }

        } catch (Exception e) {
            log.error("投诉结果定时通知任务执行异常", e);
        }

        log.info("投诉结果定时通知任务执行完成");
    }

    /**
     * 向用户发送Excel通知
     *
     * @param user 用户信息
     * @param records 符合条件的记录列表
     */
    private void sendExcelNotificationToUser(SysUser user, List<ComplaintRecordExcelDTO> records, String collectMessage, String suffixName) {
        if (user == null || CollUtil.isEmpty(records)) {
            return;
        }

        // 1. 准备Excel文件
        String fileName = suffixName + "-" +user.getUserName() + "-" + System.currentTimeMillis() + "-近一天的投诉结果.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;

        // 2. 准备OSS对象路径
        String ossObjectKey = "smc/dingDingFiles/" + DateUtils.getDate() + "/" + fileName;

        try {
            // 3. 生成Excel文件
            try (ExcelWriter excelWriter = EasyExcel.write(filePath).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "近一天的投诉结果").head(ComplaintRecordExcelDTO.class).build();
                excelWriter.write(records, writeSheet);
            }

            // 4. 上传到OSS
            aliOssService.putObjectByFile(bucketName, ossObjectKey, filePath);
            String ossUrl = aliOssAddress + ossObjectKey;

            log.info("投诉警报文件URL: {}", ossUrl);

            // 5. 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("您有").append(collectMessage).append("详情请查看附件。\n\n");

            // 6. 发送钉钉消息
            ActionCardMsgDto actionCardMsgDto = new ActionCardMsgDto();
            actionCardMsgDto.setTargetDingUserId(user.getUserCode());
            actionCardMsgDto.setMessageTitle("投诉通知");
            actionCardMsgDto.setMessageContent(content.toString());
            actionCardMsgDto.setMessageUrl(ossUrl);

            dingAsyncSendService.asyncSend(actionCardMsgDto);

            log.info("已向用户 {} 发送投诉通知，共 {} 条记录，文件URL: {}",
                    user.getUserName(), records.size(), ossUrl);

        } catch (Exception e) {
            log.error("向用户 {} 发送投诉通知失败", user.getUserName(), e);
        } finally {
            // 7. 删除本地临时文件
            FileUtils.deleteFile(filePath);
        }
    }
}
