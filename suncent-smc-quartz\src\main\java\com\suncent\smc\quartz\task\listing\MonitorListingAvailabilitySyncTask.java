package com.suncent.smc.quartz.task.listing;

import com.alibaba.fastjson.JSON;
import com.suncent.smc.common.enums.MonitorEnum;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.persistence.publication.domain.entity.MonitorListingAvailabilitySnapshot;
import com.suncent.smc.persistence.publication.service.IMonitorListingAvailabilitySnapshotService;
import com.suncent.smc.provider.biz.publication.DingdingMonitorInfoBiz;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * 商品可用性同步定时任务
 * 
 * <AUTHOR>
 * @date 2024-03-21
 */
@Component
public class MonitorListingAvailabilitySyncTask {
    private static final Logger log = LoggerFactory.getLogger(MonitorListingAvailabilitySyncTask.class);

    private static final int BATCH_SIZE = 2000;
    private static final String SYNC_CONTROL_KEY = "monitor_listing_availability:sync_control"; // Redis控制键

    @Autowired
    private IMonitorListingAvailabilitySnapshotService monitorListingAvailabilitySnapshotService;
    
    @Autowired
    private DingdingMonitorInfoBiz dingdingMonitorInfoBiz;
    
    @Autowired
    private RedisService redisService;

    /**
     * 同步可用性数据
     */
    @XxlJob("syncAvailabilityData")
    public void syncAvailabilityData() {
        log.info("开始同步商品可用性数据");
        
        try {
            // 1. 首先收集并分析所有待处理记录的异常模式
            log.info("第一阶段：开始收集并分析所有待处理数据的模式");
            List<Map<String, Object>> allPatterns = collectAllAbnormalPatterns();
            
            // 2. 处理异常模式并决定是否继续同步
            boolean shouldContinue = processAllAbnormalPatterns(allPatterns);
            
            // 3. 如果不应继续，则停止处理
            if (!shouldContinue) {
                log.warn("检测到异常数据模式且同步控制状态为暂停，暂停数据同步");
                XxlJobHelper.handleSuccess("已暂停数据同步，等待手动恢复");
                return;
            }
            
            // 4. 然后分批同步和处理所有数据
            log.info("第二阶段：开始同步所有待处理数据");
            int totalProcessed = 0;
            int batchCount = 0;
            boolean hasMore = true;
            
            // 循环处理，每次处理一批，直到全部处理完毕
            while (hasMore) {
                int processedCount = monitorListingAvailabilitySnapshotService.syncAvailabilityData(BATCH_SIZE);
                totalProcessed += processedCount;
                batchCount++;
                
                // 如果处理的数量小于批次大小，说明数据已处理完毕
                hasMore = processedCount == BATCH_SIZE;
                
                // 每处理10批数据记录一次日志，避免日志过多
                if (batchCount % 10 == 0) {
                    log.info("已处理 {} 批次，共 {} 条数据", batchCount, totalProcessed);
                }
            }
            
            log.info("商品可用性数据同步完成，处理数据条数：{}", totalProcessed);
            XxlJobHelper.handleSuccess("处理数据条数：" + totalProcessed);
            
        } catch (Exception e) {
            log.error("商品可用性数据同步失败", e);
            XxlJobHelper.handleFail("同步失败：" + e.getMessage());
        }
    }
    
    /**
     * 收集所有的异常模式
     * 
     * @return 异常模式列表
     */
    @SuppressWarnings("unchecked")
    private List<Map<String, Object>> collectAllAbnormalPatterns() {
        List<MonitorListingAvailabilitySnapshot> allPendingData = new ArrayList<>();
        List<MonitorListingAvailabilitySnapshot> batch;
        int totalCount = 0;
        int batchCount = 0;
        
        // 分批加载所有待处理数据，不限制最大记录数，确保分析全部数据
        Long lastId = 0L; // 初始ID为0，查询所有ID大于0的记录
        
        do {
            batch = monitorListingAvailabilitySnapshotService.getPendingSnapshotsById(lastId, BATCH_SIZE);
            if (!batch.isEmpty()) {
                allPendingData.addAll(batch);
                totalCount += batch.size();
                batchCount++;
                
                // 记录最后一条数据的ID，用于下次查询
                lastId = batch.get(batch.size() - 1).getId();
                
                // 每加载10批数据记录一次日志，避免日志过多
                if (batchCount % 10 == 0) {
                    log.info("已加载 {} 批次，共 {} 条待处理数据", batchCount, totalCount);
                }
            }
        } while (!batch.isEmpty() && batch.size() == BATCH_SIZE);
        
        log.info("已完成加载，待分析的待处理数据总数：{}", totalCount);
        
        // 对所有待处理数据进行异常模式分析
        Map<String, Object> analysisResult = monitorListingAvailabilitySnapshotService.analyzeSnapshotPatterns(allPendingData);
        
        if (analysisResult.containsKey("abnormalPatterns")) {
            return (List<Map<String, Object>>) analysisResult.get("abnormalPatterns");
        }
        
        return new ArrayList<>();
    }
    
    /**
     * 处理所有异常模式
     * 
     * @param patterns 异常模式列表
     * @return 是否继续处理数据（false表示需要暂停处理）
     */
    private boolean processAllAbnormalPatterns(List<Map<String, Object>> patterns) {
        if (patterns.isEmpty()) {
            log.info("未发现异常数据模式");
            return true;
        }
        
        log.info("发现 {} 个异常数据模式", patterns.size());
        boolean hasNotified = false;
        
        for (Map<String, Object> pattern : patterns) {
            String patternKey = "abnormal_pattern:" + pattern.get("pattern");
            if (!redisService.exists(patternKey)) {
                // 发送钉钉通知
                Map<String, Object> featureMap = new HashMap<>();
                featureMap.put("影响商品数", pattern.get("count"));
                featureMap.put("数据占比", String.format("%.2f%%", ((double) pattern.get("ratio")) * 100));
                dingdingMonitorInfoBiz.monitorMediumRiskAndSend(MonitorEnum.MONITOR_RPA_AVAILABILITY_ERROR,
                    "商品可用性数据异常：大量数据变更为相同数据\n" + JSON.toJSONString(featureMap, true));
                
                // 设置Redis缓存，1小时过期
                redisService.setCacheObject(patternKey, "1", 1L, TimeUnit.HOURS);
                
                log.info("已发送异常模式通知: {}", pattern.get("pattern"));
                hasNotified = true;
                break;
            }
        }
        
        // 如果发送了通知，检查同步控制状态
        if (hasNotified) {
            String controlValue = redisService.getCacheObject(SYNC_CONTROL_KEY);
            // 如果控制值是"0"，则暂停同步
            if ("0".equals(controlValue)) {
                log.warn("检测到同步控制状态为暂停(0)，将不继续处理数据");
                return false;
            }
        }
        
        return true;
    }

    /**
     * 清理旧数据
     */
    @XxlJob("cleanOldAvailabilityData")
    public void cleanOldAvailabilityData() {
        log.info("开始清理商品可用性旧数据");
        try {
            // 计算5天前的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -5);
            Date date = calendar.getTime();

            int deletedCount = monitorListingAvailabilitySnapshotService.cleanOldData(date);
            log.info("商品可用性旧数据清理完成，删除数据条数：{}", deletedCount);
            XxlJobHelper.handleSuccess("删除数据条数：" + deletedCount);
        } catch (Exception e) {
            log.error("商品可用性旧数据清理失败", e);
            XxlJobHelper.handleFail("清理失败：" + e.getMessage());
        }
    }
} 