package com.suncent.smc.common.utils;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import com.fasterxml.jackson.core.type.TypeReference;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023-06-28 11:22:00
 */
@Component
@Slf4j
public class SpelUtil {

    public static final String SPEL_MARK = "#";

    public static final ExpressionParser parser = new SpelExpressionParser();
    // 表达式缓存
    private final Map<String, Expression> expressionCache = new ConcurrentHashMap<>();

    // 模板解析上下文
    private final ParserContext templateContext = new TemplateParserContext("${", "}");

    // 模板变量正则表达式：匹配${xxx}模式
    private static final Pattern TEMPLATE_VAR_PATTERN = Pattern.compile("\\$\\{([^{}]+)\\}");

    /**
     * 获取SPEL上下文
     * @param method
     * @param args
     * @return
     */
    public static StandardEvaluationContext getContext(Method method, Object[] args) {
        //获取被拦截方法参数名列表(使用Spring支持类库)
        LocalVariableTableParameterNameDiscoverer u = new LocalVariableTableParameterNameDiscoverer();
        String[] paraNameArr = u.getParameterNames(method);
        //SPEL上下文
        StandardEvaluationContext context = new StandardEvaluationContext();
        //把方法参数放入SPEL上下文中
        for (int i = 0; i < paraNameArr.length; i++) {
            context.setVariable(paraNameArr[i], args[i]);
        }
        return context;
    }


    /**
     * 解析spel表达式，返回String
     * @param spel
     * @param context
     * @return
     */
    public static String parse(String spel, StandardEvaluationContext context) {
        if (StringUtils.isEmpty(spel)) {
            return "";
        }
        //spel包含多个{}，截取{}中的内容  解析格式: {#listingEditDTO.goodsCode}
        if (spel.contains("{") && spel.contains("}")) {
            StringBuilder sb = getContextString(spel, context);
            spel = sb.toString();
        }
        if (!spel.contains(SPEL_MARK)) {
            return spel;
        }
        return parser.parseExpression(spel).getValue(context, String.class);
    }


    /**
     * 解析spel表达式，返回List
     *
     * @param spel
     * @param context
     * @return
     */
    public static List<String> parseList(String spel, StandardEvaluationContext context) {
        if (StringUtils.isEmpty(spel)) {
            return new ArrayList();
        }
        //spel包含多个{}，截取{}中的内容  解析格式: {#listingEditDTO.goodsCode}
        if (spel.contains("{") && spel.contains("}")) {
            StringBuilder sb = getContextString(spel, context);
            spel = sb.toString();
        }
        if (!spel.contains(SPEL_MARK)) {
            return new ArrayList();
        }
        List<Object> value = parser.parseExpression(spel).getValue(context, List.class);
        //将Object转换为String
        List<String> list = new ArrayList<>();
        for (Object o : value) {
            list.add(String.valueOf(o));
        }
        return list;
    }



    private static StringBuilder getContextString(String spel, StandardEvaluationContext context) {
        String[] split = spel.split("\\{");
        StringBuilder sb = new StringBuilder();
        for (String s : split) {
            if (s.contains("}")) {
                String[] split1 = s.split("}");
                sb.append(parse(split1[0], context));
                // split1长度小于2，说明没有}，直接拼接
                if (split1.length >= 2) {
                    sb.append(split1[1]);
                }
            } else {
                sb.append(s);
            }
        }
        return sb;
    }




    /**
     * 评估提取规则
     *
     * @param rule 规则表达式
     * @param data 数据上下文
     * @return 评估结果
     */
    public boolean evaluateRule(String rule, Map<String, String> data) {
        if (rule == null || rule.trim().isEmpty()) {
            return true; // 空规则默认匹配
        }

        try {
            // 创建评估上下文
            StandardEvaluationContext context = createEvaluationContext(data);

            // 获取或解析表达式
            Expression expression = getOrParseExpression(rule);

            // 评估表达式
            return Boolean.TRUE.equals(expression.getValue(context, Boolean.class));
        } catch (Exception e) {
            log.error("评估规则时发生异常: {}, 规则: {}", e.getMessage(), rule);
            return false;
        }
    }

    /**
     * 评估模板并替换变量
     *
     * @param template 模板字符串
     * @param data 变量数据
     * @return 替换后的字符串
     */
    public String evaluateTemplate(String template, Map<String, String> data) {
        if (template == null || template.isEmpty()) {
            return "";
        }

        try {
//             简化模板语法转换为SpEL兼容语法
//            String spelTemplate = convertToSpelTemplate(template);
            
            // 创建评估上下文
            StandardEvaluationContext context = createEvaluationContext(data);

            // 解析并评估模板表达式
            Expression expression = parser.parseExpression(template, templateContext);
            String result = expression.getValue(context, String.class);

            // 如果结果包含"null"，可能是某些键不存在，尝试手动替换
            if (result != null && result.contains("null")) {
                log.debug("模板结果包含null值，尝试手动替换: {}", result);
                return "";
            }

            return result;
        } catch (Exception e) {
            log.warn("评估模板时发生异常: {}, 模板: {}, 尝试手动替换", e.getMessage(), template);
            return "";
        }
    }


    /**
     * 将简单模板语法转换为SpEL兼容语法
     * 将${key}转换为${['key']}
     */
    private String convertToSpelTemplate(String template) {
        if (template == null) {
            return "";
        }

        StringBuffer sb = new StringBuffer();
        Matcher matcher = TEMPLATE_VAR_PATTERN.matcher(template);

        while (matcher.find()) {
            String key = matcher.group(1);
            String replacement = "${['" + key + "']}";
            matcher.appendReplacement(sb, Matcher.quoteReplacement(replacement));
        }
        matcher.appendTail(sb);

        return sb.toString();
    }

    /**
     * 手动替换模板变量（备用方法）
     */
    private String manualTemplateReplace(String template, Map<String, String> data) {
        String result = template;
        for (Map.Entry<String, String> entry : data.entrySet()) {
            String key = entry.getKey();
            String value = entry.getValue() != null ? entry.getValue() : "";
            result = result.replace("${" + key + "}", value);
        }
        return result;
    }


    /**
     * 创建评估上下文
     */
    private StandardEvaluationContext createEvaluationContext(Map<String, String> data) {
        StandardEvaluationContext context = new StandardEvaluationContext();

        // 添加根对象
        context.setRootObject(data);
        // 2. 添加数据作为变量，使用#data语法访问整个Map
        context.setVariable("data", data);

        // 添加变量
        for (Map.Entry<String, String> entry : data.entrySet()) {
            context.setVariable(entry.getKey(), entry.getValue());
        }

        // 注册自定义函数
        registerCustomFunctions(context);

        return context;
    }

    /**
     * 获取或解析表达式（带缓存）
     */
    private Expression getOrParseExpression(String expressionString) {
        return expressionCache.computeIfAbsent(expressionString, parser::parseExpression);
    }

    /**
     * 注册自定义函数
     * 可以添加一些常用的辅助函数，扩展SpEL的能力
     */
    private void registerCustomFunctions(StandardEvaluationContext context) {
        try {
            // 注册数组相关函数
            context.registerFunction("contains",
                    SpelUtil.class.getDeclaredMethod("arrayContains", String.class, String.class));

            // 注册字符串处理函数
            context.registerFunction("isBlank",
                    SpelUtil.class.getDeclaredMethod("isBlank", String.class));

            context.registerFunction("isNotBlank",
                    SpelUtil.class.getDeclaredMethod("isNotBlank", String.class));

            // 注册错误码生成函数
            context.registerFunction("validationError",
                    ErrorCodeGenerator.class.getDeclaredMethod("validationError", String.class));

            context.registerFunction("businessError",
                    ErrorCodeGenerator.class.getDeclaredMethod("businessError", String.class));

            context.registerFunction("systemError",
                    ErrorCodeGenerator.class.getDeclaredMethod("systemError", String.class));

            context.registerFunction("frameworkError",
                    ErrorCodeGenerator.class.getDeclaredMethod("frameworkError", String.class));

            // 添加获取所有命名捕获组的函数
            context.registerFunction("getAllNamedGroups",
                    SpelUtil.class.getDeclaredMethod("getAllNamedGroups", String.class));

            // 添加获取特定命名捕获组的函数
            context.registerFunction("getNamedGroup",
                    SpelUtil.class.getDeclaredMethod("getNamedGroup", String.class, String.class));

            // 添加一个函数用于检查捕获组是否存在
            context.registerFunction("hasNamedGroup",
                    SpelUtil.class.getDeclaredMethod("hasNamedGroup", String.class, String.class));
        } catch (NoSuchMethodException e) {
            log.error("注册自定义函数失败", e);
        }
    }




    /**
     * 从JSON字符串中获取所有命名捕获组
     * 可在SpEL表达式中使用: #{getAllNamedGroups(allNamedGroups)}
     */
    public static Map<String, String> getAllNamedGroups(String jsonStr) {
        if (jsonStr == null || jsonStr.isEmpty()) {
            return Collections.emptyMap();
        }

        try {
            return new ObjectMapper().readValue(jsonStr,
                    new TypeReference<Map<String, String>>() {});
        } catch (Exception e) {
            return Collections.emptyMap();
        }
    }

    /**
     * 从JSON字符串中获取特定命名捕获组的值
     * 可在SpEL表达式中使用: #{getNamedGroup(allNamedGroups, 'groupName')}
     */
    public static String getNamedGroup(String jsonStr, String groupName) {
        if (jsonStr == null || jsonStr.isEmpty() || groupName == null) {
            return "";
        }

        try {
            Map<String, String> groups = new ObjectMapper().readValue(jsonStr,
                    new TypeReference<Map<String, String>>() {});
            return groups.getOrDefault(groupName, "");
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 检查特定命名捕获组是否存在
     * 可在SpEL表达式中使用: #{hasNamedGroup(allNamedGroups, 'groupName')}
     */
    public static boolean hasNamedGroup(String jsonStr, String groupName) {
        if (jsonStr == null || jsonStr.isEmpty() || groupName == null) {
            return false;
        }

        try {
            Map<String, String> groups = new ObjectMapper().readValue(jsonStr,
                    new TypeReference<Map<String, String>>() {});
            return groups.containsKey(groupName);
        } catch (Exception e) {
            return false;
        }
    }

    
    /**
     * 自定义数组包含函数
     */
    public static boolean arrayContains(String arrayStr, String value) {
        if (arrayStr == null || value == null) {
            return false;
        }

        // 处理JSON数组格式字符串
        if (arrayStr.startsWith("[") && arrayStr.endsWith("]")) {
            String[] items = arrayStr.substring(1, arrayStr.length() - 1)
                    .split(",");

            for (String item : items) {
                String trimmed = item.trim();
                // 去除可能的引号
                if (trimmed.startsWith("\"") && trimmed.endsWith("\"")) {
                    trimmed = trimmed.substring(1, trimmed.length() - 1);
                }
                if (value.equals(trimmed)) {
                    return true;
                }
            }
        }

        // 处理普通逗号分隔字符串
        String[] items = arrayStr.split(",");
        for (String item : items) {
            if (value.equals(item.trim())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 自定义空白检查函数
     */
    public static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 自定义非空白检查函数
     */
    public static boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }



    // 调试表达式解析
    public static void validateSpEL(String expr) {
        SpelUtil spelUtil = new SpelUtil();
        spelUtil.evaluateTemplate(expr, Collections.emptyMap());
    }

    public static void main(String[] args) {
//        validateSpEL("['code'] == \"8560\" and ['message'] == \"SKU did not provide all of the required attributes\" and ['attributeNames'].contains(\"external_product_id\") and ['attributeNames'].contains(\"externally_assigned_product_identifier\")");
        validateSpEL("${T(java.lang.String).format(\"字段%s(%s)的值不能编辑，请还原为%s后提交\", \n" +
                "   ['message'].substring(['message'].indexOf(\"''\") + 1, ['message'].indexOf(\"''\", ['message'].indexOf(\"''\") + 1)),\n" +
                "   ['attributeNames'],\n" +
                "   ['message'].substring(['message'].indexOf(\"to ''\") + 4, ['message'].indexOf(\"''\", ['message'].indexOf(\"to ''\") + 4))\n" +
                " )}");

    }


}