package com.suncent.smc.provider.update.resolver;

import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.provider.update.HandlerListingUpdateModuleComposite;
import com.suncent.smc.provider.update.ListingUpdateModuleResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 亚马逊规格更新模块执行器
 */
@Slf4j
@Component
public class AmazonSpecificationUpdateModuleResolver extends HandlerListingUpdateModuleComposite implements ListingUpdateModuleResolver {

    @Resource
    private HandlerListingUpdateModuleComposite handlerListingUpdateModuleComposite;

    @PostConstruct
    public void init() {
        handlerListingUpdateModuleComposite.moduleResolverMap.put(PlatformTypeEnum.AM.name() + ListingModuleType.SPECIFICATION.name(), this);
    }


    @Override
    public ItemDTO build(ItemDTO item) {
        if (ObjectUtils.isEmpty(item) || ObjectUtils.isEmpty(item.getGoodsHead())) {
            return item;
        }
        Map<String, String> amazonListingMap = item.getAmazonListingMap();
        if (ObjectUtils.isEmpty(amazonListingMap)) {
            amazonListingMap = new HashMap<>();
        }
        // Listing规格数据
        Map<String, String> specificationMap = goodsSpecificationService.selectSpecificationMapByGoodsId(item.getGoodsHead().getId());
        if (!CollectionUtils.isEmpty(specificationMap)) {
            try {
                specificationMap.put("item_length_unit_of_measure", Objects.isNull(specificationMap.get("item_length_unit")) ? "CM" : specificationMap.get("item_length_unit").toUpperCase());
                specificationMap.put("item_width_unit_of_measure", Objects.isNull(specificationMap.get("item_width_unit")) ? "CM" : specificationMap.get("item_width_unit").toUpperCase());
                specificationMap.put("item_height_unit_of_measure", Objects.isNull(specificationMap.get("item_height_unit")) ? "CM" : specificationMap.get("item_height_unit").toUpperCase());
                specificationMap.put("package_length_unit_of_measure", Objects.isNull(specificationMap.get("package_length_unit")) ? "CM" : specificationMap.get("package_length_unit").toUpperCase());
                specificationMap.put("package_width_unit_of_measure", Objects.isNull(specificationMap.get("package_width_unit")) ? "CM" : specificationMap.get("package_width_unit").toUpperCase());
                specificationMap.put("package_height_unit_of_measure", Objects.isNull(specificationMap.get("package_height_unit")) ? "CM" : specificationMap.get("package_height_unit").toUpperCase());
                specificationMap.put("package_weight_unit_of_measure", Objects.isNull(specificationMap.get("package_weight_unit")) ? "KG" : specificationMap.get("package_weight_unit").toUpperCase());

                specificationMap.put("item_length", new BigDecimal(String.valueOf(specificationMap.get("item_length"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("item_width", new BigDecimal(String.valueOf(specificationMap.get("item_width"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("item_height", new BigDecimal(String.valueOf(specificationMap.get("item_height"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_length", new BigDecimal(String.valueOf(specificationMap.get("package_length"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_width", new BigDecimal(String.valueOf(specificationMap.get("package_width"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_height", new BigDecimal(String.valueOf(specificationMap.get("package_height"))).setScale(2, RoundingMode.HALF_UP).toString());
                specificationMap.put("package_weight", new BigDecimal(String.valueOf(specificationMap.get("package_weight"))).setScale(2, RoundingMode.HALF_UP).toString());
            } catch (Exception e) {
                //长宽高重量保留两位  但是不影响主流程  所以catch住
                log.warn("Listing规格数据转换异常", e);
            }
        }
        amazonListingMap.putAll(specificationMap);
        item.setAmazonListingMap(amazonListingMap);

        return item;
    }

    @Override
    public ItemDTO compareData(ItemDTO item) throws Exception {
        return item;
    }
}