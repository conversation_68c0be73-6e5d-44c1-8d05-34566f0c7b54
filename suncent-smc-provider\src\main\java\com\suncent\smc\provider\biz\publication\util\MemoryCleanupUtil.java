package com.suncent.smc.provider.biz.publication.util;

import com.ebay.soap.eBLBaseComponents.ItemCompatibilityListType;
import com.ebay.soap.eBLBaseComponents.ItemCompatibilityType;
import com.ebay.soap.eBLBaseComponents.ItemType;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.provider.biz.liteflow.entity.EbayItemDTO;
import com.suncent.smc.provider.biz.publication.domain.EbayItemTypeDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;

/**
 * 内存清理工具类
 * 用于管理和清理eBay相关对象占用的内存，特别是处理大型描述字段
 */
@Slf4j
public class MemoryCleanupUtil {

    /**
     * 清理ItemType对象中的大内存占用部分
     * 主要清理description、ItemCompatibilityList等大字段
     *
     * @param ebayItem ItemType对象
     */
    public static void cleanupItemType(ItemType ebayItem) {
        if (ObjectUtils.isEmpty(ebayItem)) {
            return;
        }

        // 清理描述字段（通常是最大的内存占用）
        ebayItem.setDescription(null);

        // 清理适配数据
        cleanupCompatibilityList(ebayItem);

        // 可以根据需要清理其他大字段
        // ebayItem.setPictureDetails(null);
        // 等其他占用内存较大的字段
    }

    /**
     * 清理适配数据列表
     *
     * @param ebayItem ItemType对象
     */
    private static void cleanupCompatibilityList(ItemType ebayItem) {
        ItemCompatibilityListType itemCompatibilityList = ebayItem.getItemCompatibilityList();
        if (ObjectUtils.isNotEmpty(itemCompatibilityList)) {
            // 清空适配数据数组，释放内存
            itemCompatibilityList.setCompatibility(new ItemCompatibilityType[0]);
        }
    }

    /**
     * 完整清理EbayItemDTO对象
     *
     * @param ebayItemDTO EbayItemDTO对象
     */
    public static void cleanupEbayItemDTO(EbayItemDTO ebayItemDTO) {
        if (ObjectUtils.isEmpty(ebayItemDTO)) {
            return;
        }

        // 先清理内部的ItemType
        cleanupItemType(ebayItemDTO.getItemType());

        // 清理引用
        ebayItemDTO.setItemType(null);
    }

    /**
     * 清理JSON字符串变量
     * 适用于将大型对象序列化为JSON后的字符串变量
     *
     * @param jsonString JSON字符串
     * @return 返回null，方便链式调用
     */
    public static String cleanupJsonString(String jsonString) {
        if (StringUtils.isNotBlank(jsonString) && jsonString.length() > 10000) {
            // 记录清理大型JSON字符串
            log.debug("清理大型JSON字符串，长度: {}", jsonString.length());
        }
        return null;
    }

    /**
     * 综合清理方法，清理刊登/更新过程中使用的各种对象
     *
     * @param ebayItem    ItemType对象
     * @param ebayItemDTO EbayItemDTO对象
     * @param jsonString  JSON字符串
     */
    public static void cleanupAll(ItemType ebayItem, EbayItemDTO ebayItemDTO, String jsonString) {
        cleanupItemType(ebayItem);
        cleanupEbayItemDTO(ebayItemDTO);
        cleanupJsonString(jsonString);

        // 建议手动触发GC
        // System.gc(); // 不建议频繁调用，可能影响性能
    }

    /**
     * 只清理描述字段
     * 用于需要保留ItemType其他信息，但释放描述字段内存的场景
     *
     * @param ebayItem ItemType对象
     */
    public static void cleanupDescriptionOnly(ItemType ebayItem) {
        if (ObjectUtils.isEmpty(ebayItem)) {
            return;
        }
        ebayItem.setDescription(null);
    }

    /**
     * 清理EbayItemTypeDTO中的大内存字段
     *
     * @param itemType EbayItemTypeDTO对象
     */
    public static void cleanupEbayItemTypeDTO(EbayItemTypeDTO itemType) {
        if (ObjectUtils.isEmpty(itemType)) {
            return;
        }

        // 清理描述字段
        itemType.setDescription(null);

        // 清理适配数据
        itemType.setItemCompatibilityList(null);

        // 清理图片详情
        if (itemType.getPictureDetails() != null) {
            itemType.setPictureDetails(null);
        }

        // 清理视频详情
        itemType.setVideoDetails(null);

        // 清理其他可能的大字段
        itemType.setVariations(null);
    }

    /**
     * 强制清理所有可能的大内存字段
     *
     * @param itemType EbayItemTypeDTO对象
     */
    public static void forceCleanupEbayItemTypeDTO(EbayItemTypeDTO itemType) {
        if (ObjectUtils.isEmpty(itemType)) {
            return;
        }

        cleanupEbayItemTypeDTO(itemType);

        // 额外清理更多字段
        itemType.setPictureDetails(null);
        itemType.setVariations(null);
        itemType.setItemSpecifics(null);
        itemType.setShippingDetails(null);
        itemType.setReturnPolicy(null);
    }

    /**
     * 批量清理ItemType数组
     *
     * @param items ItemType数组
     */
    public static void cleanupItemTypeArray(ItemType[] items) {
        if (items == null || items.length == 0) {
            return;
        }

        for (ItemType item : items) {
            cleanupItemType(item);
        }
    }

    /**
     * 清理并设置为null，用于确保引用被清除
     *
     * @param obj 要清理的对象
     * @return 返回null
     */
    public static <T> T cleanupAndNull(T obj) {
        if (obj instanceof ItemType) {
            cleanupItemType((ItemType) obj);
        } else if (obj instanceof EbayItemTypeDTO) {
            cleanupEbayItemTypeDTO((EbayItemTypeDTO) obj);
        }
        return null;
    }
}