package com.suncent.smc.provider.biz.publication.util;

import lombok.extern.slf4j.Slf4j;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;

/**
 * 内存监控工具类
 * 用于监控JVM内存使用情况，当内存使用过高时进行告警和自动清理
 */
@Slf4j
public class MemoryMonitorUtil {

    // 内存使用率阈值（默认80%）
    private static final double MEMORY_THRESHOLD = 0.8;

    // 高危内存使用率阈值（默认90%）
    private static final double HIGH_MEMORY_THRESHOLD = 0.9;

    // 内存监控间隔（毫秒）
    private static final long MONITOR_INTERVAL = 5000;

    // 上次GC建议时间
    private static volatile long lastGcSuggestionTime = 0;

    // GC建议间隔（毫秒，避免频繁GC）
    private static final long GC_SUGGESTION_INTERVAL = 30000;

    /**
     * 获取当前内存使用情况
     *
     * @return MemoryInfo对象包含内存使用详情
     */
    public static MemoryInfo getCurrentMemoryInfo() {
        MemoryMXBean memoryBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapMemoryUsage = memoryBean.getHeapMemoryUsage();

        long used = heapMemoryUsage.getUsed();
        long max = heapMemoryUsage.getMax();
        long committed = heapMemoryUsage.getCommitted();

        double usageRatio = (double) used / max;

        return new MemoryInfo(used, max, committed, usageRatio);
    }

    /**
     * 检查内存使用情况并在必要时进行告警
     *
     * @param context 上下文信息，用于日志记录
     * @return true表示内存使用正常，false表示内存使用过高
     */
    public static boolean checkMemoryUsage(String context) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();

        if (memoryInfo.getUsageRatio() > HIGH_MEMORY_THRESHOLD) {
            log.warn("【高危内存告警】{}：内存使用率达到{:.2f}%，已用内存：{}MB，最大内存：{}MB",
                    context,
                    memoryInfo.getUsageRatio() * 100,
                    memoryInfo.getUsedMB(),
                    memoryInfo.getMaxMB());

            // 建议立即进行垃圾回收
            suggestGarbageCollection(true);
            return false;

        } else if (memoryInfo.getUsageRatio() > MEMORY_THRESHOLD) {
            log.warn("【内存告警】{}：内存使用率达到{:.2f}%，已用内存：{}MB，最大内存：{}MB",
                    context,
                    memoryInfo.getUsageRatio() * 100,
                    memoryInfo.getUsedMB(),
                    memoryInfo.getMaxMB());

            // 建议进行垃圾回收
            suggestGarbageCollection(false);
            return false;
        }

        return true;
    }

    /**
     * 建议进行垃圾回收
     *
     * @param force 是否强制建议GC（忽略时间间隔限制）
     */
    public static void suggestGarbageCollection(boolean force) {
        long currentTime = System.currentTimeMillis();

        if (force || (currentTime - lastGcSuggestionTime) > GC_SUGGESTION_INTERVAL) {
            log.info("建议进行垃圾回收以释放内存");
            System.gc();
            lastGcSuggestionTime = currentTime;
        }
    }

    /**
     * 记录内存使用情况
     *
     * @param context 上下文信息
     */
    public static void logMemoryUsage(String context) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();
        log.info("【内存监控】{}：内存使用率{:.2f}%，已用内存：{}MB，最大内存：{}MB，已提交内存：{}MB",
                context,
                memoryInfo.getUsageRatio() * 100,
                memoryInfo.getUsedMB(),
                memoryInfo.getMaxMB(),
                memoryInfo.getCommittedMB());
    }

    /**
     * 在处理大对象前检查内存
     *
     * @param context           上下文信息
     * @param objectDescription 对象描述
     * @return true表示可以继续处理，false表示应该暂停处理
     */
    public static boolean checkBeforeProcessingLargeObject(String context, String objectDescription) {
        MemoryInfo memoryInfo = getCurrentMemoryInfo();

        if (memoryInfo.getUsageRatio() > HIGH_MEMORY_THRESHOLD) {
            log.warn("【内存不足】{}：处理{}前内存使用率已达{:.2f}%，建议暂停处理",
                    context, objectDescription, memoryInfo.getUsageRatio() * 100);

            // 强制进行垃圾回收
            suggestGarbageCollection(true);

            // 等待一段时间让GC完成
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }

            // 重新检查内存
            MemoryInfo newMemoryInfo = getCurrentMemoryInfo();
            if (newMemoryInfo.getUsageRatio() > HIGH_MEMORY_THRESHOLD) {
                log.error("【内存严重不足】{}：GC后内存使用率仍为{:.2f}%，暂停处理{}",
                        context, newMemoryInfo.getUsageRatio() * 100, objectDescription);
                return false;
            }
        }

        return true;
    }

    /**
     * 内存信息类
     */
    public static class MemoryInfo {
        private final long used;
        private final long max;
        private final long committed;
        private final double usageRatio;

        public MemoryInfo(long used, long max, long committed, double usageRatio) {
            this.used = used;
            this.max = max;
            this.committed = committed;
            this.usageRatio = usageRatio;
        }

        public long getUsed() {
            return used;
        }

        public long getMax() {
            return max;
        }

        public long getCommitted() {
            return committed;
        }

        public double getUsageRatio() {
            return usageRatio;
        }

        public long getUsedMB() {
            return used / (1024 * 1024);
        }

        public long getMaxMB() {
            return max / (1024 * 1024);
        }

        public long getCommittedMB() {
            return committed / (1024 * 1024);
        }
    }
}
