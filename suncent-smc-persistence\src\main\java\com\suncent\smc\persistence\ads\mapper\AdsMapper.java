package com.suncent.smc.persistence.ads.mapper;

import com.suncent.smc.common.annotation.DataSizeMonitor;
import com.suncent.smc.common.annotation.DataSource;
import com.suncent.smc.common.enums.DataSourceType;
import com.suncent.smc.persistence.ads.domain.*;
import com.suncent.smc.persistence.publication.domain.entity.AdapterManage;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

import static com.suncent.smc.common.enums.MonitorEnum.*;

@Repository
@DataSource(DataSourceType.ADS)
public interface AdsMapper {

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer insertItDemandA(ItDemand itDemand);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer insertItDemandCompare(ItDemand itDemand);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer countByPnAndAsin(@Param("asin") String asin);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer countByPnAndAsinCompare(@Param("asin") String asin);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_2000)
    List<Map<String, String>> selectByAsinList(List<String> asinList);

    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_50000)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<Map<String, Object>> selectAdapterErrorList( @Param("currentNumber")int currentNumber);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer insertItDemandByCopyId( @Param("partNumber")String partNumber, @Param("itDemandId")String itDemandId);

    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_50000)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<Map<String, String>> selectByStatus( @Param("statusList")List<String> status, @Param("createTime")String createTime);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    int updateItDemandStatusByAsin(@Param("asinList")List<String> asinList, @Param("status")String status, @Param("errorReason")String errorReason);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    int updateItDemandStatusById(@Param("id")String id, @Param("status")String status, @Param("errorReason")String errorReason);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_10000)
    List<AdsFitmentDataEbay> selectFitmentDataEbayByProductCode(@Param("productCode") String productCode);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_10000)
    List<AdsEbayDataRequirement> selectEbayDataRequirementByProductCode(@Param("productCode") String productCode, @Param("goodsCode") String goodsCode);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer insertEbayDataRequirement(@Param("productCode") String productCode, @Param("goodsCode") String goodsCode);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    Integer updateEbayFitmentIsUpload(@Param("productCodeList") List<String> productCodeList);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    String selectDicPartnumberByProductCode(@Param("productCode") String productCode);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_50000)
    List<AdsFitmentDataVIO> selectFitmentDataVIO(List<String> productCodes);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_10000)
    List<AdsFitmentGPTData> selectFitmentGPTData(List<String> productCodes);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    String getDataRedLineMaxBatch();

    // 列 redline_price 不存在
    @Deprecated
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_100000)
    List<Map<String, String>> getRedLinePriceList(@Param("dataMaxBatch") String dataMaxBatch,@Param("goodsCodeList") List<String> goodsCodeList,@Param("platform") String platform);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_100000)
    List<String> getDataRedLineGoodsByMaxBatch(String dataMaxBatch);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_50000)
    List<AdsListingLabel>getListingLabelList(@Param("shopCode")String shopCode);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_5000)
    List<AdsListingLabel> getListingLabelListPaged(@Param("shopCode") String shopCode, @Param("offset") int offset, @Param("limit") int limit);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    AdsListingLabel getListingLabelByAsin(@Param("shopCode")String shopCode,@Param("asin")String asin);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_2000)
    List<Map<String, Object>> countByProductCodes(@Param("productCodes") List<String> productCodes);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<AdsRecordData> selectTodayCartList(@Param("date")String date, @Param("shopCode")String shopCode,@Param("asinList")List<String> asinList);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    int countNodeIncorrectByCoreListing(@Param("nowDate") String nowDate);

    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_2000)
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<AmzJudgeCategoryInfoVO> listNodeIncorrectByCoreListing(String nowDate);

    /**
     * 根据平台商品ID、平台商品编码、店铺编码、站点编码更新caseId
     * @param platformGoodsId
     * @param platformGoodsCode
     * @param shopCode
     * @param siteCode
     * @param caseId
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateCaseIdByNodeCorrect(@Param("platformGoodsId") String platformGoodsId, @Param("platformGoodsCode") String platformGoodsCode, @Param("shopCode") String shopCode, @Param("siteCode") String siteCode, @Param("caseId") String caseId);

    /**
     * 插入节点异常待办
     * @param amzJudgeCategoryMapVO
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void insertAmzJudgeCategoryMap(AmzJudgeCategoryMapVO amzJudgeCategoryMapVO);

    /**
     * 查询节点异常待办
     * @param operationClassification
     * @param categoryName
     * @param categoryCode
     * @param newPlatformCategoryId
     * @param newCategoryName
     * @return
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    int countAmzJudgeCategoryMap(@Param("operationClassification") String operationClassification, @Param("categoryCode") String categoryCode,
                                 @Param("newPlatformCategoryId") String newPlatformCategoryId, @Param("newCategoryName") String newCategoryName);

    /**
     * 更新节点异常待办状态
     * @param platformGoodsId
     * @param platformGoodsCode
     * @param shopCode
     * @param siteCode
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateAmzJudgeCategoryInfoStatus(@Param("platformGoodsId") String platformGoodsId, @Param("platformGoodsCode") String platformGoodsCode, @Param("shopCode") String shopCode, @Param("siteCode") String siteCode);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_5000)
    List<FitmentDemand> selectFitmentDemandList();

    /**
     * 按类型分页查询适配需求列表
     *
     * @param type   数据类型：1-SKU+ASIN+PN, 2-SKU+ASIN, 3-仅SKU
     * @param offset 偏移量
     * @param limit  限制数量
     * @return 适配需求列表
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_5000)
    List<FitmentDemand> selectFitmentDemandListByType(@Param("type") int type, @Param("offset") int offset, @Param("limit") int limit);

    /**
     * 批量查询ASIN对应的处理记录数量
     *
     * @param asinList ASIN列表
     * @return ASIN和对应数量的映射
     */
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_5000)
    List<Map<String, Object>> countByPnAndAsinBatch(@Param("asinList") List<String> asinList);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateFitmentDemandStatus(@Param("status") String status,@Param("id") Long id);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_2000)
    List<FitExamineDay> getFitExamineDayListByAsins(@Param("asins") List<String> asins);

    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_2000)
    List<Map<String, String>> listDemandByAsins(@Param("notExistsAsin") List<String> notExistsAsin);

    int countByAsin(@Param("platformGoodsId") String platformGoodsId);

    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_50000)
    List<AmazonListingChangeMonitorVO> listAmazonListingChangeMonitor(@Param("pdmGoodsCodeList") List<String> pdmGoodsCodeList, @Param("asinList") List<String> asinList, @Param("date") String date);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<AdsFitmentDataBi> getByProduct(@Param("productCode") String productCode);

    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_2000)
    List<String> getDataUpdateLinks();

    List<AdsFitmentDataEbayItem> selectFitmentDataEbayItemByProductCode(@Param("productCode") String productCode);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<AdapterManage> listAdapterManage(AdapterManage adapterManage);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    void updateAdapterManage(List<AdapterManage> adapterManages);

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    String getMaxDate();

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    String getSecondDate();

    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<String> getAdapterManageDifferenceSetByDate(@Param("fromDate") String fromDate, @Param("toDate") String toDate, @Param("pageSize") int pageSize, @Param("offset")int offset);


    @Transactional(propagation = Propagation.REQUIRES_NEW)
    List<String> listBlockRelinePriceSku();

    Integer countCartListByDate(@Param("date") String date, @Param("shopCode") String shopCode, @Param("asinList") List<String> asinList);

    /**
     * 根据产品编码列表查询ads_fitmentdata_oe表数据
     *
     * @param productCodes 产品编码列表
     * @return OE数据列表
     */
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    @DataSizeMonitor(value = MONITOR_QUERY_LIMIT_10000)
    List<AdsFitmentDataOe> selectFitmentDataOeList(List<String> productCodes);
}
