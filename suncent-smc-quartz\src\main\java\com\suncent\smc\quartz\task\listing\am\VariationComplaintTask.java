package com.suncent.smc.quartz.task.listing.am;

import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import java.util.List;
import java.util.Map;
import java.util.Date;
import java.util.ArrayList;
import java.util.stream.Collectors;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.config.RuoYiConfig;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.file.FileUtils;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.salesTool.domain.entity.AmVariationComplaintConfig;
import com.suncent.smc.persistence.salesTool.domain.entity.AmVariationComplaintMonitorRecord;
import com.suncent.smc.persistence.salesTool.service.IAmVariationComplaintConfigService;
import com.suncent.smc.persistence.salesTool.service.IAmVariationComplaintMonitorRecordService;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.system.service.ISysUserService;
import com.suncent.smc.provider.dingding.service.IDingAsyncSendService;
import com.suncent.smc.provider.dingding.domain.ActionCardMsgDto;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.suncent.smc.provider.biz.publication.DingdingMonitorInfoBiz;
import com.suncent.smc.provider.dingding.domain.DingDingDetailVO;

/**
 * 变体投诉定时通知任务
 */
@Component
@Slf4j
public class VariationComplaintTask {

    @Autowired
    private IAmVariationComplaintMonitorRecordService monitorRecordService;
    
    @Autowired
    private ISysUserService sysUserService;
    
    @Autowired
    private IDingAsyncSendService dingAsyncSendService;
    
    @Autowired
    private IAmVariationComplaintConfigService amVariationComplaintConfigService;
    
    @Autowired
    private DingdingMonitorInfoBiz dingdingMonitorInfoBiz;
    
    @Autowired
    private IAliOssService aliOssService;
    
    @Value("${aliyun.oss.defaultBucketName}")
    private String bucketName;
    
    @Value("${aliyun.oss.suncentUrlPrefix}")
    private String aliOssAddress;
    
    private static final String CHINESE_DATE_FORMAT = "yyyy年MM月dd日";
    
    @XxlJob("variationComplaintNoticeTask")
    public void variationComplaintNoticeTask() {
        log.info("变体投诉定时通知任务开始执行");
        // 条件：监控结果为"存在变体、需要投诉"、已返回CASE ID、且CASE状态为Answered则直接通知对应创建人
        
        try {
            // 1. 查询符合条件的记录
            AmVariationComplaintConfig queryParamConfig = new AmVariationComplaintConfig();
            // 监控结果为"存在变体、需要投诉"(1)
            queryParamConfig.setMonitorResult("1");
            // CASE状态为Answered 
            queryParamConfig.setCaseStatus("Answered");
            queryParamConfig.setType(1);
            
            List<AmVariationComplaintConfig> records = amVariationComplaintConfigService.selectList(queryParamConfig);

            // 增加类型为变体监控，结果为“不存在变体”的记录
            AmVariationComplaintConfig monitorResultConfig = new AmVariationComplaintConfig();
            monitorResultConfig.setMonitorResult("0");
            monitorResultConfig.setType(2);
            List<AmVariationComplaintConfig> monitorResultRecords = amVariationComplaintConfigService.selectList(monitorResultConfig);
            if (CollUtil.isNotEmpty(monitorResultRecords)) {
                records.addAll(monitorResultRecords);
            }
            
            if (CollUtil.isEmpty(records)) {
                log.info("没有符合条件的变体投诉记录");
                return;
            }
            log.info("符合条件的变体投诉记录数: {}", records.size());

            // 2. 按创建人分组
            Map<String, List<AmVariationComplaintConfig>> creatorRecordsMap = records.stream()
                    .filter(record -> StrUtil.isNotBlank(record.getCreateBy()) && StrUtil.isNotBlank(record.getCaseId()))
                    .collect(Collectors.groupingBy(AmVariationComplaintConfig::getCreateBy));
            
            if (creatorRecordsMap.isEmpty()) {
                log.info("没有需要通知的创建人");
                return;
            }
            
            // 3. 获取所有创建人用户信息
            for (Map.Entry<String, List<AmVariationComplaintConfig>> entry : creatorRecordsMap.entrySet()) {
                String creator = entry.getKey();
                List<AmVariationComplaintConfig> creatorRecords = entry.getValue();
                
                // 通过登录名查询用户
                SysUser user = sysUserService.selectUserById(Convert.toLong(creator));
                
                if (user == null || StrUtil.isBlank(user.getUserCode())) {
                    log.warn("创建人 {} 不存在或没有设置用户编码", creator);
                    continue;
                }
                
                sendExcelNotificationToUser(user, creatorRecords);
            }
            
        } catch (Exception e) {
            log.error("变体投诉定时通知任务执行异常", e);
        }
        
        log.info("变体投诉定时通知任务执行完成");
    }
    
    /**
     * 向用户发送Excel通知
     * 
     * @param user 用户信息
     * @param records 符合条件的记录列表
     */
    private void sendExcelNotificationToUser(SysUser user, List<AmVariationComplaintConfig> records) {
        if (user == null || CollUtil.isEmpty(records)) {
            return;
        }
        
        // 1. 准备Excel文件
        String fileName = DateUtil.format(new Date(), CHINESE_DATE_FORMAT) + "-" + 
                          user.getUserName() + "-" + System.currentTimeMillis() + "-变体投诉处理.xlsx";
        String filePath = RuoYiConfig.getDownloadPath() + fileName;
        
        // 2. 准备OSS对象路径
        String ossObjectKey = "smc/dingDingFiles/" + DateUtils.getDate() + "/" + fileName;
        
        try {
            // 3. 生成Excel文件
            try (ExcelWriter excelWriter = EasyExcel.write(filePath).build()) {
                WriteSheet writeSheet = EasyExcel.writerSheet(0, "变体投诉记录").head(AmVariationComplaintConfig.class).build();
                excelWriter.write(records, writeSheet);
            }
            
            // 4. 上传到OSS
            aliOssService.putObjectByFile(bucketName, ossObjectKey, filePath);
            String ossUrl = aliOssAddress + ossObjectKey;
            
            // 5. 构建消息内容
            StringBuilder content = new StringBuilder();
            content.append("您有").append(records.size()).append("条变体投诉记录需要处理，详情请查看附件。\n\n");
            content.append("主要包含以下ASIN：\n");
            
            // 最多显示5个ASIN，避免内容过长
            List<String> asinList = records.stream()
                    .map(AmVariationComplaintConfig::getAsin)
                    .distinct()
                    .limit(5)
                    .collect(Collectors.toList());
            
            content.append(String.join("、", asinList));
            if (records.size() > 5) {
                content.append("等...");
            }
            
            // 6. 发送钉钉消息
            ActionCardMsgDto actionCardMsgDto = new ActionCardMsgDto();
            actionCardMsgDto.setTargetDingUserId(user.getUserCode());
            actionCardMsgDto.setMessageTitle("变体投诉处理通知");
            actionCardMsgDto.setMessageContent(content.toString());
            actionCardMsgDto.setMessageUrl(ossUrl);
            
            dingAsyncSendService.asyncSend(actionCardMsgDto);
            
            log.info("已向用户 {} 发送变体投诉处理通知，共 {} 条记录，文件URL: {}", 
                    user.getUserName(), records.size(), ossUrl);
            
        } catch (Exception e) {
            log.error("向用户 {} 发送变体投诉处理通知失败", user.getUserName(), e);
        } finally {
            // 7. 删除本地临时文件
            FileUtils.deleteFile(filePath);
        }
    }
}
