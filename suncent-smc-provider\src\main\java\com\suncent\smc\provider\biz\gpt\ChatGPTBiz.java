package com.suncent.smc.provider.biz.gpt;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class ChatGPTBiz {
    @Value("${chat-gpt.url}")
    protected String chatGptUrl;

    @Value("${chat-gpt.key}")
    protected String chatGptKey;

    @Autowired
    private ISysConfigService sysConfigService;

    /**
     * chatGPT
     * @param message
     * @return chat一下
     */
    public String chat(String message){
        try {
            Map<String, String> param = new HashMap<>();
            param.put("content", message);
            param.put("role", "user");

            Map<String, List> messages = new HashMap<>();
            messages.put("messages", Lists.newArrayList(param));
            HttpResponse authorization = HttpUtils.createPost(chatGptUrl)
                    .header("Content-Type", "application/json")
                    .header("api-key", chatGptKey)
                    .body(JSON.toJSONString(messages))
                    .execute();

            String body = authorization.body();
            ChatGPTDTO chatGPTDTO = JSONUtil.toBean(body, ChatGPTDTO.class);
            List<ChatGPTDTO.ChoicesDTO> choices = chatGPTDTO.getChoices();
            if (CollUtil.isNotEmpty(choices)){
                ChatGPTDTO.ChoicesDTO.MessageDTO messageDTO = choices.get(0).getMessage();
                if (ObjUtil.isNotNull(messageDTO)){
                    return messageDTO.getContent();
                }
            }
            log.error("chatGPT error:{}", body);
            return null;
        } catch (Exception e) {
            log.error("chatGPT error", e);
            return null;
        }
    }

    public String aiOptimize(String platformCode, JSONObject aiParam) {
        if("AM".equals(platformCode)){
            String defaultAmPrompt = sysConfigService.selectConfigByKey("amazon.prompt");

            String prompt = defaultAmPrompt +
                    "\n"  +
                    "\n"  +
                    "\"\"\""+ JSONUtil.toJsonStr(aiParam) + "\"\"\"";

            log.debug("amazon prompt:{}", prompt);
            String chat = chat(prompt);
            log.debug("chat:{}", chat);
            // 处理前后的```
            if (chat != null && chat.startsWith("```") && chat.endsWith("```")){
                chat = chat.substring(3, chat.length() - 3);
            }
            return chat;
        }else {
            String defaultEbPrompt = sysConfigService.selectConfigByKey("ebay.prompt");

            String prompt = defaultEbPrompt +
                    "\n"  +
                    "\n"  +
                    "```"+ JSONUtil.toJsonStr(aiParam) + "```\n";
            log.debug("ebay prompt:{}", prompt);

            String chat = chat(prompt);
            log.debug("chat:{}", chat);
            // 处理前后的```
            if (chat != null && chat.startsWith("```") && chat.endsWith("```")){
                chat = chat.substring(3, chat.length() - 3);
            }
            return chat;
        }
    }

    public static void main(String[] args) {
        String json = "{\"title\":\"Replacement Modicle for Silverado 1500, Suburban 1500, Sierra 1500, Tahoe, Silverado 2500 HD, Yukon XL 1500, Savana 3500, Astro, Escalade EXT, Express 2500, Yukon XL 2500, Sierra 3500 & Savana 2500\"}";
        JSONObject jsonObject = JSON.parseObject(json);


        System.out.println(jsonObject);
    }
}
