package com.suncent.smc.framework.mybatis;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.alibaba.fastjson.JSON;
import com.github.pagehelper.parser.CountSqlParser;
import com.p6spy.engine.common.Value;
import com.suncent.dingtalk.robot.model.MonitorServerEnum;
import com.suncent.dingtalk.robot.model.MonitorTemplate;
import com.suncent.dingtalk.robot.utils.DingdingMessageUtil;
import com.suncent.smc.common.annotation.DataSizeMonitor;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.enums.MonitorEnum;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.EnvUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.builder.StaticSqlSource;
import org.apache.ibatis.cache.CacheKey;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.ParameterMapping;
import org.apache.ibatis.mapping.ParameterMode;
import org.apache.ibatis.mapping.ResultMap;
import org.apache.ibatis.mapping.SqlCommandType;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.plugin.Intercepts;
import org.apache.ibatis.plugin.Invocation;
import org.apache.ibatis.plugin.Plugin;
import org.apache.ibatis.plugin.Signature;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Slf4j
@Intercepts({
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}),
        @Signature(type = Executor.class, method = "query", args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class, CacheKey.class, BoundSql.class})
})
public class DataSizeMonitorInterceptor implements Interceptor {

    @Autowired
    public RedisService redisService;
    @Resource
    protected DingdingMessageUtil dingdingMessageUtil;

    // 缓存方法上的注解，提高性能
    private final Map<String, DataSizeMonitor> annotationCache = new ConcurrentHashMap<>();

    /**
     * 监控结果枚举
     */
    private static class MonitorResult {
        private final boolean block;
        private final String message;

        private MonitorResult(boolean block, String message) {
            this.block = block;
            this.message = message;
        }

        public boolean isBlock() {
            return block;
        }

        public String getMessage() {
            return message;
        }

        public static MonitorResult proceed() {
            return new MonitorResult(false, null);
        }

        public static MonitorResult block(String message) {
            return new MonitorResult(true, message);
        }
    }

    @Override
    public Object intercept(Invocation invocation) throws Throwable {
        // 第一层决策：快速判断是否需要跳过监控
        if (shouldSkipMonitoring(invocation)) {
            // 直接执行业务SQL，不进行监控
            return invocation.proceed();
        }
        
        // 第二层：监控检查（使用try-catch确保监控逻辑异常不会影响业务）
        MonitorResult monitorResult = MonitorResult.proceed(); // 默认允许执行
        try {
            monitorResult = performDataSizeCheck(invocation);
        } catch (Exception e) {
            // 监控逻辑异常，记录日志但不影响业务执行
            log.error("数据量监控逻辑异常，将跳过监控: {}", e.getMessage(), e);
            // 这里不改变monitorResult的默认值，保持proceed
        }
        
        // 第三层：根据监控结果决定最终行为
        if (monitorResult.isBlock()) {
            // 明确要阻止执行的情况
            throw new BusinessException(monitorResult.getMessage());
        }
        
        // 所有情况下的最终保障：确保业务SQL执行
        return invocation.proceed();
    }

    /**
     * 快速检查是否应跳过监控
     */
    private boolean shouldSkipMonitoring(Invocation invocation) {
        try {
            Object[] args = invocation.getArgs();
            MappedStatement ms = (MappedStatement) args[0];
            Object parameter = args[1];
            
            // 1. 不是SELECT操作的直接跳过
            if (ms.getSqlCommandType() != SqlCommandType.SELECT) {
                return true;
            }
            
            // 2. 检查SQL是否为空
            BoundSql boundSql = getBoundSql(ms, parameter);
            if (StrUtil.isBlank(boundSql.getSql())) {
                return true;
            }
            
            // 3. 检查是否有监控注解
            DataSizeMonitor annotation = getAnnotation(ms.getId());
            if (annotation == null) {
                return true;
            }
            
            // 4. 检查是否为COUNT查询
            if (isCountQuery(boundSql.getSql())) {
                return true;
            }
            
            // 5. 检查是否为分页查询
            if (isPaginationQuery(ms, parameter, boundSql)) {
                return true;
            }
            
            // 6. 检查监控枚举是否有效
            MonitorEnum monitorEnum = annotation.value();
            if (monitorEnum == null || monitorEnum.getMonitorNum() <= 0) {
                return true;
            }
            
            // 需要进行监控
            return false;
        } catch (Exception e) {
            // 判断过程出错，为安全起见跳过监控
            log.error("判断是否跳过监控时发生异常，将跳过监控", e);
            return true;
        }
    }

    /**
     * 执行数据量检查逻辑
     */
    private MonitorResult performDataSizeCheck(Invocation invocation) throws Exception {
        // 获取所需参数
        Object[] args = invocation.getArgs();
        MappedStatement ms = (MappedStatement) args[0];
        Object parameter = args[1];
        RowBounds rowBounds = (RowBounds) args[2];
        ResultHandler<?> resultHandler = (ResultHandler<?>) args[3];
        Executor executor = (Executor) invocation.getTarget();
        Configuration configuration = ms.getConfiguration();
        String statementId = ms.getId();
        BoundSql boundSql = getBoundSql(ms, parameter);
        
        // 获取注解和相关配置
        DataSizeMonitor annotation = getAnnotation(statementId);
        MonitorEnum monitorEnum = annotation.value();
        
        // 执行count查询
        int count = 0;
        if (StringUtils.hasText(annotation.countMethod())) {
            // 构造count方法名
            String countMethodName = annotation.countMethod();
            // 构造count方法的完整ID
            String namespacePrefix = statementId.substring(0, statementId.lastIndexOf(".") + 1);
            String countStatementId = namespacePrefix + countMethodName;

            // 检查count语句是否存在
            MappedStatement countMs = ms.getConfiguration().getMappedStatement(countStatementId);

            // 使用相同的Executor执行count查询
            List<Object> countResult = executor.query(countMs, parameter, RowBounds.DEFAULT, resultHandler);
            if (countResult == null || countResult.isEmpty()) {
                log.warn("count查询结果为空: {}", countStatementId);
                return MonitorResult.proceed();
            }
            Number countNumber = (Number) countResult.get(0);
            count = countNumber.intValue();
        } else {
            String countSql = getCountSql(boundSql, configuration);
            count = selectCount(executor, countSql, ms, configuration);
        }
        
        // 如果数据量低于监控阈值，直接允许执行，无需检查缓存
        Integer monitorNum = monitorEnum.getMonitorNum();
        if (count <= monitorNum) {
            log.info("数据查询监控 - {} 数据量未超过阈值: {} <= {}", statementId, count, monitorNum);
            return MonitorResult.proceed();
        }

        // 如果count大于200000，强制拦截
        boolean isBreak = count > 200000;

        String paramJson = parameter == null ? "" : JSON.toJSONString(parameter);
        String md5 = DigestUtil.md5Hex(StrUtil.join(":", statementId, paramJson));

        // 检查Redis缓存
        String key = monitorEnum.getKeyPrefix() + ":" + md5;
        if (redisService.exists(key)) {
            String value = redisService.getCacheObject(key);
            // 大于200000条,直接异常出去
            if (isBreak) {
                return MonitorResult.block("数据量过大,已达到:" + count + "条数据,请使用分页查询.");
            }
            if ("true".equals(value)) {
                return MonitorResult.proceed();
            } else {
                return MonitorResult.block("数据量过大,已达到:" + count + "条数据,请使用分页查询.");
            }
        }

        // 数据量超过阈值
        log.error("数据查询监控 - {} 数据量超过阈值: {} > {}", statementId, count, monitorNum);

        // 发送钉钉告警
        Map<String, String> featureMap = new HashMap<>();
        featureMap.put("查询总数", count + " 条");
        featureMap.put("异常方法", statementId);
        featureMap.put("redisKey", key);
        if (EnvUtils.isProdProfile()) {
            dingdingMessageUtil.sendMsgByMiddleRotbot(
                    new MonitorTemplate(
                            MonitorServerEnum.SMC,
                            monitorEnum.getMonitorName(),
                            MonitorEnum.getMonitorLevelEnum(monitorEnum.getMonitorType()),
                            "数据组异常查询监控报警",
                            featureMap
                    )
            );
        }
        redisService.setCacheObject(key, isBreak ? "false" : "true", 1L, TimeUnit.DAYS);

        // 大于200000条,直接异常出去
        if (isBreak) {
            return MonitorResult.block("数据量过大,已达到:" + count + "条数据,请使用分页查询.");
        }

        // 允许执行
        return MonitorResult.proceed();
    }

    private boolean isCountQuery(String sql) {
        // 移除多余空格并转小写
        sql = sql.replaceAll("\\s+", " ").trim().toLowerCase();

        // 检查是否是count查询
        return sql.startsWith("select count(")  // 标准count
                || sql.startsWith("select count (*")  // count *
                || sql.matches("select\\s+count\\s*\\(.*\\).*from.*");  // 更通用的匹配
    }
    
    /**
     * 判断是否为分页查询
     */
    private boolean isPaginationQuery(MappedStatement ms, Object parameter, BoundSql boundSql) {
        // 方式1：检查RowBounds是否为默认值
        if (parameter instanceof RowBounds) {
            RowBounds rowBounds = (RowBounds) parameter;
            if (rowBounds.getOffset() != RowBounds.NO_ROW_OFFSET
                    || rowBounds.getLimit() != RowBounds.NO_ROW_LIMIT) {
                return true;
            }
        }

        // 方式2：检查SQL中是否包含分页关键字
        String sql = boundSql.getSql().toLowerCase();
        if (sql.contains(" limit ") || sql.contains(" offset ")) {
            return true;
        }

        // 方式3：检查是否使用PageHelper
        try {
            Class.forName("com.github.pagehelper.Page");
            if (com.github.pagehelper.PageHelper.getLocalPage() != null) {
                return true;
            }
        } catch (ClassNotFoundException e) {
            // PageHelper未引入，忽略
        }

        // 方式4：检查参数对象中是否包含分页参数
        if (parameter != null) {
            // 检查参数类是否包含分页相关字段
            Class<?> parameterClass = parameter.getClass();
            if (hasPageField(parameterClass)) {
                return true;
            }

            // 如果是Map类型，检查是否包含分页参数
            if (parameter instanceof Map) {
                Map<?, ?> paramMap = (Map<?, ?>) parameter;
                if (paramMap.containsKey("pageNum")
                        || paramMap.containsKey("pageSize")
                        || paramMap.containsKey("offset")
                        || paramMap.containsKey("limit")) {
                    return true;
                }
            }
        }

        return false;
    }

    /**
     * 检查类中是否包含分页相关字段
     */
    private boolean hasPageField(Class<?> clazz) {
        // 检查常见的分页字段名
        String[] pageFields = {"pageNum", "pageSize", "offset", "limit"};

        // 获取所有字段，包括父类的字段
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;
        while (currentClass != null && currentClass != Object.class) {
            fields.addAll(Arrays.asList(currentClass.getDeclaredFields()));
            currentClass = currentClass.getSuperclass();
        }

        // 检查是否包含分页字段
        for (Field field : fields) {
            if (Arrays.asList(pageFields).contains(field.getName())) {
                return true;
            }
        }

        return false;
    }

    private String getCountSql(BoundSql boundSql, Configuration configuration) {
        //获取最后可执行的sql 带入参的
        String execSql = getSqlWithValues(boundSql.getSql(), buildParameterValues(configuration, boundSql));

        //将可执行sql 自动生成 countSql
        CountSqlParser countSqlParser = new CountSqlParser();
        String countSql = countSqlParser.getSmartCountSql(execSql);
        return countSql;
    }

    public static BoundSql getBoundSql(MappedStatement mappedStatement, Object parameter) {
        return mappedStatement.getBoundSql(parameter);
    }

    protected int selectCount(Executor executor, String sql, MappedStatement ms, Configuration configuration) throws SQLException {
        try {
            // 使用现有的Executor执行count查询
            // 创建新的MappedStatement用于count查询
            MappedStatement.Builder builder = new MappedStatement.Builder(
                configuration,
                    ms.getId() + "_monitor_count",
                new StaticSqlSource(configuration, sql),
                SqlCommandType.SELECT
            );
            
            builder.resultMaps(Collections.singletonList(
                new ResultMap.Builder(configuration, "count",
                    Long.class, new ArrayList<>()).build()
            ));
            
            MappedStatement countMs = builder.build();
            
            // 使用executor执行查询
            List<Object> result = executor.query(
                countMs,
                null,
                RowBounds.DEFAULT,
                Executor.NO_RESULT_HANDLER
            );
            
            return result != null && !result.isEmpty() ? 
                   ((Number) result.get(0)).intValue() : 0;
                   
        } catch (Exception e) {
            log.error("执行count查询失败", e);
            return 0; // 失败时返回0，允许原查询继续执行
        }
    }

    private static Map<Integer, Object> buildParameterValues(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        // ParameterMapping描述参数，包括属性、名称、表达式、javaType、jdbcType、typeHandler等信息
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        if (parameterMappings != null) {
            Map<Integer, Object> parameterValues = new HashMap<>();
            //类型处理器用于注册所有的 TypeHandler，并建立 Jdbc 类型、JDBC 类型与 TypeHandler 之间的对应关系
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            for (int i = 0; i < parameterMappings.size(); i++) {
                ParameterMapping parameterMapping = parameterMappings.get(i);
                if (parameterMapping.getMode() != ParameterMode.OUT) {
                    Object value;
                    String propertyName = parameterMapping.getProperty();
                    if (boundSql.hasAdditionalParameter(propertyName)) { // issue #448 ask first for additional params
                        value = boundSql.getAdditionalParameter(propertyName);
                    } else if (parameterObject == null) {
                        value = null;
                    } else if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                        value = parameterObject;
                    } else {
                        MetaObject metaObject = configuration.newMetaObject(parameterObject);
                        value = metaObject.getValue(propertyName);
                    }
                    parameterValues.put(i, new Value(value));
                }
            }
            return parameterValues;
        }
        return Collections.emptyMap();
    }

    public static String getSqlWithValues(String statementQuery, Map<Integer, Object> parameterValues) {
        final StringBuilder sb = new StringBuilder();

        // iterate over the characters in the query replacing the parameter placeholders
        // with the actual values
        int currentParameter = 0;
        for (int pos = 0; pos < statementQuery.length(); pos++) {
            char character = statementQuery.charAt(pos);
            if (statementQuery.charAt(pos) == '?' && currentParameter <= parameterValues.size()) {
                // replace with parameter value
                Object value = parameterValues.get(currentParameter);
                sb.append(value != null ? value.toString() : new Value().toString());
                currentParameter++;
            } else {
                sb.append(character);
            }
        }

        return sb.toString();
    }

    /**
     * 获取方法上的注解
     */
    private DataSizeMonitor getAnnotation(String statementId) {
        return annotationCache.computeIfAbsent(statementId, key -> {
            try {
                // 解析Mapper接口和方法名
                String className = key.substring(0, key.lastIndexOf("."));
                String methodName = key.substring(key.lastIndexOf(".") + 1);

                // 加载Mapper接口类
                Class<?> mapperInterface = Class.forName(className);

                // 查找对应的方法
                Method[] methods = mapperInterface.getMethods();
                for (Method method : methods) {
                    if (method.getName().equals(methodName)) {
                        // 获取方法上的注解
                        return AnnotationUtils.findAnnotation(method, DataSizeMonitor.class);
                    }
                }
                return null;
            } catch (Exception e) {
                log.error("获取方法注解失败: {}", key, e);
                return null;
            }
        });
    }

    @Override
    public Object plugin(Object target) {
        return Plugin.wrap(target, this);
    }

    @Override
    public void setProperties(Properties properties) {
        Interceptor.super.setProperties(properties);
    }
}
