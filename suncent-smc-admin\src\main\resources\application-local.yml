spring:
  application:
    name: suncent-smc
  mvc:
    pathmatch:
      matching-strategy: ant_path_matcher
  data:
    mongodb:
      url: mongodb://root:2wsx#<EMAIL>:3717,dds-wz95ed425641eab42684-pub.mongodb.rds.aliyuncs.com:3717/competitive_products?authSource=admin
    mongodbAWS:
      url: mongodb://root:2wsx#<EMAIL>:3717,dds-wz95ed425641eab42684-pub.mongodb.rds.aliyuncs.com:3717/aws_product_details?authSource=admin
    mongodbSMC:
      url: mongodb://root:<EMAIL>:3717,dds-wz94ca4a8b9739942666-pub.mongodb.rds.aliyuncs.com:3717/suncent-mongo-smc?authSource=admin&replicaSet=mgset-74543744
      testUrl: mongodb://root:<EMAIL>:3717,dds-wz94ca4a8b9739942666-pub.mongodb.rds.aliyuncs.com:3717/suncent-mongo-smc-test?authSource=admin&replicaSet=mgset-74543744
      active: dev
    mongodbWebhook:
      url: mongodb://root:<EMAIL>:3717,dds-wz94ca4a8b9739942666-pub.mongodb.rds.aliyuncs.com:3717/suncent-webhook?authSource=admin&replicaSet=mgset-74543744
  redis:
    database: 1
    host: ************
    port: 6379
    password: Cwcnp7VSRKyL1GX!

    jedis:
      pool:
        max-active: 8
        max-wait: -1
        max-idle: 8
        min-idle: 0
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: com.mysql.cj.jdbc.Driver
    druid:
      master:
        url: *******************************************************************************************************************************************************************************
        username: root
        password: bF7vFFcijep8sL6v!
#        url: ***************************************************************************************************************************************************************************
#        username: root
#        password: a123456
      slave:
        enabled: true
        url: ********************************************************************************************************************************************************
        username: root
        password: bF7vFFcijep8sL6v!
      pdm:
        enabled: true
        url: ************************************************************************************************************************************************************************************************
        username: suncent_smc
        password: hGEUD8AiyKqAK3c
      ads:
        enabled: true
        url: *******************************************************************************************************************************************************************************************************************************
        username: it
        password: It@suncent123
      bi:
        enabled: true
        url: ************************************************************************************************************************************************
        username: smartbir
        password: Smartbir@123
      bi2:
        enabled: true
        url: ******************************************************************************************************************************************************************************
        username: suncent_mws_reptile
        password: sore2025Pw

      initialSize: 5
      minIdle: 10
      maxActive: 20
      maxWait: 60000
      timeBetweenEvictionRunsMillis: 60000
      minEvictableIdleTimeMillis: 300000
      maxEvictableIdleTimeMillis: 900000
      validationQuery: SELECT 1 FROM DUAL
      testWhileIdle: true
      testOnBorrow: false
      testOnReturn: false
      webStatFilter:
        enabled: true
      statViewServlet:
        enabled: true
        allow:
        url-pattern: /druid/*
        login-username: ruoyi
        login-password: 123456
      filter:
        stat:
          enabled: true
          log-slow-sql: true
          slow-sql-millis: 1000
          merge-sql: true
        wall:
          config:
            multi-statement-allow: true
  activemq:
    broker-url: tcp://192.168.75.7:61616
    user: suncent
    password: DihSzaUMUMhNQFAh
    close-timeout: 15s
    in-memory: true
    non-blocking-redelivery: false
    send-timeout: 0

aliyun:
  oss:
    endpoint: oss-cn-shenzhen.aliyuncs.com
    accessKeyId: LTAI5tMhnCysVequ1J9NzYyC
    accessKeySecret: ******************************
    defaultBucketName: suncent-cloud
    urlPrefix: http://oss.cloud.suncentgroup.com/
    suncentUrlPrefix: https://oss.cloud.suncentgroup.com/

sso:
  client:
    enable: true
    systemCode: ruoyi
    casPath: /sso/client/cas
  service:
    loginUrl: "http://test.cloud.suncentgroup.com/sso/server/login"
    checkTicketUrl: "http://test.cloud.suncentgroup.com/sso/server/check-ticket"

bi:
  competitor-url: http://120.78.196.95:8899/competitor
  rebate_sku_url: http://107edb3167124db888de6b2bf820d9f4-cn-shenzhen.alicloudapi.com/tmt_psr
  daily-sales-forecast-url: http://107edb3167124db888de6b2bf820d9f4-cn-shenzhen.alicloudapi.com/forecast_sales
sys-file:
  file-template-path: F:/suncent/product/template/
  file-template-path_new: F:/suncent/product/templateNew/
  file-template-path_new_en: F:/suncent/product/templateNewEn/
python:
  file-path: F:\suncent\product\template\
server:
  tomcat:
    max-http-form-post-size: 8MB
  error:
    include-binding-errors:

pdm:
  add_pdm_goods_status: https://pdm.cloud.suncentgroup.com/pdm/MappingGoods/createMapping
  add_update_pdm_goods_mapping_v2: https://pdm.cloud.suncentgroup.com/api/goodsMapping
  query_pdm_goods_mapping: https://pdm.cloud.suncentgroup.com/api/goodsMapping/queryDetail
  query_pdm_goods_mapping_list: https://pdm.cloud.suncentgroup.com/api/goodsMapping/query
  fix_pdm_goods_status: https://pdm.cloud.suncentgroup.com/pdm/MappingGoods/changeStatusByGoodsCode
  getPriceByGoodsCode: https://pdm.cloud.suncentgroup.com/api/goods/queryGoodsPrice
  pageQueryGoodsDetail: https://pdm.cloud.suncentgroup.com/api/goods/pageQueryGoodsDetail
  listPDMGoodsDetail: https://pdm.cloud.suncentgroup.com/api/goods/listGoodsDetail
  getDetail: https://pdm.cloud.suncentgroup.com/api/goods/getDetail
  getAllParentPartList: https://pdm.cloud.suncentgroup.com/api/goods/getAllParentPartList
  getAllMainGoodsCodes: https://pdm.cloud.suncentgroup.com/api/goods/getAllMainGoodsCodes
  getRedLinePriceByGoodsCode: https://pdm.cloud.suncentgroup.com/api/goods/queryRedLinePrice
  queryGoodsAttribute: https://pdm.cloud.suncentgroup.com/api/goods/queryGoodsAttribute
  queryGoodsCategoryAttribute: https://pdm.cloud.suncentgroup.com/api/goods/queryGoodsCategoryAttribute
  queryGoodsListInfo: https://pdm.cloud.suncentgroup.com/api/goods/queryGoodsListInfo
  execPythonUrl: https://pdm.cloud.suncentgroup.com/api/goods/execPython
  pageQueryGoodsDetailV2: https://pdm.cloud.suncentgroup.com/api/goods/pageQueryGoodsInfo
  updateGoodsBrandImage: https://pdm.cloud.suncentgroup.com/api/goods/updateGoodsBrandImage
xxl:
  job:
    admin:
      addresses: http://127.0.0.1:8080/xxl-job-admin
    accessToken:
    executor:
      appname: suncent-smc-yiki
      address:
      ip:
      port: 18091
      logpath: /data/applogs/${ruoyi.name}/
      logretentiondays: 30

api:
  amazon-vc-ip: http://127.0.0.1:9011
  amazon-sc-ip: http://127.0.0.1:9002
  amazon-update-v2-url: /order/submit/partiallyUpdateListingByJSON
  amazon-delete-v2-url: /listing/deleteListingJSONBySku
  amazon-select-v2-url: /listing/getListingsItemBySku
  amazon-upload-v2-url: /listing/listingJSON
  amazon-vc-detail-v2-url: /listing/searchListingsItems
  amazon-front-detail-url: /listing/searchCatalogItems
  amazon-vc-upload-image: /APlus/uploadImageToAmazon
  amazon-real-time-sales-report-url: /listing/listRealTimeSalesReport
  amazon-category-get-attribute-url: /feed/category/getAttributeByProductType
  amazon-category-get-category-info-url: /feed/category/getCategoryInfoByAmazonCategoryId
  amazon-vc-all-listing-url: /listing/listAmazonListing
  submit-inventory-update-url: http://127.0.0.1:9011/vcInventory/submitInventoryUpdate
  get-transaction-status-call-url: http://127.0.0.1:9011/vcInventory/getTransactionStatusCall
  amazon-vc-detail-url: http://*************:9011/feed/info/pullItemByAsin
  amazon-upload-listing-url: http://172.29.189.1:9002/amazon/order/submit/listing
  amazon-upload-listing-submitted-result-url: http://172.29.189.1:9002/amazon/feed/info/getFileSubmittedResult
  amazon-update-price-url: http://127.0.0.1:9011/amazon/order/submit/listingUpdatePrice
  amazon-update-json-listing-url: http://127.0.0.1:9011/amazon/order/submit/partiallyUpdateListingByJSON
  amazon-listing-url: http://127.0.0.1:9011/amazon/listing/getListingsItem
  amazon-sellerCode-allAsin-url: http://127.0.0.1:9011/amazon/feed/info/getAllAsinBySellerCode
  amazon-sellerCode-allAsin-v2-url: http://************:9002/amazon/feed/info/v2/getAllAsinBySellerCode
  amazon-detail-url: http://************:9002/amazon/feed/info/pullItemByAsin
  amazon-update-inventory-url: http://127.0.0.1:9011/amazon/order/submit/listingUpdateInventory
  amazon-aplus-list-url: http://127.0.0.1:9011/amazon/APlus/searchContentDocuments
  amazon-aplus-description-url: http://127.0.0.1:9011/amazon/APlus/getContentDocumentByContentReferenceKey
  amazon-aplus-relations-asin-url: http://127.0.0.1:9011/amazon/APlus/getRelationsAsinByContentReferenceKey
  amazon-aplus-publish-url: http://127.0.0.1:9011/amazon/APlus/updateAsinRelations
  amazon-aplus-create-doc: http://127.0.0.1:9011/amazon/APlus/createContentDocument
  amazon-aplus-update-doc: http://127.0.0.1:9011/amazon/APlus/updateContentDocument
  amazon-aplus-upload-image: http://127.0.0.1:9011/amazon/APlus/uploadImageToAmazon
  amazon-aplus-get-by-asin-url: http://127.0.0.1:9011/amazon/APlus/getContentDocumentByAsin
  amazon-aplus-stop-url: http://127.0.0.1:9011/amazon/APlus/stopContentDocument
  amazon-getTodayVCDFHighCost-url: http://127.0.0.1:9011/vcInventory/getTodayVCDFHighCost
  thirdparty-out-stock-url: http://***********:9015/thirdparty/inventory/getOutOfStockSku
  ebay-all-listingItemId-url: http://localhost:9012/listing/getListingByAccountCode
  ebay-updateSellerListingHandleStatus-url: http://localhost:9012/listing/updateSellerListingHandleStatus
  ebay-getCompatibilityProperties-url: http://localhost:9012/category/getCompatibilityProperties/{accountCode}/{categoryTreeId}/{categoryId}
  ebay-getItemAspectsByCategoryIds-url: http://localhost:9012/category/getItemAspectsByCategoryIds
  ebay-getItem-url: http://localhost:9012/item/getItem/{accountCode}/{itemId}
  ebay-verifyAddItem-url: http://localhost:9012/item/{accountCode}/verifyAddItem
  ebay-addItem-url: http://localhost:9012/item/{accountCode}/addItem
  ebay-fixItem-url: http://localhost:9012/item/{accountCode}/fixItem
  ebay-endItem-url: http://localhost:9012/item/{accountCode}/endFixedPriceItem/{itemId}
  ebay-relistItem-url: http://localhost:9012/item/{accountCode}/relistFixedPriceItem/{itemId}
  ebay-reviseInventoryStatus-url: http://localhost:9012/item/reviseInventoryStatus
  ebay-getSellerEvents-url: http://localhost:9012/item/getSellerEvents
  ebay-getStore-url: http://************:9012/item/getStore
  ebay-uploadEpsImage-url: http://localhost:9012/item/uploadEpsImage
  ebay-getItemCategory-url: http://************:9012/category//getItemCategory/{site}
  ebay-getCategoryVariationsEnabled-url: http://127.0.0.1:9012/category/getCategoryVariationsEnabled/{accountCode}/{categoryId}/{siteCode}
  thirdparty-fbm-stock-url: http://************:9015/thirdparty/inventory/getFbmStock
  thirdparty-getStockByCountry-url: http://************:9015/thirdparty/inventory/getStockByCountry
  getInventorySku: http://************:9015/thirdparty/inventory/getInventorySku
  thirdparty-token: eyJUeXBlIjoiSnd0IiwidHlwIjoiSldUIiwiYWxnIjoiSFMyNTYifQ.*******************************************************************************************************.ijRNzeFqMH6z916uISp9a6j3Qmob6W6OFu4ZhQhAjB8
  temu-listing-url: http://127.0.0.1:9999/item/getGoodsList
  temu-get-quantity-url: http://127.0.0.1:9999/item/getGoodsQuantity/{shopCode}/{productSkcId}
  temu-update-quantity-url: http://127.0.0.1:9999/item/updateGoodsQuantity
  temu-getGoodsCats-url: http://127.0.0.1:9999/item/getGoodsCats
  temu-getGoodsAttrs-url: http://127.0.0.1:9999/item/getGoodsAttrs
  temu-getGoodsMainAttrs-url: http://127.0.0.1:9999/item/getGoodsMainAttrs
  temu-getGoodsBrand-url: http://127.0.0.1:9999/item/getGoodsBrand
  temu-getTemplates-url: http://127.0.0.1:9999/item/getTemplates
  temu-uploadGoodsImage-url: http://127.0.0.1:9999/item/uploadGoodsImage
  temu-textToImage-url: http://127.0.0.1:9999/item/textToImage
  temu-getWarehouse-url: http://127.0.0.1:9999/item/getGoodsWarehouse
  temu-addGoods-url: http://127.0.0.1:9999/item/addGoods
  temu-edit-sensitive-attr-url: http://127.0.0.1:9999/item/editSensitiveAttr
  temu-createGoodsSpec-url: http://127.0.0.1:9999/item/createGoodsSpec
  ebay-createVideo-url: http://127.0.0.1:9012/item/createVideo
  ebay-uploadVideo-url: http://127.0.0.1:9012/item/uploadVideo
  ebay-getVideo-url: http://127.0.0.1:9012/item/getVideo
  temu-uploadGoodsImage-url-v2: http://127.0.0.1:9999/item/uploadGoodsImageV2
  # OMS API配置
  oms-base-url: https://oms.cloud.suncentgroup.com/
  oms-secret-key: OMS_API_SECRETKEY
  # Ebay内容生成API配置
  ebay-content-generation-url: https://u242675-a03f-da8b7148.westc.gpuhub.com:8443
  ebay-content-generation-token: 123789
  # VC测价工具接口配置
  vc-calculation-base-url: http://120.78.147.42:8977
  vc-calculation-query-url: /get_table_data_1
  vc-calculation-app-code: smc920a45cbb14a9bibf869f57a21fde



oms:
  get_shopCode_url: https://oms.cloud.suncentgroup.com/api/getPermissionsByUserId/
  get_user_url: https://oms.cloud.suncentgroup.com/api/getPermissionsByShopCode/
  get_vcdf_inventory_black_url: https://oms.cloud.suncentgroup.com/api/vcDfInventory/getVcDfInventoryBlacklistRule
  get_warehouse_url: https://oms.cloud.suncentgroup.com/api/warehouse/getWarehouse

suncent-platform-inventory:
  update-zero-inventory-url: http://127.0.0.1:18080/amazon/inventory/updateZeroInventory
  update-restock-inventory-url: http://127.0.0.1:18080/amazon/inventory/updateRestockInventory

data:
  get_price_count_url: http://39.108.54.61:8011/bj

cloud:
  api:
    accessSecret: AJY6iob0OFuZPcSy
    syncCloudDeptUrl: "https://cloud.suncentgroup.com/api/system/dept/list"
    syncCloudUserUrl: "https://cloud.suncentgroup.com/api/system/user/list"

image:
  single-url-upload: http://************:8001/image/single/linkChange

liteflow:
  rule-source: config/flow.el.xml
  print-banner: false

autolisting:
  InventoryLine: 25
  itShopList: US25,IT1
  ebskuList:
  amskuList: KX4WBK09100
  maxShopListing: 1

# 亚马逊流水线并行处理配置
amazon:
  pipeline:
    # 是否启用流水线处理（默认关闭，需要测试验证后启用）
    enabled: false
    # 是否启用详细监控
    monitoring-enabled: true
    # 图片上传超时时间（分钟）
    timeout-minutes: 5
    # 批量处理大小
    batch-size: 20
    # 是否启用降级处理
    fallback-enabled: true

dingDing:
  agentId: 1206025128
  appKey: dingaqx7pnj6taw3r3yh
  appSecret: YfZaNfhsBx57WsOk4Pa7gD_2E8WVJKGjlYsUVxW35y5toUMeLrlxyh7OblALehKr
  tokenCachePrefix: "dingding:token:"
  tokenCacheTimeout: 7200
  tokenUrl: https://oapi.dingtalk.com/gettoken
  asyncSendUrl: https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2
  defaultDingTalkClient: https://oapi.dingtalk.com/user/get

noticfication:
  url: "http://127.0.0.1/system/node?"

chat-gpt:
  url: https://suncent-openai4-01.openai.azure.com/openai/deployments/gpt-4-32k/chat/completions?api-version=2023-05-15
  key: fcee2ffd18804e599ed7f699db003592

word:
  preview:
    default:
      brandCode: Sealight
      categoryId: 109
      goodsCode: KX1ALT00200U1


rocketmq:
  enhance:
    enabledIsolation: false
    environment: dev
  name-server: ************:9876
  consumer:
    pull-batch-size: 10
    access-key: dI1Cu1f7KN379XPW
    secret-key: a56T3gl6aFemFn6x
  producer:
    group: suncent-smc-group
    send-message-timeout: 3000
    retry-next-server: false
    access-key: dI1Cu1f7KN379XPW
    secret-key: a56T3gl6aFemFn6x
jetcache:
  statIntervalMinutes: 60
  areaInCacheName: false
  local:
    default:
      type: caffeine
      keyConvertor: fastjson
      limit: 100
      expireAfterWriteInMillis: 600000
      expireAfterAccessInMillis: 300000
  remote:
    default:
      type: redis.springdata
      keyPrefix: 'smc:jetcache:'
      keyConvertor: fastjson
      valueEncoder: java
      valueDecoder: java
      expireAfterWriteInMillis: 3600000
      poolConfig:
        minIdle: 5
        maxIdle: 20
        maxTotal: 50
      host: ${spring.redis.host}
      port: ${spring.redis.port}
      database: ${spring.redis.database}
      password: ${spring.redis.password}


monitor:
  thread-pool:
    executors[0]:
      pool-name: temuPushPool
      core-pool-size: 4
      max-pool-size: 8
      queueCapacity: 1024
      queueType: LinkedBlockingQueue
      rejectedHandlerType: ABORT_POLICY
      keepAliveTime: 60
      unit: SECONDS
    executors[1]:
      pool-name: editAdaptivePool
      core-pool-size: 6
      max-pool-size: 6
      queueCapacity: 2000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
      keepAliveTime: 1
      unit: MILLISECONDS
    executors[2]:
      pool-name: shipTemplateSync
      core-pool-size: 2
      max-pool-size: 8
      queueCapacity: 2000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
      keepAliveTime: 0
      unit: MILLISECONDS
    executors[3]:
      pool-name: listingUpdatePool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[4]:
      pool-name: syncAndUpdateEbayListingPool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[5]:
      pool-name: listingLabelTaskPool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[6]:
      pool-name: lostCartTodoResolverPool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[7]:
      pool-name: inventoryLowTodoResolverPool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[8]:
      pool-name: syncAndUpdateAmazonListingPool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[9]:
      pool-name: amazon-push-scheduled-upload
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 100
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[10]:
      pool-name: amazon-push-scheduled-update
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 100
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[11]:
      pool-name: inventory-consumer-pool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 4000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[12]:
      pool-name: inventory-oversold-task-pool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 4000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[13]:
      pool-name: amazon-image-process
      core-pool-size: 8
      max-pool-size: 16
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 500
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[14]:
      pool-name: amazon-asin-callback
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 200
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[15]:
      pool-name: amListingUpdatePool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[16]:
      pool-name: inventoryCalibrationPool
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 10
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: ABORT_POLICY
    executors[17]:
      pool-name: eidtPoolConfig
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 120
      unit: SECONDS
      queueCapacity: 2000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[18]:
      pool-name: amazon-push-upload
      core-pool-size: 4
      max-pool-size: 8
      keepAliveTime: 120
      unit: SECONDS
      queueCapacity: 5000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[19]:
      pool-name: amazon-pipeline-listing
      core-pool-size: 6
      max-pool-size: 12
      keepAliveTime: 60
      unit: SECONDS
      queueCapacity: 200
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[20]:
      pool-name: amazon-listing-shop-concurrent
      core-pool-size: 4
      max-pool-size: 4
      keepAliveTime: 60
      unit: SECONDS
      queueCapacity: 100
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[21]:
      pool-name: amazon-part-image-process
      core-pool-size: 10
      max-pool-size: 20
      keepAliveTime: 120
      unit: SECONDS
      queueCapacity: 2000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
    executors[22]:
      pool-name: part-listing-update
      core-pool-size: 8
      max-pool-size: 20
      keepAliveTime: 120
      unit: SECONDS
      queueCapacity: 2000
      queueType: ArrayBlockingQueue
      rejectedHandlerType: CALLER_RUNS_POLICY
# 速率限制配置
suncent:
  rate-limit:
    rateLimitConfigs:
      # 亚马逊图片上传速率限制 - 每秒10个请求
      - rateLimiterName: amazon-image-upload
        limitForPeriod: 10
        refreshPeriodSeconds: 1
        timeoutSeconds: 15

      # 亚马逊产品上传速率限制 - 每秒5个请求
      - rateLimiterName: amazon-push-scheduled-upload
        limitForPeriod: 5
        refreshPeriodSeconds: 1
        timeoutSeconds: 15

      # 亚马逊产品更新速率限制 - 每秒5个请求
      - rateLimiterName: amazon-push-scheduled-update
        limitForPeriod: 5
        refreshPeriodSeconds: 1
        timeoutSeconds: 15
  mongodb:
    # MongoDB连接池配置 - 解决PowerOfTwoBufferPool内存问题
    # 适用于所有MongoDB数据源（primary、AWS、SMC、Webhook）
    # 保持MongoDB驱动默认值，减少对现有业务的影响，只启用NettyStreamFactory
    connection-pool:
      enable-netty-stream-factory: true         # 启用NettyStreamFactory解决内存问题
      allocator-type: UNPOOLED                  # Netty内存分配器：UNPOOLED(安全，由JVM管理内存) / POOLED(高性能，需配置JVM，不依赖JVM)

tess4j:
  dataPath: "F:\\tessdata-main"
