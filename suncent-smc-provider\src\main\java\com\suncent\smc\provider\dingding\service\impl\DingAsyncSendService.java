package com.suncent.smc.provider.dingding.service.impl;

import com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskRequest;
import com.aliyun.teautil.models.RuntimeOptions;
import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.request.OapiUserGetRequest;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.dingtalk.api.response.OapiUserGetResponse;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.provider.dingding.domain.ActionCardMsgDto;
import com.suncent.smc.provider.dingding.domain.TODOTaskCardMsgDto;
import com.suncent.smc.provider.dingding.service.IDingAsyncSendService;
import com.suncent.smc.provider.dingding.service.IDingTokenService;
import com.suncent.smc.provider.dingding.util.PageLinkHelper;
import com.taobao.api.ApiException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.connection.ConnectionUtils;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/8/16 14:21
 * @Version 1.0
 */
@Service
@Slf4j
public class DingAsyncSendService implements IDingAsyncSendService {
    @Value("${dingDing.asyncSendUrl}")
    public String dingDingAsyncSendUrl;

    @Value("${dingDing.agentId}")
    public Long dingDingAgentId;
    
    @Autowired
    public IDingTokenService tokenService;
    @Value("${dingDing.defaultDingTalkClient}")
    public String defaultDingTalkClient;
    @Override
    public String sendTOdoTask(TODOTaskCardMsgDto todoTaskCardMsgDto) {
        try {
            String accessToken = tokenService.getAccessToken();

            com.aliyun.dingtalktodo_1_0.Client client = createClient();

            List<String> executorCodes = todoTaskCardMsgDto.getExecutorCodes();

            List<String> executorIds = buildExecutorIds(accessToken, executorCodes);
            if (CollectionUtils.isNotEmpty(executorIds)) {
                com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskHeaders createTodoTaskHeaders = new com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskHeaders();

                createTodoTaskHeaders.xAcsDingtalkAccessToken = accessToken;

                CreateTodoTaskRequest createTodoTaskRequest = buildData(todoTaskCardMsgDto, executorIds);

              client.createTodoTaskWithOptions(executorIds.get(0), createTodoTaskRequest, createTodoTaskHeaders, new RuntimeOptions());

            }
        } catch (Exception e) {
            log.error("钉钉待办消息推送失败,失败原因:" + e.getMessage());
        }
        return null;
    }

    private com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskRequest buildData(TODOTaskCardMsgDto todoTaskCardMsgDto, List<String> executorIds) {
        com.aliyun.dingtalktodo_1_0.models.CreateTodoTaskRequest todoTaskRequest = new CreateTodoTaskRequest();
        CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs notifyConfigs = new CreateTodoTaskRequest.CreateTodoTaskRequestNotifyConfigs()
                .setDingNotify("1");
        CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl createTodoTaskRequestDetailUrl = new CreateTodoTaskRequest.CreateTodoTaskRequestDetailUrl()
                .setAppUrl(todoTaskCardMsgDto.getAppUrl())
                .setPcUrl(todoTaskCardMsgDto.getPcUrl());

        todoTaskRequest
                .setSubject(todoTaskCardMsgDto.getTitle())
                .setDescription(todoTaskCardMsgDto.getDescription())
                .setExecutorIds(executorIds)
                .setDetailUrl(createTodoTaskRequestDetailUrl)
                .setNotifyConfigs(notifyConfigs);
        return todoTaskRequest;
    }


    public com.aliyun.dingtalktodo_1_0.Client createClient() throws Exception {
        com.aliyun.teaopenapi.models.Config config = new com.aliyun.teaopenapi.models.Config();
        config.protocol = "https";
        config.regionId = "central";
        return new com.aliyun.dingtalktodo_1_0.Client(config);
    }
    List<String> buildExecutorIds(String accessToken, List<String> userCodes) {
        List<String> executorIds = new ArrayList<>();
        try {
            if (CollectionUtils.isNotEmpty(userCodes)) {
                DingTalkClient client = new DefaultDingTalkClient(defaultDingTalkClient);
                OapiUserGetRequest req = new OapiUserGetRequest();
                for (String userCode : userCodes) {
                    req.setUserid(userCode);
                    req.setHttpMethod("GET");
                    OapiUserGetResponse rsp = client.execute(req, accessToken);
                    if (Objects.nonNull(rsp)) {
                        executorIds.add(rsp.getUnionid());
                    }
                }
            }
        } catch (ApiException e) {
            log.error("获取钉钉用户信息失败,失败原因:" + e.getMessage());
            throw new BusinessException(e.getMessage());
        }
        if (CollectionUtils.isEmpty(executorIds)) {
            log.warn("本次获取用户id为空");
        }
        return executorIds;
    }

    /**
     * 异步发送消息
     *
     * @return
     */
    @Override
    public void asyncSend(ActionCardMsgDto actionCardMsgDto) {
        if (StringUtils.isEmpty(actionCardMsgDto.getTargetDingUserId())) {
            throw new BusinessException("钉钉用户编号为空");
        }

        DingTalkClient client = tokenService.buildClient(dingDingAsyncSendUrl);
        sendActionCardMsg(client, actionCardMsgDto);
    }


    @Override
    public void asyncSendDefault(ActionCardMsgDto actionCardMsgDto) {
        if (StringUtils.isEmpty(actionCardMsgDto.getTargetDingUserId())) {
            throw new BusinessException("钉钉用户编号为空");
        }

        DingTalkClient client = tokenService.buildClient(dingDingAsyncSendUrl);
        sendActionCardMsgDefault(client, actionCardMsgDto);
    }


    public void sendActionCardMsgDefault(DingTalkClient client, ActionCardMsgDto actionCardMsgDto) {
        try {
            String accessToken = tokenService.getAccessToken();

            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(dingDingAgentId);
            request.setToAllUser(false);
            request.setUseridList(actionCardMsgDto.getTargetDingUserId());

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
            msg.setMsgtype("action_card");

            //是透出到会话列表和通知的文案
            msg.getActionCard().setTitle(actionCardMsgDto.getMessageTitle());
            //支持markdown格式的正文内容
            msg.getActionCard().setMarkdown(actionCardMsgDto.getMessageContent());


            //自定义按钮列表
            List<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList> btnJsonLists = new ArrayList<>();
            OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList btnJsonList = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
            btnJsonList.setTitle("点击处理");
            btnJsonList.setActionUrl(actionCardMsgDto.getMessageUrl());
            btnJsonLists.add(btnJsonList);
            msg.getActionCard().setBtnJsonList(btnJsonLists);
            msg.getActionCard().setBtnOrientation("0");

//            //卡片菜单
//            msg.getActionCard().setSingleTitle("点击处理");
//            //调整链接
//            msg.getActionCard().setSingleUrl(PageLinkHelper.openBrowser(actionCardMsgDto.getMessageUrl()));


            request.setMsg(msg);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, accessToken);
            if (rsp == null) {
                throw new BusinessException("未获取到返回信息");
            }
            if (!rsp.isSuccess()) {
                throw new BusinessException(rsp.getErrmsg());
            }
        } catch (ApiException e) {
            throw new BusinessException(e.getErrMsg());
        }

    }

    public void sendActionCardMsg(DingTalkClient client, ActionCardMsgDto actionCardMsgDto) {
        try {
            String accessToken = tokenService.getAccessToken();

            OapiMessageCorpconversationAsyncsendV2Request request = new OapiMessageCorpconversationAsyncsendV2Request();
            request.setAgentId(dingDingAgentId);
            request.setToAllUser(false);
            request.setUseridList(actionCardMsgDto.getTargetDingUserId());

            OapiMessageCorpconversationAsyncsendV2Request.Msg msg = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            msg.setActionCard(new OapiMessageCorpconversationAsyncsendV2Request.ActionCard());
            msg.setMsgtype("action_card");

            //是透出到会话列表和通知的文案
            msg.getActionCard().setTitle(actionCardMsgDto.getMessageTitle());
            //支持markdown格式的正文内容
            msg.getActionCard().setMarkdown(actionCardMsgDto.getMessageContent());


            //自定义按钮列表
            List<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList> btnJsonLists = new ArrayList<>();
            OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList btnJsonList = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
            btnJsonList.setTitle("点击处理");
            btnJsonList.setActionUrl(PageLinkHelper.openBrowser(actionCardMsgDto.getMessageUrl()));
            btnJsonLists.add(btnJsonList);
            msg.getActionCard().setBtnJsonList(btnJsonLists);
            msg.getActionCard().setBtnOrientation("0");

//            //卡片菜单
//            msg.getActionCard().setSingleTitle("点击处理");
//            //调整链接
//            msg.getActionCard().setSingleUrl(PageLinkHelper.openBrowser(actionCardMsgDto.getMessageUrl()));


            request.setMsg(msg);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(request, accessToken);
            if (rsp == null) {
                throw new BusinessException("未获取到返回信息");
            }
            if (!rsp.isSuccess()) {
                throw new BusinessException(rsp.getErrmsg());
            }
        } catch (ApiException e) {
            throw new BusinessException(e.getErrMsg());
        }

    }

}
