package com.suncent.smc.common.utils;

import cn.hutool.core.date.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.springframework.util.CollectionUtils;

import java.lang.management.ManagementFactory;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 时间工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class DateUtils extends org.apache.commons.lang3.time.DateUtils
{
    public static String YYYY = "yyyy";

    public static String YYYY_MM = "yyyy-MM";

    public static String YYYY_MM_DD = "yyyy-MM-dd";

    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";

    public static String YYYY_MM_DD_HH_MM = "yyyy-MM-dd HH:mm";

    private static String[] parsePatterns = {
            "yyyy-MM-dd", "yyyy-MM-dd HH:mm:ss", "yyyy-MM-dd HH:mm", "yyyy-MM", 
            "yyyy/MM/dd", "yyyy/MM/dd HH:mm:ss", "yyyy/MM/dd HH:mm", "yyyy/MM",
            "yyyy.MM.dd", "yyyy.MM.dd HH:mm:ss", "yyyy.MM.dd HH:mm", "yyyy.MM"};

    /**
     * 获取当前Date型日期
     * 
     * @return Date() 当前日期
     */
    public static Date getNowDate()
    {
        return new Date();
    }

    /**
     * 获取当前日期, 默认格式为yyyy-MM-dd
     * 
     * @return String
     */
    public static String getDate()
    {
        return dateTimeNow(YYYY_MM_DD);
    }

    public static final String getTime()
    {
        return dateTimeNow(YYYY_MM_DD_HH_MM_SS);
    }

    public static final String dateTimeNow()
    {
        return dateTimeNow(YYYYMMDDHHMMSS);
    }

    public static final String dateTimeNow(final String format)
    {
        return parseDateToStr(format, new Date());
    }

    public static final String dateTime(final Date date)
    {
        return parseDateToStr(YYYY_MM_DD, date);
    }

    public static final String parseDateToStr(final String format, final Date date)
    {
        return new SimpleDateFormat(format).format(date);
    }

    public static final Date dateTime(final String format, final String ts)
    {
        try
        {
            return new SimpleDateFormat(format).parse(ts);
        }
        catch (ParseException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * 日期路径 即年/月/日 如2018/08/08
     */
    public static final String datePath()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyy/MM/dd");
    }

    /**
     * 日期路径 即年/月/日 如20180808
     */
    public static final String dateTime()
    {
        Date now = new Date();
        return DateFormatUtils.format(now, "yyyyMMdd");
    }

    /**
     * 日期型字符串转化为日期 格式
     */
    public static Date parseDate(Object str)
    {
        if (str == null)
        {
            return null;
        }
        try
        {
            return parseDate(str.toString(), parsePatterns);
        }
        catch (ParseException e)
        {
            return null;
        }
    }

    /**
     * 获取服务器启动时间
     */
    public static Date getServerStartDate()
    {
        long time = ManagementFactory.getRuntimeMXBean().getStartTime();
        return new Date(time);
    }

    /**
     * 计算相差天数
     */
    public static int differentDaysByMillisecond(Date date1, Date date2)
    {
        return Math.abs((int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24)));
    }

    /**
     * 计算两个时间差
     */
    public static String getDatePoor(Date endDate, Date nowDate)
    {
        long nd = 1000 * 24 * 60 * 60;
        long nh = 1000 * 60 * 60;
        long nm = 1000 * 60;
        // long ns = 1000;
        // 获得两个时间的毫秒时间差异
        long diff = endDate.getTime() - nowDate.getTime();
        // 计算差多少天
        long day = diff / nd;
        // 计算差多少小时
        long hour = diff % nd / nh;
        // 计算差多少分钟
        long min = diff % nd % nh / nm;
        // 计算差多少秒//输出结果
        // long sec = diff % nd % nh % nm / ns;
        return day + "天" + hour + "小时" + min + "分钟";
    }

    /**
     * 计算两个时间差  小时数
     */
    public static int getDifferHour(Date startDate, Date endDate) {
        long dayM = 1000 * 24 * 60 * 60;
        long hourM = 1000 * 60 * 60;
        long differ = endDate.getTime() - startDate.getTime();
        long hour = differ % dayM / hourM;
        return Integer.parseInt(String.valueOf(hour));
    }

    /**
     * 增加 LocalDateTime ==> Date
     */
    public static Date toDate(LocalDateTime temporalAccessor)
    {
        ZonedDateTime zdt = temporalAccessor.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    /**
     * 增加 LocalDate ==> Date
     */
    public static Date toDate(LocalDate temporalAccessor)
    {
        LocalDateTime localDateTime = LocalDateTime.of(temporalAccessor, LocalTime.of(0, 0, 0));
        ZonedDateTime zdt = localDateTime.atZone(ZoneId.systemDefault());
        return Date.from(zdt.toInstant());
    }

    public static String getYesterday() {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        return new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
    }

    /**
     * 获取时间戳戳是星期几
     *
     * @param date
     * @return 当前日期是星期几
     */
    public static String getWeekOfDate(Date date) {
        String[] weekDays = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        Calendar cal = Calendar.getInstance();
        cal.setTime(date);
        int w = cal.get(Calendar.DAY_OF_WEEK) - 1;
        if (w < 0)
            w = 0;
//        log.info("{} {} {} ", cal.get(Calendar.DAY_OF_WEEK), w, weekDays[w]);
        return weekDays[w];
    }


    /**
     * 获取昨天的hourBegin时间
     * 比如 hourBegin 为10   则返回昨天的10点
     * @param hourBegin
     * @return
     */
    public static Date getYesterdayBegin(String hourBegin) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -1);
        String yesterday = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        String yesterdayBegin = yesterday + " " + hourBegin + ":00:00";
        return dateTime(YYYY_MM_DD_HH_MM_SS, yesterdayBegin);
    }


    public static Date getTodayDayEnd(String hour) {
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 0);
        String today = new SimpleDateFormat("yyyy-MM-dd").format(cal.getTime());
        String todayEnd = today + " " + hour + ":00:00";
        return dateTime(YYYY_MM_DD_HH_MM_SS, todayEnd);
    }

    public static String getNowTime() {
        return new SimpleDateFormat("HH:mm:ss").format(new Date());
    }

    // 获取当前时间的分钟数
    public static int getNowMinute() {
        return Integer.parseInt(new SimpleDateFormat("mm").format(new Date()));
    }

    /**
     * 将UTC时间转换为CST时间
     * @param expirationDate 2024-08-02T06:27:59Z
     * @return
     */
    public static String UTC2CST(String expirationDate) {

        DateTimeFormatter formatter = DateTimeFormatter.ISO_DATE_TIME;
        ZonedDateTime utcDateTime = ZonedDateTime.parse(expirationDate, formatter.withZone(java.time.ZoneId.of("UTC")));
        ZonedDateTime cstDateTime = utcDateTime.withZoneSameInstant(java.time.ZoneId.of("Asia/Shanghai"));
        String cstDateString = cstDateTime.format(formatter);
        ZonedDateTime zonedDateTime = ZonedDateTime.parse(cstDateString, DateTimeFormatter.ISO_ZONED_DATE_TIME);
        return  zonedDateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
    }


    /**
     * 时区缩写映射表 - 静态初始化，避免重复创建
     */
    private static final Map<String, ZoneId> TIMEZONE_ABBREVIATION_MAP = new HashMap<>();

    public static void main(String[] args) {
        Date date = convertTimeZoneToChinaStandardTime("10/07/2023 11:21:05 AEST");
        Date date2 = convertTimeZoneToChinaStandardTime("10/09/2024 05:27:54 MEST");
        Date date3 = convertTimeZoneToChinaStandardTime("10/09/2024 10:25:24 BST");
        Date date4 = convertTimeZoneToChinaStandardTime("08/11/2024 10:59:10 MET");
        Date date5 = convertTimeZoneToChinaStandardTime("08/10/2024 05:39:30 BST");
        System.out.println(DateUtil.formatDateTime(date));
        System.out.println(DateUtil.formatDateTime(date2));
        System.out.println(DateUtil.formatDateTime(date3));
        System.out.println(DateUtil.formatDateTime(date4));
        System.out.println(DateUtil.formatDateTime(date5));
    }
    static {
        // 英国时区
        TIMEZONE_ABBREVIATION_MAP.put("BST", ZoneId.of("Europe/London")); // 英国夏令时

        // 美国时区
        TIMEZONE_ABBREVIATION_MAP.put("EST", ZoneId.of("America/New_York")); // 美国东部标准时间
        TIMEZONE_ABBREVIATION_MAP.put("EDT", ZoneId.of("America/New_York")); // 美国东部夏令时
        TIMEZONE_ABBREVIATION_MAP.put("CST", ZoneId.of("America/Chicago")); // 美国中部标准时间
        TIMEZONE_ABBREVIATION_MAP.put("CDT", ZoneId.of("America/Chicago")); // 美国中部夏令时
        TIMEZONE_ABBREVIATION_MAP.put("MST", ZoneId.of("America/Denver")); // 美国山地标准时间
        TIMEZONE_ABBREVIATION_MAP.put("MDT", ZoneId.of("America/Denver")); // 美国山地夏令时
        TIMEZONE_ABBREVIATION_MAP.put("PST", ZoneId.of("America/Los_Angeles")); // 美国太平洋标准时间
        TIMEZONE_ABBREVIATION_MAP.put("PDT", ZoneId.of("America/Los_Angeles")); // 美国太平洋夏令时

        // 欧洲时区
        TIMEZONE_ABBREVIATION_MAP.put("CET", ZoneId.of("Europe/Paris")); // 中欧标准时间
        TIMEZONE_ABBREVIATION_MAP.put("CEST", ZoneId.of("Europe/Paris")); // 中欧夏令时
        TIMEZONE_ABBREVIATION_MAP.put("EET", ZoneId.of("Europe/Athens")); // 东欧标准时间
        TIMEZONE_ABBREVIATION_MAP.put("EEST", ZoneId.of("Europe/Athens")); // 东欧夏令时
        TIMEZONE_ABBREVIATION_MAP.put("WET", ZoneId.of("Europe/Lisbon")); // 西欧标准时间
        TIMEZONE_ABBREVIATION_MAP.put("WEST", ZoneId.of("Europe/Lisbon")); // 西欧夏令时
        TIMEZONE_ABBREVIATION_MAP.put("MEST", ZoneId.of("Europe/Paris")); // 中欧夏令时（另一种表示）

        // 澳大利亚时区
        TIMEZONE_ABBREVIATION_MAP.put("AEST", ZoneId.of("Australia/Sydney")); // 澳大利亚东部标准时间
        TIMEZONE_ABBREVIATION_MAP.put("AEDT", ZoneId.of("Australia/Sydney")); // 澳大利亚东部夏令时
        TIMEZONE_ABBREVIATION_MAP.put("ACST", ZoneId.of("Australia/Adelaide")); // 澳大利亚中部标准时间
        TIMEZONE_ABBREVIATION_MAP.put("ACDT", ZoneId.of("Australia/Adelaide")); // 澳大利亚中部夏令时
        TIMEZONE_ABBREVIATION_MAP.put("AWST", ZoneId.of("Australia/Perth")); // 澳大利亚西部标准时间

        // 亚洲时区
        TIMEZONE_ABBREVIATION_MAP.put("JST", ZoneId.of("Asia/Tokyo")); // 日本标准时间
        TIMEZONE_ABBREVIATION_MAP.put("KST", ZoneId.of("Asia/Seoul")); // 韩国标准时间
        TIMEZONE_ABBREVIATION_MAP.put("IST", ZoneId.of("Asia/Kolkata")); // 印度标准时间
        TIMEZONE_ABBREVIATION_MAP.put("SGT", ZoneId.of("Asia/Singapore")); // 新加坡标准时间
        TIMEZONE_ABBREVIATION_MAP.put("HKT", ZoneId.of("Asia/Hong_Kong")); // 香港标准时间

        // 其他常用时区
        TIMEZONE_ABBREVIATION_MAP.put("UTC", ZoneId.of("UTC")); // 协调世界时
        TIMEZONE_ABBREVIATION_MAP.put("GMT", ZoneId.of("GMT")); // 格林威治标准时间
    }

    /**
     * 将带时区缩写的时间字符串转换为中国标准时间
     * 支持格式：
     * - dd/MM/yyyy HH:mm:ss z (例如：10/07/2023 11:21:05 AEST)
     * - yyyy-MM-dd HH:mm:ss z (例如：2023-07-10 11:21:05 AEST)
     *
     * @param inputTimeString 输入的时间字符串
     * @return 转换后的中国标准时间Date对象，转换失败返回null
     */
    public static Date convertTimeZoneToChinaStandardTime(String inputTimeString) {
        if (StringUtils.isBlank(inputTimeString)) {
            return null;
        }

        try {
            // 首先尝试直接解析时区缩写
            String timezoneAbbreviation = extractTimezoneAbbreviation(inputTimeString);
            ZoneId targetZoneId = null;

            if (timezoneAbbreviation != null) {
                targetZoneId = TIMEZONE_ABBREVIATION_MAP.get(timezoneAbbreviation);
            }

            if (targetZoneId != null) {
                // 使用我们映射的正确时区来解析
                LocalDateTime localDateTime = parseLocalDateTime(inputTimeString);
                if (localDateTime != null) {
                    ZonedDateTime sourceZonedDateTime = localDateTime.atZone(targetZoneId);
                    ZonedDateTime chinaTime = sourceZonedDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                    return Date.from(chinaTime.toInstant());
                }
            }

            // 如果上面的方法失败，尝试使用Java内置的解析（可能不准确）
            ZonedDateTime zonedDateTime = parseTimeStringWithTimezoneAbbreviation(inputTimeString);
            if (zonedDateTime != null) {
                // 直接转换为中国标准时间
                ZonedDateTime chinaTime = zonedDateTime.withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                return Date.from(chinaTime.toInstant());
            }

            return null;
        } catch (Exception e) {
            log.error("时间转换异常，输入时间字符串: {}", inputTimeString, e);
            return null;
        }
    }

    /**
     * 从时间字符串中提取时区缩写
     *
     * @param inputTimeString 输入的时间字符串
     * @return 时区缩写，如果未找到返回null
     */
    private static String extractTimezoneAbbreviation(String inputTimeString) {
        if (StringUtils.isBlank(inputTimeString)) {
            return null;
        }

        // 使用正则表达式提取最后的时区缩写部分
        String[] parts = inputTimeString.trim().split("\\s+");
        if (parts.length > 0) {
            String lastPart = parts[parts.length - 1];
            // 检查是否是已知的时区缩写
            if (TIMEZONE_ABBREVIATION_MAP.containsKey(lastPart)) {
                return lastPart;
            }
        }

        return null;
    }

    /**
     * 解析时间字符串为LocalDateTime（不包含时区信息）
     *
     * @param inputTimeString 输入的时间字符串
     * @return LocalDateTime对象，解析失败返回null
     */
    private static LocalDateTime parseLocalDateTime(String inputTimeString) {
        if (StringUtils.isBlank(inputTimeString)) {
            return null;
        }

        // 移除时区部分，只保留日期时间部分
        String timezoneAbbreviation = extractTimezoneAbbreviation(inputTimeString);
        if (timezoneAbbreviation != null) {
            inputTimeString = inputTimeString.replace(" " + timezoneAbbreviation, "").trim();
        }

        // 支持的本地时间格式
        String[] patterns = {
            "dd/MM/yyyy HH:mm:ss",     // 10/07/2023 11:21:05
            "yyyy-MM-dd HH:mm:ss",     // 2023-07-10 11:21:05
            "MM/dd/yyyy HH:mm:ss",     // 07/10/2023 11:21:05 (美式格式)
            "yyyy/MM/dd HH:mm:ss"      // 2023/07/10 11:21:05
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return LocalDateTime.parse(inputTimeString, formatter);
            } catch (Exception e) {
                // 继续尝试下一个格式
                log.debug("格式 {} 解析失败，尝试下一个格式", pattern);
            }
        }

        log.warn("无法解析本地时间字符串: {}", inputTimeString);
        return null;
    }

    /**
     * 解析带时区缩写的时间字符串
     * 支持多种格式的时间字符串解析
     *
     * @param inputTimeString 输入的时间字符串
     * @return 解析后的ZonedDateTime对象，解析失败返回null
     */
    private static ZonedDateTime parseTimeStringWithTimezoneAbbreviation(String inputTimeString) {
        // 支持的时间格式列表
        String[] patterns = {
            "dd/MM/yyyy HH:mm:ss z",     // 10/07/2023 11:21:05 AEST
            "yyyy-MM-dd HH:mm:ss z",     // 2023-07-10 11:21:05 AEST
            "dd/MM/yyyy HH:mm:ss zzz",   // 10/07/2023 11:21:05 AEST
            "yyyy-MM-dd HH:mm:ss zzz",   // 2023-07-10 11:21:05 AEST
            "MM/dd/yyyy HH:mm:ss z",     // 07/10/2023 11:21:05 AEST (美式格式)
            "yyyy/MM/dd HH:mm:ss z"      // 2023/07/10 11:21:05 AEST
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                return ZonedDateTime.parse(inputTimeString, formatter);
            } catch (Exception e) {
                // 继续尝试下一个格式
                log.debug("格式 {} 解析失败，尝试下一个格式", pattern);
            }
        }

        log.warn("所有格式都无法解析时间字符串: {}", inputTimeString);
        return null;
    }

    /**
     * 批量转换时间字符串为中国标准时间
     *
     * @param timeStrings 时间字符串列表
     * @return 转换后的Date列表
     */
    public static List<Date> convertTimeZonesToChinaStandardTime(List<String> timeStrings) {
        if (CollectionUtils.isEmpty(timeStrings)) {
            return new ArrayList<>();
        }

        return timeStrings.stream()
                .map(DateUtils::convertTimeZoneToChinaStandardTime)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    /**
     * 检查时区缩写是否被支持
     *
     * @param timezoneAbbreviation 时区缩写
     * @return 是否支持该时区缩写
     */
    public static boolean isSupportedTimezoneAbbreviation(String timezoneAbbreviation) {
        return TIMEZONE_ABBREVIATION_MAP.containsKey(timezoneAbbreviation);
    }

    /**
     * 获取所有支持的时区缩写
     *
     * @return 支持的时区缩写集合
     */
    public static Set<String> getSupportedTimezoneAbbreviations() {
        return new HashSet<>(TIMEZONE_ABBREVIATION_MAP.keySet());
    }

    /**
     * 添加自定义时区映射
     *
     * @param abbreviation 时区缩写
     * @param zoneId 对应的ZoneId
     */
    public static void addCustomTimezoneMapping(String abbreviation, ZoneId zoneId) {
        TIMEZONE_ABBREVIATION_MAP.put(abbreviation, zoneId);
        log.info("添加自定义时区映射: {} -> {}", abbreviation, zoneId);
    }

    public static boolean isBetween(String saleBeginDate, String saleEndDate) {
        try {
            if (StringUtils.isBlank(saleBeginDate) || StringUtils.isBlank(saleEndDate)) {
                return false;
            }
            // 如果是这个格式 2025-05-10 则后面补充00:00:00
            if (saleBeginDate.length() == 10) {
                saleBeginDate = saleBeginDate + "T00:00:00Z";
            }
            if (saleEndDate.length() == 10) {
                saleEndDate = saleEndDate + "T23:59:59Z";
            }
            Instant beginInstant = Instant.parse(saleBeginDate);
            Instant endInstant = Instant.parse(saleEndDate);
            Instant now = Instant.now();
            if (!now.isBefore(beginInstant) && !now.isAfter(endInstant)) {
                return true;
            }
        } catch (Exception e) {
            log.error("时间转换异常", e);
        }
        return false;
    }

    public static String getLastMonthFirstDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return new SimpleDateFormat("yyyy-MM-dd").format(calendar.getTime());
    }

    public static Date minDate(Date d1, Date d2) {
        if (d1 == null) return d2;
        if (d2 == null) return d1;
        return d1.before(d2) ? d1 : d2;
    }

    public static Date maxDate(Date d1, Date d2) {
        if (d1 == null) return d2;
        if (d2 == null) return d1;
        return d1.after(d2) ? d1 : d2;
    }
}
