package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.file.FileUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.amazon.domain.AmazonWarehouseMapping;
import com.suncent.smc.persistence.amazon.domain.VcListingInventory;
import com.suncent.smc.persistence.amazon.service.IAmazonWarehouseMappingService;
import com.suncent.smc.persistence.amazon.service.IVcListingInventoryService;
import com.suncent.smc.persistence.common.domain.LogRecord;
import com.suncent.smc.persistence.common.service.ILogRecordService;
import com.suncent.smc.persistence.configuration.category.service.ICategoryInfoService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.mapper.PlatformCategoryMapper;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.inventory.service.IInventoryPendingListingService;
import com.suncent.smc.persistence.inventory.service.IListingInventoryOperateService;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.product.domain.entity.ProductDocumentRecord;
import com.suncent.smc.persistence.product.service.ProductDocumentRecordService;
import com.suncent.smc.persistence.publication.domain.dto.GoodsDetailDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemBackUpDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.AmazonFileSubmittedResultVO;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingFeedVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.provider.biz.config.NoModleDataListener;
import com.suncent.smc.provider.biz.configuration.ViolateWordBiz;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.publication.dto.AmazonPushDTO;
import com.suncent.smc.provider.biz.todo.SMCTodoBiz;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import com.suncent.smc.system.service.ISysConfigService;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.common.utils.thread.FutureUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.builder.EqualsBuilder;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.stream.Collectors;

/**
 * 拿到数据后写excel 调api服务
 * <p>
 * 推送商品报文（结果以推送报告为准，商家后台数据会有延迟）
 * <br>新增：商品对象updateDelete字段为空
 * <br>更新：商品对象updateDelete字段为Update或不填
 * <br>删除：商品对象updateDelete字段为Delete(且仅需Sku码即可，无需传其他字段)
 */
@Component
@Slf4j
public class BaseAmazonProductTask {
    @Autowired
    protected IGoodsHeadService goodsHeadService;
    @Autowired
    @Lazy
    protected ListingInfoBiz listingInfoBiz;
    @Autowired
    protected IAliOssService iAliOssService;
    @Autowired
    protected ProductDocumentRecordService productDocumentRecordService;
    @Autowired
    protected IPlatformCategoryService platformCategoryService;
    @Autowired
    protected PlatformCategoryMapper platformCategoryMapper;
    @Value("${sys-file.file-template-path}")
    protected String fileTemplatePath;
    @Value("${sys-file.file-template-path_new}")
    protected String fileTemplatePathNew;
    @Value("${api.amazon-upload-listing-url}")
    protected String uploadUrl;
    @Value("${api.amazon-update-inventory-url}")
    protected String updateStockUrl;
    @Autowired
    protected ICategoryInfoService categoryInfoService;

    @Autowired
    protected IListingLogService listingLogService;
    @Autowired
    protected ViolateWordBiz violateWordBiz;
    @Autowired
    protected IGoodsDescriptionService goodsDescriptionService;
    @Autowired
    protected IListingAmazonAttributeLineService listingAmazonAttributeLineService;
    @Autowired
    protected IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;

    @Autowired
    protected IListingInventoryOperateService listingInventoryOperateService;
    @Value("${aliyun.oss.urlPrefix}")
    protected String urlPrefix;

    @Value("${api.amazon-upload-listing-submitted-result-url}")
    protected String submittedFileResultUrl;

    @Autowired
    protected DingdingMonitorInfoBiz dingdingMonitorInfoBiz;
    @Autowired
    protected ThirdpartyInventoryBiz inventoryBiz;
    @Autowired
    private IGoodsTaskInfoService goodsTaskInfoService;
    ThreadPoolExecutor pool = new ThreadPoolExecutor(
            2,
            10,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(1000));
    ThreadPoolExecutor comonPool = new ThreadPoolExecutor(
            4,
            4,
            0L,
            TimeUnit.MILLISECONDS,
            new LinkedBlockingDeque<>(1000));
    @Autowired
    protected RedisService redisService;

    @Resource
    private SpringTaskRetry retryable;
    @Autowired
    IInventoryPendingListingService inventoryPendingListingService;

    @Resource
    private IGoodsHeadBackupService goodsHeadBackupService;

    @Autowired
    @Lazy
    private ListingUpdateBuilder listingUpdateBuilder;
    @Autowired
    private IGoodsResourceService goodsResourceService;
    @Autowired
    private IGoodsSpecificationService goodsSpecificationService;
    @Autowired
    SMCTodoBiz smcTodoBiz;
    @Value("${sys-file.file-template-path_new_en}")
    protected String fileTemplatePathNewEn;

    @Autowired
    @Lazy
    private AmazonProductBiz amazonProductBiz;

    @Autowired
    @Lazy
    private AmazonProductTaskBiz amazonProductTaskBiz;

    @Autowired
    ILogRecordService logRecordService;
    @Autowired
    private ISysConfigService sysConfigService;

    @Autowired
    IAmazonWarehouseMappingService amazonWarehouseMappingService;

    @Autowired
    IVcListingInventoryService vcListingInventoryService;

    @Autowired
    ThreadPoolForMonitorManager threadPoolForMonitorManager;

    /**
     * 商品刊登任务处理（支持任务类型区分）
     *
     * @param goodsHeads 商品列表
     * @param operationType 操作类型
     * @param amazonListingMap Amazon刊登映射
     */
    public void prodocutTask(List<GoodsHead> goodsHeads, String operationType, Map<Integer, Map<String, String>> amazonListingMap) {
        if (CollectionUtils.isEmpty(goodsHeads)) {
            return;
        }
        List<GoodsTaskTypeEnum> goodsTaskTypes = new ArrayList<>();
        goodsTaskTypes.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_MODIFY);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELISTING);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELETE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_IMPORT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.ONE_KEY_FOLLOW);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TODO_TEXT_UPDATE);

        //2、写数据到文件excel  同一个店铺放一个excel表
        Map<String, List<GoodsHead>> goodsByShopCode = goodsHeads.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));

        processShopsConcurrently(goodsByShopCode, operationType, goodsTaskTypes);
    }

    /**
     * 并发处理多个店铺的刊登任务,使用信号量控制并发店铺数量
     * 处理链路：XXLJOB发起刊登》多店铺同时刊登》单店铺继续并发刊登》单链接等待图片多线程换链》换链后刊登链接
     *
     * @param goodsByShopCode 按店铺分组的商品数据
     * @param operationType 操作类型
     * @param goodsTaskTypes 任务类型列表
     */
    private void processShopsConcurrently(Map<String, List<GoodsHead>> goodsByShopCode,
                                        String operationType,
                                        List<GoodsTaskTypeEnum> goodsTaskTypes) {
        if (goodsByShopCode.isEmpty()) {
            return;
        }
        // VC链接优先级更高，保证本次刊登一定会刊登VC链接
        List<String> vcCodes = goodsByShopCode.keySet().stream().filter(e -> e.contains("VC")).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(vcCodes)) {
            for (String shopCode : vcCodes) {
                List<GoodsHead> goodsHeadVOS = goodsByShopCode.get(shopCode);
                if (CollectionUtils.isEmpty(goodsHeadVOS)) {
                    continue;
                }

                // 为每个店铺创建异步任务，使用信号量控制并发数
                CompletableFuture.runAsync(() -> {
                    try {
                        // 处理店铺刊登任务，传递线程池和任务类型信息
                        processShopListing(shopCode, goodsHeadVOS, operationType, goodsTaskTypes);
                    }  catch (Exception e) {
                        log.error("店铺{}处理时发生异常", shopCode,  e);
                    }
                }, comonPool);
            }
        }

        // 最多3个线程跑，采用信号量而不采用CompletableFuture.all的原因是，只要一个店铺处理完成就可以处理其他店铺，而allof必须等待4个店铺一起处理完成
        final Semaphore selectedSemaphore =  new Semaphore(3);
        for (Map.Entry<String, List<GoodsHead>> entry : goodsByShopCode.entrySet()) {
            String shopCode = entry.getKey();
            if (shopCode.contains("VC")){
                continue;
            }
            List<GoodsHead> goodsHeadVOS = entry.getValue();

            if (CollectionUtils.isEmpty(goodsHeadVOS)) {
                continue;
            }

            // 为每个店铺创建异步任务，使用信号量控制并发数
            CompletableFuture.runAsync(() -> {
                try {
                    // 获取信号量许可
                    selectedSemaphore.acquire();
                    log.debug("店铺{}获取到信号量许可，开始处理", shopCode);

                    // 处理店铺刊登任务，传递线程池和任务类型信息
                    processShopListing(shopCode, goodsHeadVOS, operationType, goodsTaskTypes);

                } catch (InterruptedException e) {
                    log.error("店铺{}等待信号量许可时被中断", shopCode, e);
                    Thread.currentThread().interrupt();
                } catch (Exception e) {
                    log.error("店铺{}任务时发生异常", shopCode,  e);
                } finally {
                    // 确保释放信号量许可
                    selectedSemaphore.release();
                    log.debug("店铺{}释放号量许可", shopCode);
                }
            }, comonPool);
        }
    }

    /**
     * 处理单个店铺的刊登任务（支持资源隔离）
     *
     * @param shopCode 店铺代码
     * @param goodsHeadVOS 商品列表
     * @param operationType 操作类型
     * @param goodsTaskTypes 任务类型列表
     */
    private void processShopListing(String shopCode,
                                  List<GoodsHead> goodsHeadVOS,
                                  String operationType,
                                  List<GoodsTaskTypeEnum> goodsTaskTypes) {
        long startTime = System.currentTimeMillis();
        log.info("开始处理店铺刊登，店铺：{}，商品数量：{}，操作类型：{}", shopCode, goodsHeadVOS.size(), operationType);

        try {
            GoodsHead goodsHead = goodsHeadVOS.get(0);
            PlatformCategory categoryInfo = platformCategoryService.selectPlatformCategoryById(Long.valueOf(goodsHead.getCategoryId()));

            if (Objects.isNull(categoryInfo)) {
                log.error("商品id:{},商品name:{},类目信息不存在", goodsHead.getId(), goodsHead.getTitle());
                throw new RuntimeException("类目信息不存在");
            }

            // 执行刊登流程
            JSONFlow(operationType, shopCode, goodsHeadVOS, goodsTaskTypes, categoryInfo);

            long endTime = System.currentTimeMillis();
            log.info("店铺刊登处理完成，店铺：{}，耗时：{}ms", shopCode, (endTime - startTime));

        } catch (Exception e) {
            log.error("店铺刊登处理失败，店铺：{}，操作类型：{}", shopCode, operationType, e);

            // 错误处理
            try {
                GoodsHead goodsHead = goodsHeadVOS.get(0);

                // 创建刊登错误日志
                amazonProductBiz.errorLog(goodsHeadVOS, operationType, e.getMessage());

                // 更新任务状态
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()),
                    goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR, e.getMessage());

                // 更新待办状态
                smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(), TodoStatusEnum.WAIT_STATUS);

            } catch (Exception ex) {
                log.error("处理店铺刊登异常时发生错误，店铺：{}", shopCode, ex);
            }
        }
    }

    /**
     * JSON 流程
     *
     * @param operationType 操作类型
     * @param shopCode 店铺代码
     * @param goodsHeadVOS 商品列表
     * @param goodsTaskTypes 任务类型列表
     * @param categoryInfo 类目信息
     */
    private void JSONFlow(String operationType, String shopCode, List<GoodsHead> goodsHeadVOS,
                         List<GoodsTaskTypeEnum> goodsTaskTypes, PlatformCategory categoryInfo) {
        AmazonPushDTO amazonPushDTO = AmazonPushDTO.builder()
                .shopCode(shopCode)
                .goodsHeadVOS(goodsHeadVOS)
                .operationType(operationType)
                .goodsTaskTypes(goodsTaskTypes)
                .categoryInfo(categoryInfo)
                .build();
        amazonProductTaskBiz.uploadGoodsHeads(amazonPushDTO);
    }

    /**
     * sc excel上传流程
     *
     * @param operationType
     * @param amazonListingMap
     * @param shopCode
     * @param categoryInfo
     * @param goodsHeadVOS
     */
    private void scExcelFlow(String operationType, Map<Integer, Map<String, String>> amazonListingMap, String shopCode, PlatformCategory categoryInfo, List<GoodsHead> goodsHeadVOS) {
        boolean exist = listingInfoBiz.judgeNewTemplateExistByProductType(categoryInfo.getProductType());
        String templateFileName = "";
        String fileName = "";
        String fileNa = "";
        if (exist) {
            //新模板
            templateFileName = fileTemplatePathNew + categoryInfo.getProductType() + ".xlsm";  //模板
            fileNa = UUID.randomUUID() + "_" + shopCode + "_" + categoryInfo.getCategoryEnName().replaceAll(" ", "") + "_" + categoryInfo.getProductType() + "_" + operationType + "_new_target.xlsm";//目标文件名
            fileName = fileTemplatePathNew + fileNa;  //目标文件完整地址
        } else {
            //旧模板
            templateFileName = fileTemplatePath + categoryInfo.getCategoryEnName().replaceAll(" ", "") + ".xlsm";  //模板
            fileNa = UUID.randomUUID() + "_" + shopCode + "_" + categoryInfo.getCategoryEnName().replaceAll(" ", "") + "_" + operationType + "_target.xlsm";//目标文件名
            fileName = fileTemplatePath + fileNa;  //目标文件完整地址
        }
        if (Objects.equals(operationType, "跟卖")) {
            templateFileName = fileTemplatePathNew + "flow/flowsold.xlsx";

        }
        log.info("amazon平台----模板url:{},目标url:{}", templateFileName, fileName);

        //将值写到excel
        writeToExcel(templateFileName, fileName, goodsHeadVOS, operationType, amazonListingMap);

        if (CollUtil.isEmpty(goodsHeadVOS)) {
            return;
        }

        String url = "";
        if (Objects.equals(operationType, "跟卖")) {
            url = uploadOss(fileName);
        } else {
            //将fileName copy至纯英文文件
            String targetName = "";
            templateFileName = fileTemplatePathNewEn + categoryInfo.getProductType() + ".xlsm";  //模板
            File enTemplatefile = new File(templateFileName);
            if (enTemplatefile.exists()) {
                fileNa = UUID.randomUUID() + "_" + shopCode + "_" + categoryInfo.getCategoryEnName().replaceAll(" ", "") + "_" + categoryInfo.getProductType() + "_" + operationType + "_new_en_target.xlsm";//目标文件名
                targetName = copyWriteToEnExcel(fileName, templateFileName, fileTemplatePathNewEn + fileNa);
                FileUtils.deleteFile(fileName);
            }
            url = uploadOss(enTemplatefile.exists() ? targetName : fileName);
        }


        // 文件记录入库
        List<Integer> headIds = goodsHeadVOS.stream().map(GoodsHead::getId).collect(Collectors.toList());
        ProductDocumentRecord productDocumentRecord = new ProductDocumentRecord();
        productDocumentRecord.setFileName(fileNa);
        productDocumentRecord.setUrl(url);
        productDocumentRecord.setListingIds(JSON.toJSONString(headIds));
        productDocumentRecord.setCreateTime(new Date());
        productDocumentRecordService.insertRecord(productDocumentRecord);

        //调用Api项目
        AmazonListingFeedVO amazonListingFeedVO = new AmazonListingFeedVO();
        amazonListingFeedVO.setSmcRecordId(String.valueOf(productDocumentRecord.getId()));
        amazonListingFeedVO.setSellerCode(shopCode);
        amazonListingFeedVO.setFileUrl(url);
        uploadListingExcelToApi(amazonListingFeedVO, goodsHeadVOS, operationType);
        log.info("amazon平台----调用API服务完成,文件入库id:{},url:{}", productDocumentRecord.getId(), url);
    }


    private String uploadOss(String fileName) {
        String url;
        // 上传商品数据文件
        try {
            String iAliFileName = iAliOssService.renameUuidFileName(fileName);
            iAliOssService.putObjectByFile(iAliFileName, fileName);
            url = iAliOssService.generateBaseUrl(iAliFileName);
            url = urlPrefix + url.split(".com/")[1];
        } catch (Exception e) {
            log.error("文件上传失败", e);
            throw new RuntimeException("文件上传至oss失败");
        } finally {
            FileUtils.deleteFile(fileName);
        }
        return url;
    }

    /**
     * @param fileName         源文件
     * @param templateFileName 英文模板文件
     * @return 目标文件
     */
    public String copyWriteToEnExcel(String fileName, String templateFileName, String targetName) {
        try {
            //将fileName copy至纯英文文件
            NoModleDataListener noModleDataListener = new NoModleDataListener();
            EasyExcel.read(fileName, noModleDataListener).sheet("模板").doRead();
            List<Map<Integer, String>> allListInfo = noModleDataListener.getDataInfoListInfo();

            EasyExcel.write(targetName).withTemplate(templateFileName).sheet("Template").doWrite(dataList(allListInfo));

            return targetName;
        } catch (Exception e) {
            return fileName;
        }
    }


    /**
     * 违禁词校验
     *
     * @param operationType
     * @param goodsHeadVOS
     * @return
     */
    public List<GoodsHead> checkGoodsViolateWord(String operationType, List<GoodsHead> goodsHeadVOS) {
        if (CollectionUtils.isEmpty(goodsHeadVOS)) {
            return goodsHeadVOS;
        }
        List<GoodsHead> goodsHeadList = new ArrayList<>();
        List<Integer> headIds = goodsHeadVOS.stream().map(GoodsHead::getId).collect(Collectors.toList());

        Map<Integer, List<GoodsDescription>> descMap = goodsDescriptionService
                .selectDescriptionListByGoodsIdList(headIds)
                .stream()
                .collect(Collectors.groupingBy(GoodsDescription::getGoodsId));

        Map<Integer, List<ListingAmazonAttributeLine>> attributeLineMap = listingAmazonAttributeLineService.selectByGoodsIds(headIds)
                .stream()
                .collect(Collectors.groupingBy(ListingAmazonAttributeLine::getGoodsId));

        Map<String, GoodsDetailDTO> goodsDetailMap = new HashMap<>();
        for (GoodsHead goodsHead : goodsHeadVOS) {
            //vc违禁词不校验
            if (goodsHead.getShopCode().contains("VC")){
                goodsHeadList.add(goodsHead);
                continue;
            }
            Integer headId = goodsHead.getId();
            List<GoodsDescription> goodsDescription = descMap.get(headId);
            List<ListingAmazonAttributeLine> attributeLines = attributeLineMap.get(headId);

            try {
                // 非更新，判断是否可以上架
                if(!"更新".equals(operationType)) {
                    amazonProductBiz.canPutListing(goodsHead.getBrandCode(), goodsHead.getPdmGoodsCode(), goodsDetailMap, goodsHead.getShopCode());
                }

                violateWordBiz.checkViolateWord(Objects.equals(operationType, "上架"),
                        CollectionUtils.isEmpty(goodsDescription) ? null : goodsDescription.get(0)
                        , CollectionUtils.isEmpty(attributeLines) ? null : attributeLines
                        , goodsHead);
                goodsHeadList.add(goodsHead);
            } catch (Exception e) {
                log.error("商品id:{},商品name:{},Listing校验失败", goodsHead.getId(), goodsHead.getTitle(), e);
                amazonProductBiz.insertListingLog(operationType, e.getMessage(), goodsHead);
                //完结任务
                List<GoodsTaskTypeEnum> goodsTaskTypes = new ArrayList<>();
                goodsTaskTypes.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
                goodsTaskTypes.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_MODIFY);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELISTING);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELETE);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_IMPORT);
                goodsTaskTypes.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
                goodsTaskTypes.add(GoodsTaskTypeEnum.ONE_KEY_FOLLOW);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE);
                goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);

                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        return goodsHeadList;
    }


    /**
     * 1、通过系统字段-文件表头映射关系  得到  文件表头-系统字段具体的值 映射关系
     * 2、读excel 得到 文件表头序号(第几列)-文件表头 映射关系
     * 3、即可得出第几列需要写什么值
     *
     * @param templateFileName
     * @param fileName
     * @param goodsHeadVOS
     */
    private void writeToExcel(String templateFileName, String fileName, List<GoodsHead> goodsHeadVOS, String operationType, Map<Integer, Map<String, String>> amazonListingMap) {
        //GoodsHeadId ---<< 文件表头 --- 系统字段数据>>
        Map<Integer, Map<String, String>> idAndfileheadAndSys = getMaps(goodsHeadVOS, operationType, amazonListingMap);

        //对应 序号 ---  文件表头
        NoModleDataListener noModleDataListener = new NoModleDataListener();
        if (Objects.equals(operationType, "跟卖")) {
            EasyExcel.read(templateFileName, noModleDataListener).sheet("Template").doRead();
        } else {
            EasyExcel.read(templateFileName, noModleDataListener).sheet("模板").doRead();
        }
        Map<Integer, String> fileNOAndFileHead = noModleDataListener.getList();

        List<Map<Integer, String>> resultList = new ArrayList();
        for (int i = 0; i < goodsHeadVOS.size(); i++) {
            GoodsHead goodsHead = goodsHeadVOS.get(i);
            //文件表头 --- 系统字段数据
            Map<String, String> fileheadAndSys = idAndfileheadAndSys.get(goodsHead.getId());
            //得出 序号  --- 系统字段数据
            HashMap<Integer, String> sysAndFileNo = getSysAndFileNo(fileheadAndSys, fileNOAndFileHead);
            resultList.add(sysAndFileNo);
        }
        if (CollectionUtils.isEmpty(resultList)) {
            return;
        }

        if (Objects.equals(operationType, "跟卖")) {
            EasyExcel.write(fileName).withTemplate(templateFileName).sheet("Template").doWrite(dataList(resultList));
        } else {
            EasyExcel.write(fileName).withTemplate(templateFileName).sheet("模板").doWrite(dataList(resultList));
        }

    }


    private List<List<Object>> dataList(List<Map<Integer, String>> sysAndFileNoList) {
        List<List<Object>> list = new ArrayList<>();
        for (int i = 0; i < sysAndFileNoList.size(); i++) {
            Map<Integer, String> map = sysAndFileNoList.get(i);
            List<Object> data = new ArrayList<>();
            Integer column = map.keySet().stream().max(Comparator.comparing(Integer::valueOf)).orElse(245);
            for (int j = 0; j < column + 1; j++) {
                data.add(Objects.isNull(map.get(j)) ? null : map.get(j));
            }
            list.add(data);
        }
        return list;
    }


    /**
     * 拿到系统字段与文件表头序号关系
     *
     * @param fileheadAndSys
     * @param fileNOAndFilehead
     * @return
     */
    private HashMap<Integer, String> getSysAndFileNo(Map<String, String> fileheadAndSys, Map<Integer, String> fileNOAndFilehead) {
        HashMap<Integer, String> sysAndFileNo = new HashMap<>();

        //1、遍历 文件表头 --- 系统字段
        fileheadAndSys.keySet().stream().forEach(fileHead -> {
            String systemField = fileheadAndSys.get(fileHead);

            //2、遍历 序号 ---  文件表头
            fileNOAndFilehead.keySet().stream().forEach(fileNo -> {
                String fileHead2 = fileNOAndFilehead.get(fileNo);
                if (!Objects.equals(fileHead, fileHead2)) {
                    return;
                }
                //得出 序号  --- 系统字段
                sysAndFileNo.put(fileNo, systemField);
            });
        });
        return sysAndFileNo;
    }

    /**
     * 系统字段 --- 文件表头
     *
     * @param goodsHeadVOS
     * @return
     */
    private Map<Integer, Map<String, String>> getMaps(List<GoodsHead> goodsHeadVOS, String operationType, Map<Integer, Map<String, String>> amazonListingMap) {
        List<GoodsTaskTypeEnum> goodsTaskTypes = new ArrayList<>();
        goodsTaskTypes.add(GoodsTaskTypeEnum.FIND_REPLACEMENT_UPDATES);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TIMING_PUBLISH);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_PUBLISH);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_MODIFY);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_EDIT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELISTING);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_DELETE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_IMPORT);
        goodsTaskTypes.add(GoodsTaskTypeEnum.CIRCULATE_TIMED_TASK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_IMAGE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_TITLE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_PRICE_STOCK);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_ATTRIBUTE);
        goodsTaskTypes.add(GoodsTaskTypeEnum.BATCH_UPDATE_FIVE_POINTS);
        goodsTaskTypes.add(GoodsTaskTypeEnum.TODO_TEXT_UPDATE);

        if (Objects.isNull(goodsHeadVOS)) {
            return new HashMap<>();
        }
        Map<Integer, Map<String, String>> resultMap = new HashMap<>();
        Iterator<GoodsHead> iterator = goodsHeadVOS.iterator();
        while (iterator.hasNext()) {
            GoodsHead goodsHead = iterator.next();
            try {
                log.info("平台商品编码为：[{}]上传至amazon平台,后台开始文件的处理", goodsHead.getPlatformGoodsCode());
                // 获取Listing所有属性
                Map<String, String> listingMap;

                //处理跟卖
                if (Objects.equals(operationType, "跟卖")) {
                    listingMap = listingInfoBiz.getFlowSoldListingFieldMap(goodsHead);
                    resultMap.put(goodsHead.getId(), listingMap);
                    continue;

                }

                //对更新的amazon数据 进行做数据对比,进行部分更新
                if (Objects.equals(operationType, "更新") && ObjectUtils.isEmpty(amazonListingMap)) {
                    amazonListingMap = comparativeData(goodsHead, amazonListingMap);
                }

                if (ObjectUtils.isEmpty(amazonListingMap) || ObjectUtils.isEmpty(amazonListingMap.get(goodsHead.getId()))) {
                    listingMap = listingInfoBiz.getListingFieldMap(goodsHead.getId());
                } else {
                    listingMap = listingInfoBiz.getListingFieldMapByModule(goodsHead.getId(), amazonListingMap);
                }

                // 兑换Listing字段
                Map<String, String> fillMap = listingInfoBiz.assembleListingMap(listingMap);

                resultMap.put(goodsHead.getId(), fillMap);

                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.PROCESSING, "");
            } catch (Exception e) {
                log.error("平台商品编码为：[{}]上传至amazon平台,后台开始文件的处理,异常信息为:{}", goodsHead.getPlatformGoodsCode(), e);
                iterator.remove();
                //创建刊登错误日志
                List<GoodsHead> goodsHeads = new ArrayList<>();
                goodsHeads.add(goodsHead);
                amazonProductBiz.errorLog(goodsHeads, operationType, e.getMessage());
                goodsTaskInfoService.updateGoodsStatusTaskInfo(String.valueOf(goodsHead.getId()), goodsTaskTypes, GoodsTaskSubStatusEnum.ERROR, e.getMessage());
            }
        }
        return resultMap;
    }

    /**
     * 对比数据->针对单个详情编辑更新的数据做对比
     *
     * @param goodsHead
     * @param amazonListingMap
     * @return
     */
    private Map<Integer, Map<String, String>> comparativeData(GoodsHead goodsHead, Map<Integer, Map<String, String>> amazonListingMap) {
        try {
            //拿到创建时间为最近6h的数据
            LogRecord headBackup = logRecordService.selectGoodsHeadBackupByGoodsIds(Lists.newArrayList(String.valueOf(goodsHead.getId())), OperTypeEnum.SINGLE_EDIT.name())
                    .stream()
                    .filter(f -> f.getCreateTime().getTime() > System.currentTimeMillis() - 6 * 60 * 60 * 1000)
                    //拿到最近的一条数据
                    .max(Comparator.comparing(LogRecord::getCreateTime)).orElse(null);
            if (ObjectUtils.isEmpty(headBackup)) {
                return null;
            }
            ItemBackUpDTO itemBackUpDTO = JSON.parseObject(headBackup.getParam(), ItemBackUpDTO.class);
            if (ObjectUtils.isEmpty(itemBackUpDTO)) {
                return null;
            }
            GoodsHead oldHead = itemBackUpDTO.getGoodsHead();
            //更新失败的状态不做对比 直接全量更新
            if (Objects.equals(oldHead.getPublishStatus(), PublishStatus.UPDATING_FAIL.getType())) {
                return null;
            }

            //属性
            List<ListingAmazonAttributeLine> attributeLineList = listingAmazonAttributeLineService.selectByGoodsId(goodsHead.getId());
            //描述
            GoodsDescription goodsDescription = goodsDescriptionService.selectDescriptionListByGoodsId(goodsHead.getId());
            //图片
            List<GoodsResource> resourceList = goodsResourceService.selectListingGoodsResourceByHeadId(goodsHead.getId());
            //规格信息
            GoodsSpecification goodsSpecification = goodsSpecificationService.selectSpecificationListByGoodsId(goodsHead.getId());
            List<String> moduleType = new ArrayList<>();
            amazonListingMap = new HashMap<>();
            List<String> excludeFields = new ArrayList<>();
            excludeFields.add("id");
            excludeFields.add("createTime");
            excludeFields.add("updateTime");
            excludeFields.add("createBy");
            excludeFields.add("updateBy");
            excludeFields.add("params");
            excludeFields.add("remark");

            //对比标题
            if (!Objects.equals(goodsHead.getTitle(), oldHead.getTitle())) {
                moduleType.add(ListingModuleType.TITLE.name());
            }
            //对比价格
            if (!Objects.equals(goodsHead.getStandardPrice(), oldHead.getStandardPrice())) {
                moduleType.add(ListingModuleType.PRICE.name());
            }
            //对比库存
            if (!Objects.equals(goodsHead.getStockOnSalesQty(), oldHead.getStockOnSalesQty())) {
                moduleType.add(ListingModuleType.INVENTORY.name());
            }
            //对比品牌
            if (!Objects.equals(goodsHead.getBrandCode(), oldHead.getBrandCode())) {
                moduleType.add(ListingModuleType.BRAND.name());
            }
            //对比描述
            if (!EqualsBuilder.reflectionEquals(goodsDescription, itemBackUpDTO.getGoodDescription(), excludeFields)) {
                moduleType.add(ListingModuleType.AMAZON_FIVE.name());
                moduleType.add(ListingModuleType.DESCRIPTION.name());
            }
            //对比属性
            if (attributeLineList.size() != itemBackUpDTO.getAmazonLines().size()) {
                moduleType.add(ListingModuleType.ATTRIBUTE.name());
            } else {
                for (int i = 0; i < itemBackUpDTO.getAmazonLines().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(attributeLineList.get(i), itemBackUpDTO.getAmazonLines().get(i), excludeFields)) {
                        moduleType.add(ListingModuleType.ATTRIBUTE.name());
                        break;
                    }
                }
            }
            //对比规格信息
            if (!EqualsBuilder.reflectionEquals(goodsSpecification, itemBackUpDTO.getGoodsSpecification(), excludeFields)) {
                moduleType.add(ListingModuleType.SPECIFICATION.name());
            }
            //对比图片
            if (resourceList.size() != itemBackUpDTO.getGoodsResourceList().size()) {
                moduleType.add(ListingModuleType.IMAGE.name());
            } else {
                for (int i = 0; i < itemBackUpDTO.getGoodsResourceList().size(); i++) {
                    if (!EqualsBuilder.reflectionEquals(resourceList.get(i), itemBackUpDTO.getGoodsResourceList().get(i), excludeFields)) {
                        moduleType.add(ListingModuleType.IMAGE.name());
                        break;
                    }
                }
            }
            ItemDTO itemDTO = new ItemDTO();
            itemDTO.setGoodsHead(goodsHead);
            itemDTO.setModuleType(moduleType);


            List<ItemDTO> itemDTOList = listingUpdateBuilder.buildItem(Collections.singletonList(itemDTO), false);
            if (ObjectUtils.isEmpty(itemDTOList) || ObjectUtils.isEmpty(itemDTOList.get(0)) || ObjectUtils.isEmpty(itemDTOList.get(0).getAmazonListingMap())) {
                return null;
            }
            amazonListingMap.put(goodsHead.getId(), itemDTOList.get(0).getAmazonListingMap());
            return amazonListingMap;
        } catch (Exception e) {
            log.error("amazon 更新listing,对比数据异常", e);
            return null;
        }

    }


    public String uploadListingExcelToApi(AmazonListingFeedVO amazonListingFeedVO, List<GoodsHead> goodsHeadVOS, String operationType) {
        String post = HttpUtils.post(uploadUrl, JSON.toJSONString(amazonListingFeedVO));
        log.debug("amazon上传文件,param:{},result:{}", amazonListingFeedVO, post);

        String result = getFileSubmitResult(amazonListingFeedVO);
        log.debug("amazon获取文件是否上传成功结果,param:{},result:{}", amazonListingFeedVO, result);

        if (result.contains("html")) {
            throw new RuntimeException("上传失败,API服务异常,请联系管理员");
        }

        JSONObject resultJson = JSON.parseObject(result);
        if (!Objects.equals(resultJson.getString("code"), "0")) {
            //文件没有提交成功  一般是api服务挂了
            throw new RuntimeException("文件提交到API失败:请联系管理员");
        }

        if (resultJson.getInteger("data") == 0) {
            //重试
            pool.execute(() -> {
                try {
                    retry(amazonListingFeedVO, 1);
                    log.debug("amazon上传文件失败,重试成功,amazonListingFeedVO:{}", amazonListingFeedVO);
                } catch (Exception e) {
                    amazonProductBiz.errorLog(goodsHeadVOS, operationType, e.getMessage());
                }
            });
        }
        return "上传成功";
    }

    private String getFileSubmitResult(AmazonListingFeedVO amazonListingFeedVO) {
        AmazonFileSubmittedResultVO resultVO = new AmazonFileSubmittedResultVO();
        resultVO.setSellerCode(amazonListingFeedVO.getSellerCode());
        resultVO.setSmcRecordId(amazonListingFeedVO.getSmcRecordId());
        String result = HttpUtils.post(submittedFileResultUrl, JSON.toJSONString(resultVO));
        return result;
    }

    private void retry(AmazonListingFeedVO amazonListingFeedVO, Integer count) {
        //4、重新上传
        String post = HttpUtils.post(uploadUrl, JSON.toJSONString(amazonListingFeedVO));
        log.debug("amazon上传文件失败,重试次数" + count + ",param:{},msg:{}", amazonListingFeedVO, post);
        if (count > 10) {
            String msg = "上传文件至API失败,重试次数" + count + "次,请求参数:" + JSON.toJSONString(amazonListingFeedVO);
            dingdingMonitorInfoBiz.monitorMediumRiskAndSend(MonitorEnum.AM_FILEMISSING, msg);
            throw new BusinessException("重试上传文件失败,请联系管理员");
        }

        String result = getFileSubmitResult(amazonListingFeedVO);
        log.debug("amazon获取文件是否上传成功结果,重试次数" + count + ",param:{},result:{}", amazonListingFeedVO, result);
        if (result.contains("html")) {
            throw new BusinessException("重试上传失败,API服务异常,请联系管理员");
        }

        JSONObject resultJson = JSON.parseObject(result);
        if (!Objects.equals(resultJson.getString("code"), "0")) {
            //文件没有提交成功
            throw new BusinessException("重试上传失败,API服务异常,请联系管理员");
        }
        if (resultJson.getInteger("data") > 0) {
            //文件提交成功
            return;
        }

        //睡眠20秒
        try {
            // 随机生成一个10000 - 25000数
            int sleepTime = RandomUtil.randomInt(10000, 25000);
            Thread.sleep(sleepTime);
        } catch (InterruptedException e) {
        }
        //重试
        retry(amazonListingFeedVO, count + 1);
    }


    /**
     * 1、过滤publishing_handler==处理中的商品
     * 2、更新该批次商品publishing_handler为处理中
     *
     * @param goodsHeads
     * @return
     */
    public List<GoodsHead> filterGoods(List<GoodsHead> goodsHeads) {
        List<GoodsHead> returnList = new ArrayList<>();
        //取非处理中的商品
        List<GoodsHead> goodsHeadsRe = goodsHeads.stream()
                .filter(goodsHead -> !Objects.equals(goodsHead.getPublishingHandler(), "处理中"))
                .collect(Collectors.toList());

        //取处理中的商品但是超过5小时还未处理完成的  重试
        List<GoodsHead> goodsHeadsIng = goodsHeads.stream()
                .filter(f -> Objects.equals(f.getPublishingHandler(), "处理中")
                        && Objects.nonNull(f.getUpdateTime())
                        && DateUtils.getDifferHour(f.getUpdateTime(), DateUtils.getNowDate()) > 5)
                .collect(Collectors.toList());

        returnList.addAll(goodsHeadsRe);
        returnList.addAll(goodsHeadsIng);
        if (CollectionUtils.isEmpty(returnList)) {
            return null;
        }
        returnList.forEach(goodsHead -> {
            GoodsHead updateGood = new GoodsHead();
            updateGood.setId(goodsHead.getId());
            updateGood.setPublishingHandler("处理中");
            goodsHeadService.updateListingGoodsHead(updateGood);
        });
        return returnList;
    }


    protected void updatePublishHandler(List<GoodsHead> heads, boolean monitorError) {
        for (GoodsHead head : heads) {
            GoodsHead updateGood = new GoodsHead();
            updateGood.setId(head.getId());
            updateGood.setPublishingHandler(monitorError ? "" : "处理中");
            goodsHeadService.updateListingGoodsHead(updateGood);
        }
    }


    /**
     * 获取vc仓库的仓库
     * @param shop
     * @param inventoryMap
     * @param sku
     * @param whCode
     * @return
     */
    public Integer getWhQuantity(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, String sku, String whCode) {
        String site = shop.getSite();
        Integer shopQuantity = inventoryBiz.getSkuQuantityAugment(sku, whCode, shop.getShopCode());
        //库存下限
        Integer quantityFloor = inventoryBiz.getSkuQuantityFloor(sku, whCode, shop.getShopCode());
        //实时库存
        Integer stock = inventoryMap.get(site + whCode + sku);
        if (ObjectUtils.isEmpty(stock)) {
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectFbmStockByShareAndPartGoodsCode(Collections.singletonList(sku), true);
            inventoryBiz.groupByWarehouseCode(thirdpartyFbmDTOList);
            ThirdpartyFbmDTO fbmDTO = thirdpartyFbmDTOList.stream().filter(t -> Objects.equals(t.getWarehouseCode(), whCode) && Objects.equals(t.getSku(), sku) && Objects.equals(t.getWhCountry(), site)).findFirst().orElse(null);
            if (ObjectUtils.isEmpty(thirdpartyFbmDTOList) || ObjectUtils.isEmpty(fbmDTO)) {
                //vc某个仓库查询为空 直接把库存更新为0
                return 0;
            }

            inventoryMap.put(site + whCode + sku, fbmDTO.getSellableQty());
            stock = fbmDTO.getSellableQty();
        }
        //实际库存大于等于库存上限 直接更新为库存上限值
        if (ObjectUtils.isNotEmpty(shopQuantity) && stock >= shopQuantity) {
            return shopQuantity;
        }
        //实时库存小于等于库存下限 直接更新为0
        if (ObjectUtils.isNotEmpty(quantityFloor) && stock <= quantityFloor) {
            return 0;
        }
        return stock;

    }

    /**
     * 获取库存 如果库存记录表没有记录则实时查一次库存接口
     *
     * @param inventoryMap
     * @param goodsHead
     * @return
     */
    public Integer getQuantity(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, GoodsHead goodsHead) {
        boolean isVc = ObjUtil.isNotEmpty(goodsHead.getShopCode()) && goodsHead.getShopCode().contains("VC");
        //查询vc报告表
        if (isVc) {
            return getVcQuantity(inventoryMap, goodsHead);
        } else {
            return getScQuantity(shop, inventoryMap, goodsHead);
        }
    }

    /**
     * 获取vc库存  key ->site +whCode +sku
     *
     * @param goodsHead
     * @return
     */
    private int getVcQuantity(Map<String, Integer> inventoryMap, GoodsHead goodsHead) {
        List<AmazonWarehouseMapping> warehouseMappings = amazonWarehouseMappingService.selectAmazonWarehouseMappingListByShopCode(goodsHead.getShopCode());
        Map<String, String> whCodeMap = warehouseMappings.stream().collect(Collectors.toMap(AmazonWarehouseMapping::getAmWhCode, AmazonWarehouseMapping::getWhCode));

        List<VcListingInventory> listingInventoryList = vcListingInventoryService.selectVcListingInventoryByGoodsId(goodsHead.getId());
        Integer quantity = 0;
        for (VcListingInventory listingInventory : listingInventoryList) {
            String whCode = whCodeMap.get(listingInventory.getWarehouseCode());
            Integer availableInventory = listingInventory.getAvailableInventory();
            Integer whQuantity = 0;
            if (ObjUtil.isEmpty(whCode)) {
                log.error("vc-df获取库存失败,未找到对应仓库编码,仓库编码:{},主键ID:{}", listingInventory.getWarehouseCode(), goodsHead.getId());
                continue;
            }
            //库存上限
            Integer shopQuantity = inventoryBiz.getSkuQuantityAugment(goodsHead.getPdmGoodsCode(), whCode, goodsHead.getShopCode());
            //库存下限
            Integer quantityFloor = inventoryBiz.getSkuQuantityFloor(goodsHead.getPdmGoodsCode(), whCode, goodsHead.getShopCode());
            Integer stock = inventoryMap.get(goodsHead.getSiteCode() + whCode + goodsHead.getPdmGoodsCode());
            if (ObjectUtils.isEmpty(stock)) {
                stock = 0;
                whQuantity = 0;
            }
            //实际库存大于等于库存上限 直接更新为库存上限值
            if (ObjectUtils.isNotEmpty(shopQuantity) && stock >= shopQuantity) {
                whQuantity = shopQuantity;
            }
            //实时库存小于等于库存下限 直接更新为0
            if (ObjectUtils.isNotEmpty(quantityFloor) && stock <= quantityFloor) {
                whQuantity = 0;
            }
            if (Objects.equals(whQuantity, availableInventory)) {
                continue;
            }
            quantity += whQuantity;
        }

        return quantity;
    }

    /**
     * 获取sc库存
     *
     * @param shop
     * @param inventoryMap
     * @param goodsHead
     * @return
     */
    private int getScQuantity(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, GoodsHead goodsHead) {
        //库存上限
        Integer shopQuantity = inventoryBiz.getSkuQuantityAugment(goodsHead.getPdmGoodsCode(), null, shop.getShopCode());
        //库存下限
        Integer quantityFloor = inventoryBiz.getSkuQuantityFloor(goodsHead.getPdmGoodsCode(), null, shop.getShopCode());
        //实时库存
        Integer stock = inventoryMap.get(goodsHead.getSiteCode() + goodsHead.getPdmGoodsCode());
        if (ObjectUtils.isEmpty(stock)) {
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(goodsHead.getPdmGoodsCode()), shop.getSite(), false);
            if (ObjectUtils.isEmpty(thirdpartyFbmDTOList)) {
                log.error("amazon库存更新失败,获取实际库存为空,此次不更新库存,平台销售编码:{}", goodsHead.getPlatformGoodsId());
                //不更新库存
                return goodsHead.getStockOnSalesQty().intValue();
            }
            inventoryMap.put(goodsHead.getSiteCode() + goodsHead.getPdmGoodsCode(), thirdpartyFbmDTOList.get(0).getSellableQty());
            stock = thirdpartyFbmDTOList.get(0).getSellableQty();
        }
        //实际库存大于等于库存上限 直接更新为库存上限值
        if (ObjectUtils.isNotEmpty(shopQuantity) && stock >= shopQuantity) {
            return shopQuantity;
        }
        //实时库存小于等于库存下限 直接更新为0
        if (ObjectUtils.isNotEmpty(quantityFloor) && stock <= quantityFloor) {
            return 0;
        }
        return stock;
    }

    /**
     * 更新amazon本地库存
     *
     * @param goodsHeadList
     * @param inventoryMap
     */
    public void updateAmazonLocalStock(ConfigStoreInfo shop, List<GoodsHead> goodsHeadList, Map<String, Integer> inventoryMap, String currUserId) {
        goodsHeadList.parallelStream().forEach(l -> {
            GoodsHead updateHead = new GoodsHead();
            updateHead.setId(l.getId());
            BigDecimal nowStock = BigDecimal.valueOf(getQuantity(shop, inventoryMap, l));
            listingLogService.insertSuccessListingLog("库存服务更新成功,库存由" + l.getStockOnSalesQty() + "变为" + nowStock, currUserId, l.getId());
            updateHead.setStockOnSalesQty(nowStock);
            goodsHeadService.updateListingGoodsHead(updateHead);
        });
    }
}
