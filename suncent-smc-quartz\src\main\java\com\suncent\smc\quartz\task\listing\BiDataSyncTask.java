package com.suncent.smc.quartz.task.listing;

import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.enums.BiDimensionEnum;
import com.suncent.smc.common.enums.DimensionEnum;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.bi.service.IBiDataService;
import com.suncent.smc.persistence.configuration.biData.domain.entity.AutoSalesDetail;
import com.suncent.smc.persistence.configuration.biData.service.IAutoSalesDetailService;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz;
import com.suncent.smc.provider.biz.task.dto.AmazonVcRealTimeSalesReport;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * BI 同步数据定时任务
 */
@Component
@Slf4j
public class BiDataSyncTask {
    @Autowired
    private IBiDataService iBiDataService;

    ExecutorService executorService = Executors.newFixedThreadPool(1);

    @Autowired
    private IAutoSalesDetailService autoSalesDetailService;
    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;

    @XxlJob("biDataSyncTask")
    public void biDataSyncTask() {
        try {
            handle();
        } catch (Exception e) {
            log.error("同步bi数据失败", e);
        }
    }

    private void handle() {
        log.info("同步bi数据开始");
        try {
            doHandle();
        } catch (Exception e) {
            log.error("同步bi数据失败", e);
        }
        log.info("同步bi数据完成");
    }

    private void doHandle() {
        //listing销量数据
        syncListingSaleData();


        executorService.submit(() -> {
            // 处理VC链接的销量数据
            log.info("同步VC销量数据开始");
            syncVcSalesData(null);
            log.info("同步VC销量数据完成");
        });
    }

    /**
     * 同步listing销量数据
     *
     * @param platformSkus
     */
    public void syncVcSalesData(String platformSkus) {
        try {
            List<String> platformGoodsCodes = null;
            if (StringUtils.isNotBlank(platformSkus)) {
                platformGoodsCodes = Arrays.asList(platformSkus.split(","));
            }
            // 1、分页获取VC链接，
            int pageNum = 1;
            int pageSize = 500;
            PageHelper.startPage(pageNum, pageSize);
            List<GoodsHead> goodsHeads = goodsHeadService.listNeedSyncRealTimeSales(platformGoodsCodes);
            if (CollectionUtils.isEmpty(goodsHeads)) {
                return;
            }
            handlerVC(goodsHeads);
            Page<GoodsHead> page = (Page<GoodsHead>) goodsHeads;
            int pages = page.getPages();
            for (int i = 2; i <= pages; i++) {
                PageHelper.startPage(i, pageSize);
                goodsHeads = goodsHeadService.listNeedSyncRealTimeSales(platformGoodsCodes);
                handlerVC(goodsHeads);
            }
        } catch (Exception e) {
            log.error("同步VC销量数据失败", e);
        }
    }

    public void handlerVC(List<GoodsHead> goodsHeads) {
        List<String> asins = goodsHeads.stream().map(GoodsHead::getPlatformGoodsId).collect(Collectors.toList());
        // 2、获取VC链接的销量数据
        AjaxResult ajaxResult = amazonApiHttpRequestBiz.listRealTimeSalesReport("VC1", asins);
        if (Objects.isNull(ajaxResult) || !ajaxResult.isSuccess()) {
            return;
        }
        List<AmazonVcRealTimeSalesReport> salesReports = JSONObject.parseArray(ajaxResult.get("data").toString(), AmazonVcRealTimeSalesReport.class);
        if (CollectionUtils.isEmpty(salesReports)) {
            return;
        }
        // 3、对报告数据汇总成30、60、90天的数据
        Map<String, List<AmazonVcRealTimeSalesReport>> listMap = salesReports.stream().collect(Collectors.groupingBy(AmazonVcRealTimeSalesReport::getAsin));
        listMap.forEach((k, v) -> {
            // 设置时间范围：30天、60天、90天
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime start90Days = endTime.minusDays(90);
            AutoSalesDetail autoSalesDetail = aggregateData(v, start90Days, endTime);
            // 查询是否已存在
            AutoSalesDetail exist = autoSalesDetailService.getOne(autoSalesDetail.getPlatformSaleCode());
            if (Objects.nonNull(exist)) {
                autoSalesDetail.setId(exist.getId());
                autoSalesDetailService.updateAutoSalesDetail(autoSalesDetail);
            } else {
                autoSalesDetail.setDimension(String.valueOf(DimensionEnum.COMPANY.code));
                autoSalesDetail.setDimensionValue("桑椹公司");
                autoSalesDetail.setPlatformSaleCode(k);
                autoSalesDetail.setPeriodIdD(new Date());
                autoSalesDetail.setOrderQuantity30(BigDecimal.ZERO);
                autoSalesDetail.setOrderQuantity60(BigDecimal.ZERO);
                autoSalesDetail.setOrderQuantity90(BigDecimal.ZERO);
                autoSalesDetail.setCreatedTime(new Date());
                autoSalesDetailService.insertAutoSalesDetail(autoSalesDetail);
            }
        });
    }


    public AutoSalesDetail aggregateData(List<AmazonVcRealTimeSalesReport> records, LocalDateTime start90Days, LocalDateTime endTime) {
        LocalDateTime start30Days = endTime.minusDays(30);
        LocalDateTime start60Days = endTime.minusDays(60);
        // 分国家聚合数据
        BigDecimal last30DaysAmount = BigDecimal.ZERO, last60DaysAmount = BigDecimal.ZERO, last90DaysAmount = BigDecimal.ZERO;
        int last30DaysQuantity = 0, last60DaysQuantity = 0, last90DaysQuantity = 0;

        for (AmazonVcRealTimeSalesReport record : records) {
            String startTimeStr = record.getStartTime();
            String endTimeStr = record.getEndTime();

            // 解析为ZonedDateTime
            ZonedDateTime zonedDateTime = ZonedDateTime.parse(startTimeStr);
            // 转换为系统默认时区的LocalDateTime
            LocalDateTime startTimeTemp = zonedDateTime.withZoneSameInstant(ZoneId.systemDefault()).toLocalDateTime();

            if (startTimeTemp.isAfter(start30Days)) {
                if (record.getOrderedRevenue() != null) {
                    last30DaysAmount = last30DaysAmount.add(record.getOrderedRevenue());
                }
                last30DaysQuantity += record.getOrderedUnits();
            }
            if (startTimeTemp.isAfter(start60Days)) {
                last60DaysAmount = last60DaysAmount.add(record.getOrderedRevenue());
                last60DaysQuantity += record.getOrderedUnits();
            }
            if (startTimeTemp.isAfter(start90Days)) {
                last90DaysAmount = last90DaysAmount.add(record.getOrderedRevenue());
                last90DaysQuantity += record.getOrderedUnits();
            }
        }
        AutoSalesDetail autoSalesDetail = new AutoSalesDetail();
        autoSalesDetail.setPlatformSaleCode(records.get(0).getAsin());
        autoSalesDetail.setSalesVolume30(BigDecimal.valueOf(last30DaysQuantity));
        autoSalesDetail.setSalesVolume60(BigDecimal.valueOf(last60DaysQuantity));
        autoSalesDetail.setSalesVolume90(BigDecimal.valueOf(last90DaysQuantity));
        return autoSalesDetail;
    }

    /**
     * 同步BI销量数据
     * 暂只同步公司维度的数据
     */
    private void syncListingSaleData() {
        try {
            log.info("开始同步BI销量数据");
            
            // 初始化临时表
            goodsHeadService.clearTempAsinSalesTable();
            log.info("临时表初始化完成");
            
            // 导入ASIN到临时表
            List<String> asinList = iBiDataService.listAsinSales();
            log.info("获取到{}个ASIN", asinList != null ? asinList.size() : 0);
            
            if (CollectionUtils.isEmpty(asinList)) {
                log.info("没有数据需要处理");
                return;
            }

            goodsHeadService.insertAsinSales(asinList);
            log.info("ASIN导入临时表完成");
            
            // 清空旧数据
            autoSalesDetailService.deleteAllSalesDetail();
            log.info("清空旧数据完成");
            
            // 分批处理ASIN
            int batchSize = 2000; // 每批处理5000个ASIN
            int processedCount = 0;
            int totalProcessed = 0;

            while (true) {
                // 分批获取数据
                asinList = goodsHeadService.listAsinSales(batchSize);
                if (CollectionUtils.isEmpty(asinList)) {
                    log.info("没有更多数据需要处理");
                    break;
                }
                
                try {
                    // 分批获取数据
                    List<Map<String, String>> batchData = iBiDataService.getCompanyListingDetailByBatch(asinList);
                    
                    // 处理数据
                    List<AutoSalesDetail> companyList = new ArrayList<>();
                    handleAutoSalesDetail(batchData, companyList, null);
                    
                    // 批量插入
                    batchInsert(companyList);

                    goodsHeadService.deleteAsinSales(asinList);

                    processedCount = asinList.size();
                    totalProcessed += processedCount;
                    log.info("已处理{}条数据，当前批次{}条", totalProcessed, processedCount);
                } catch (Exception e) {
                    log.error("处理ASIN批次数据失败", e);
                }
            }
            
            log.info("BI销量数据同步完成，共处理{}条数据", totalProcessed);
        } catch (Exception e) {
            log.error("Bi数据同步定时任务-listing销量数据同步失败", e);
        }
    }

    /**
     * 批量插入数据
     *
     * @param autoSalesDetails 需要插入的数据列表
     */
    private void batchInsert(List<AutoSalesDetail> autoSalesDetails) {
        if (CollectionUtils.isEmpty(autoSalesDetails)) {
            log.info("没有数据需要插入");
            return;
        }
        List<List<AutoSalesDetail>> batches = ListUtil.split(autoSalesDetails, 500);
        for (List<AutoSalesDetail> batch : batches) {
            autoSalesDetailService.insertList(batch);
        }
    }


    /**
     * 处理listing详情
     *
     * @param listingDetailList 销量详情列表
     * @param autoListingDetailList 结果列表
     * @param type 维度类型
     * @return
     * @Date 2023/6/28 15:15
     * <AUTHOR>
     */
    private void handleAutoSalesDetail(List<Map<String, String>> listingDetailList, List<AutoSalesDetail> autoListingDetailList, String type) {
        if (CollectionUtils.isEmpty(listingDetailList)) {
            return;
        }
        
        for (Map<String, String> map : listingDetailList) {
            try {
                AutoSalesDetail autoSalesDetail = new AutoSalesDetail();
                autoSalesDetail.setDimension("1");
                autoSalesDetail.setDimensionValue("桑椹公司");
                if (null != type) {
                    autoSalesDetail.setDimension(type);
                    autoSalesDetail.setDimensionValue(map.get(BiDimensionEnum.getDimensionName(type)));
                }
                String platformSaleCode = map.get("platform_sale_code");
                if (ObjUtil.isEmpty(platformSaleCode)) {
                    log.warn("ASIN为空，跳过处理");
                    continue;
                }
                autoSalesDetail.setPlatformSaleCode(platformSaleCode);
                autoSalesDetail.setPeriodIdD(DateUtils.parseDate(map.get("period_id_d")));
                autoSalesDetail.setSalesVolume30(Convert.toBigDecimal(map.get("sales_volume_30")));
                autoSalesDetail.setOrderQuantity30(Convert.toBigDecimal(map.get("order_quantity_30")));
                autoSalesDetail.setSalesVolume60(Convert.toBigDecimal(map.get("sales_volume_60")));
                autoSalesDetail.setOrderQuantity60(Convert.toBigDecimal(map.get("order_quantity_60")));
                autoSalesDetail.setSalesVolume90(Convert.toBigDecimal(map.get("sales_volume_90")));
                autoSalesDetail.setOrderQuantity90(Convert.toBigDecimal(map.get("order_quantity_90")));
                autoSalesDetail.setSalesVolume270(Convert.toBigDecimal(map.get("sales_volume_270")));
                autoSalesDetail.setOrderQuantity270(Convert.toBigDecimal(map.get("order_quantity_270")));
                autoSalesDetail.setCreatedTime(new Date());
                autoListingDetailList.add(autoSalesDetail);
            } catch (Exception e) {
                // 记录错误但继续处理其他数据
                log.error("处理ASIN: {}数据时出错", map.get("platform_sale_code"), e);
            }
        }
    }

}
