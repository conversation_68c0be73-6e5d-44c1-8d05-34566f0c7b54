package com.suncent.smc.provider.biz.publication;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

/**
 * 流水线配置管理器
 * 统一管理流水线处理相关的配置项
 * 支持从 Nacos 动态刷新配置
 */
@Data
@Component
@ConfigurationProperties(prefix = "amazon.pipeline")
@RefreshScope
@Slf4j
public class PipelineConfigManager {

    /**
     * 是否启用流水线处理
     */
    private boolean enabled = false;

    /**
     * 是否启用监控
     */
    private boolean monitoringEnabled = true;

    /**
     * 超时时间（分钟）
     */
    private int timeoutMinutes = 5;

    /**
     * 批量处理大小
     */
    private int batchSize = 20;

    /**
     * 是否启用降级处理
     */
    private boolean fallbackEnabled = true;

    /**
     * 配置初始化后的回调
     */
    @PostConstruct
    public void init() {
        validateConfig();
        log.info("流水线配置初始化完成: {}", getConfigSummary());
    }

    /**
     * 是否启用流水线处理
     */
    public boolean isPipelineEnabled() {
        return enabled;
    }

    /**
     * 是否启用监控
     */
    public boolean isMonitoringEnabled() {
        return monitoringEnabled;
    }

    /**
     * 获取所有配置的摘要信息
     */
    public String getConfigSummary() {
        return String.format(
            "流水线配置: 启用=%s, 监控=%s, 超时=%d分钟, 批量=%d, 降级=%s",
            enabled,
            monitoringEnabled,
            timeoutMinutes,
            batchSize,
            fallbackEnabled
        );
    }

    /**
     * 验证配置的合理性
     */
    public void validateConfig() {
        if (timeoutMinutes < 1 || timeoutMinutes > 60) {
            log.warn("超时时间配置可能不合理: {}分钟，建议设置为1-60分钟", timeoutMinutes);
        }

        if (batchSize < 1 || batchSize > 100) {
            log.warn("批量处理大小配置可能不合理: {}，建议设置为1-100", batchSize);
        }

        if (enabled) {
            log.info("流水线处理已启用，配置摘要: {}", getConfigSummary());
        } else {
            log.info("流水线处理未启用，将使用传统同步处理");
        }
    }

    /**
     * 获取配置建议
     */
    public String getConfigRecommendations() {
        StringBuilder recommendations = new StringBuilder();
        recommendations.append("流水线配置建议:\n");

        if (!enabled) {
            recommendations.append("- 建议在测试环境验证后启用流水线处理以提升性能\n");
        }

        if (timeoutMinutes < 3) {
            recommendations.append("- 超时时间较短，可能导致图片上传未完成就执行刊登\n");
        } else if (timeoutMinutes > 10) {
            recommendations.append("- 超时时间较长，可能影响整体处理效率\n");
        }

        if (batchSize < 10) {
            recommendations.append("- 批量大小较小，可能无法充分利用并发优势\n");
        } else if (batchSize > 50) {
            recommendations.append("- 批量大小较大，可能对系统资源造成压力\n");
        }

        if (!fallbackEnabled) {
            recommendations.append("- 建议启用降级处理以提高系统稳定性\n");
        }

        return recommendations.toString();
    }

    /**
     * 动态更新配置时的回调（配合 @RefreshScope 使用）
     */
    public void refreshConfig() {
        log.info("流水线配置已刷新: {}", getConfigSummary());
        validateConfig();
    }
}
