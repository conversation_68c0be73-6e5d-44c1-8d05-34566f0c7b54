package publication;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.ItemType;
import com.suncent.smc.SuncentSmcApplication;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.domain.UrlReplaceEntity;
import com.suncent.smc.common.enums.OperTypeEnum;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.enums.PublishStatus;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.Utils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.ads.domain.AdsFitmentDataEbay;
import com.suncent.smc.persistence.ads.mapper.AdsMapper;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsImage;
import com.suncent.smc.persistence.pdm.domain.entity.GoodsSpecifications;
import com.suncent.smc.persistence.pdm.service.IGoodsImageService;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.pdm.service.IGoodsSpecificationsService;
import com.suncent.smc.persistence.product.domain.entity.ProductDocumentRecord;
import com.suncent.smc.persistence.product.service.ProductDocumentRecordService;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingFeedVO;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.template.domain.entity.TemplateEbayDescription;
import com.suncent.smc.persistence.template.mapper.TemplateEbayDescriptionMapper;
import com.suncent.smc.provider.biz.configuration.CategoryInfoHandleBiz;
import com.suncent.smc.provider.biz.publication.*;
import com.suncent.smc.provider.biz.publication.service.ListingInfoService;
import com.suncent.smc.provider.biz.publication.service.impl.EbayPlatformListingServiceImpl;
import com.suncent.smc.provider.update.HandlerListingUpdateModuleComposite;
import com.suncent.smc.provider.update.ListingUpdateModuleResolver;
import com.suncent.smc.provider.update.domain.ListingModuleType;
import com.suncent.smc.quartz.task.listing.TimedCirculateTask;
import com.suncent.smc.quartz.task.listing.am.AmazonListingAdapterUpdateTask;
import com.suncent.smc.quartz.task.listing.am.AmazonListingPullFiledDictTask;
import com.suncent.smc.quartz.task.listing.am.AmazonVCListingPushTask;
import com.suncent.smc.quartz.task.listing.eb.EbayListingDeleteTask;
import com.suncent.smc.quartz.task.listing.eb.EbayListingPushTask;
import com.suncent.smc.quartz.task.listing.eb.EbayListingUpdateTask;
import com.suncent.smc.quartz.task.temu.TemuInventoryUpdate;
import com.suncent.smc.system.service.ISysUserService;
import com.suncent.smc.web.controller.consumer.AmazonListingResultConsumer;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import javax.jms.JMSException;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023-01-11 10:00:00
 */
@Slf4j
@SpringBootTest(classes = {SuncentSmcApplication.class})
@RunWith(SpringRunner.class)
public class GoodsServiceImplTest {
    @Autowired
    IGoodsService goodsService;

    @Autowired
    IListingAttributeTempService listingAttributeTempService;

    @Test
    public void getGoodsDtoByStockTest() {
        SaleGoodsDTO saleGoodsDTO = new SaleGoodsDTO();
        saleGoodsDTO.setId(1L);
        SaleGoodsDTO goodsDtoByStock = goodsService.getGoodsDtoByStock(saleGoodsDTO);
        log.info(goodsDtoByStock.toString());
    }

    @Test
    public void test() {
        ListingAttributeTemp listingAttributeTemp = new ListingAttributeTemp();
        List<ListingAttributeTemp> listingAttributeTemps = listingAttributeTempService.selectListingAttributeTempList(listingAttributeTemp);
        System.out.println(listingAttributeTemps.size());
    }

    @Autowired
    IGoodsHeadService goodsHeadService;
    @Autowired
    AmazonVCListingPushTask amazonListingFeedTask;


    @Autowired
    private IPlatformCategoryService platformCategoryService;
    @Autowired
    private AmazonListingPullFiledDictTask amazonListingPullFiledDictTask;


    @Autowired
    private ListingInfoBiz listingInfoBiz;
    @Autowired
    private IGoodsResourceService goodsResourceService;

    @Test
    public void test1() {
        List<GoodsResource> goodsResourceList = goodsResourceService.selectListingGoodsResourceByHeadId(11560);
//        listingInfoBiz.replaceResourcesUrl(goodsResourceList);
    }

    @Autowired
    private CategoryInfoHandleBiz categoryInfoHandleBiz;

    @Autowired
    private EbayListingPushTask ebayListingPushTask;
    @Autowired
    private ISysUserService userService;

    @Test
    public void ebayPush() {
//        ebayListingPushTask.ebayListingPushTask();

        try {
            String json = "[{\"asin\": \"" + "166132872304" + "\",\"platformSku\": \"" + "ZC9LHB0080" + "\",\"shopCode\": \"" + "BU123" + "\"}]";
            String operators = "738";
            String deptId = "380";
            try {
                operators = Long.valueOf(2072).toString();
            } catch (NumberFormatException e) {
            }
            SysUser sysUser = userService.selectUserById(Long.valueOf(operators));
            if (Objects.nonNull(sysUser)) {
                deptId = String.valueOf(sysUser.getDeptId());
            }

            Map<String, Object> pdmMap = new HashMap<>();
            pdmMap.put("json", json);
            pdmMap.put("operators", operators);
            pdmMap.put("deptId", deptId);
            pdmMap.put("goodsCode", "ZC9LHB0080");
            log.info("操作Ebay商品刊登完成,准备添加PDM状态映射:{}", JSON.toJSONString(pdmMap));
            String pdm = HttpUtils.post("http://47.119.194.156:8083/pdm/MappingGoods/createMapping", pdmMap);
            AjaxResult ajaxResult = JSONObject.parseObject(pdm, AjaxResult.class);
            Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);
        } catch (Exception e) {
            log.warn("新增pdm状态失败", e);
        }


    }


    @Autowired
    IGoodsSpecificationsService goodsSpecificationsService;
    @Autowired
    IGoodsSpecificationService goodsSpecificationService;

    @Test
    public void updateSpecifications() {
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setPublishStatus(2);
        goodsHead.setPlatform("AM");
        goodsHead.setCreateBy("1841");
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead);

        List<String> ids = new ArrayList<>();
        for (GoodsHead head : goodsHeads) {
            GoodsSpecification goodsSpecification = goodsSpecificationService.selectSpecificationListByGoodsId(head.getId());
            if (Objects.isNull(goodsSpecification)) {
                continue;
            }

            GoodsSpecifications goodsSpecifications = new GoodsSpecifications();
            goodsSpecifications.setGoodsCode(head.getPdmGoodsCode());
            List<GoodsSpecifications> specificationsList = goodsSpecificationsService.selectGoodsSpecificationsList(goodsSpecifications);
            if (CollectionUtil.isEmpty(specificationsList)) {
                continue;
            }
            if (!Objects.equals(String.valueOf(goodsSpecification.getItemLength()), "20.320000")) {
                continue;
            }
            GoodsSpecifications specifications = specificationsList.get(0);
            goodsSpecification.setItemLength(specifications.getLength().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setItemWidth(specifications.getWidth().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setItemHeight(specifications.getHeight().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageLength(specifications.getLength().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageWidth(specifications.getWidth().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageHeight(specifications.getHeight().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setItemLengthUnit(StringUtils.isEmpty(specifications.getLengthUnit()) ? "cm" : specifications.getLengthUnit());
            goodsSpecification.setPackageLengthUnit(StringUtils.isEmpty(specifications.getLengthUnit()) ? "cm" : specifications.getLengthUnit());
            goodsSpecification.setPackageWeight(specifications.getGrossWeight().setScale(2, RoundingMode.HALF_UP));
            goodsSpecification.setPackageWeightUnit(StringUtils.isEmpty(specifications.getGrossWeightUnit()) ? "kg" : specifications.getGrossWeightUnit());

            goodsSpecificationService.updateListingGoodsSpecification(goodsSpecification);

            GoodsHead updateHead = new GoodsHead();
            updateHead.setId(head.getId());
            updateHead.setPublishStatus(3);
            goodsHeadService.updateListingGoodsHead(updateHead);

            ids.add(head.getId().toString());
        }
        System.out.println("上架完成:" + JSON.toJSONString(ids));

        //选择排序
        int[] arr = {1, 3, 2, 45, 65, 33, 12};
        for (int i = 0; i < arr.length; i++) {
            int minIndex = i;
            for (int j = i + 1; j < arr.length; j++) {
                if (arr[j] < arr[minIndex]) {
                    minIndex = j;
                }
            }
            int temp = arr[i];
            arr[i] = arr[minIndex];
            arr[minIndex] = temp;
        }

    }


    @Autowired
    private BaseAmazonProductTask baseAmazonProductTask;

    @Test
    public void testPull() {
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setPlatform(PlatformTypeEnum.AM.name());
//        goodsHead.setPublishStatus(1);
        goodsHead.setPlatformGoodsCode("9294BPS0126US");
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead);

        baseAmazonProductTask.prodocutTask(goodsHeads, "上架", null);
    }


    @Autowired
    private IGoodsImageService goodsImageService;


    @Test
    public void amazonUpdate() {
//        String[] platformIds= {"B0C5N17DXS","B0C5MVDNVB","B0C5MV4WXT","B0C5MXTHSL","B0C5MW64QF","B0C5N1D499","B0C5MWJQJ7","B0C5MX8MZG","B0C5N167R6","B0C5MY81DP","B0C5MWH8R3","B0C5N1WMG2","B0C5MWJKXH","B0C5MXDWQ7","B0C5MXT7YF","B0C5MYZD4Z","B0C5MWVR8W","B0C5MYJJJN","B0C5MXBFZ6","B0C5MXW4TM","B0C5MY3ZHF","B0C5MXC6K5","B0C5N1VT2T","B0C5MYFFKK","B0C5MYXPV5","B0C5MXYNGW","B0C5MX69JL","B0C5MYVH6K","B0C5MY7T6P","B0C5N19MDD","B0C5MVMVTP","B0C5N13K3V","B0C5MYS9TW","B0C5MY3GKJ","B0C5MXSDXG","B0C5MXGJVY","B0C5MXNXG2","B0C5N1B294","B0C5MVXZH3","B0C5MZ47Z4","B0C5MW8C92","B0C5MXSDRF","B0C5MZZZXS","B0C5MWSNTL","B0C5MX6TL1","B0C5N1K1XS","B0C5MW27L6","B0C5MXV54R","B0C5MXT2H5","B0C5N1DCGD","B0C5MWP2XX","B0C5MX4CX1","B0C5MYTN6P","B0C5MXVWP5","B0C5MYCFB9","B0C5N1V8VQ","B0C6938GWZ","B0C692LM6N","B0C692CTKY","B0C693P7G5","B0C693KZJ6","B0C6936TL5","B0C694NVK8","B0C692QL6J","B0C6977FN5","B0C693VL54","B0C6935X9R","B0C6942PHR","B0C6925T9L","B0C693MTVL","B0C6927979","B0C6971L72","B0C692DMB7","B0C693P9B9","B0C691PBCM","B0C6932V95","B0C692WGQH","B0C693F9C4","B0C6926GJH","B0C694STJF","B0C69314VP","B0C69436WQ","B0C693LRNS","B0C692Q25K","B0C692YPZD","B0C69547R6","B0C6MC74K7","B0C6MBW9B7","B0C6MDCDH1","B0C6MC7F74","B0C6MBY2ZF","B0C6MC84GJ","B0C6M8XV8Q","B0C6MG11G8","B0C6MCXDDG","B0C6MBXS9R","B0C6MC43KZ","B0C6MFQ8F5","B0C6MDZBXV","B0C6M8N3BB","B0C6MC44XS","B0C6MD8SHS","B0C6MDGBDT","B0C6M92R98","B0C6M9QWNH","B0C6MB2F2C","B0C6MBJY1Q","B0C6M9VWYJ","B0C6MFVVKH","B0C6MCHDCJ","B0C6MGSMBP","B0C6MJ1V6F","B0C6MJNXC3","B0C6MGZLDZ","B0C6ML9WW9","B0C6MGKLXY","B0C6MJKTMN","B0C6MH5CVV","B0C6MGTNXJ","B0C6MG7KT4","B0C6MK6138","B0C6MGF7XM","B0C6MH8RRP","B0C6MFYCGQ","B0C6MJTCZ9","B0C6MJMRSD","B0C6MHZVZD","B0C6MJ5ZXB","B0C6MF2SDD","B0C6MHG3VF","B0C6SMDBY1","B0C6MHJJY2","B0C6MJMM51","B0C6MJ28CT","B0C6MF7HSG","B0C6MH947Q","B0C6MH4CK3","B0C6MJKX2W","B0C6MJFTZR","B0C6MJPS49","B0C6MGRRRN","B0C6MH1FMM","B0C6MJ8K35","B0C6MJRTBP","B0C6MJLRLC","B0C6MHBTBC","B0C6MGSVWN","B0C6MLJS1S","B0C6MDBMZ2","B0C6MHQD5S","B0C6MGXQ7P","B0C6MJFV9R","B0C6SH5KS3","B0C6MHFMJC","B0C6MG3L8Q","B0C6MGTNX6","B0C6ML1MTC","B0C6SK8XJT","B0C6MH9B7X","B0C6MDBBL3","B0C6MGPNHL","B0C6MHBRJ6","B0C6MHTNRV","B0C6MFQ3YX","B0C6SYKX3L","B0C6MKG4S8","B0C6SQMRXK","B0C6MK83RN","B0C6MJ89NK","B0C6MKNK1R","B0C6MH6FHH","B0C6MFZFWC","B0C6MGZ8HK","B0C6MK4F9X","B0C6MJJN3F","B0C6MHCFXW","B0C6MHCXF5","B0C6MJPN76","B0C6MK3R7B","B0C6MLFM7N","B0C6MGKQ8N","B0C6MGP3KC","B0C6MJ392T","B0C6MGLZ7C","B0C6MHJW3C","B0C6MHTZQ7","B0C6MH1TTH","B0C6MH21WX","B0C6MKQN5Q","B0C6MMDVL9","B0C6MKHR87","B0C6MNGMBB","B0C6MP9P32","B0C6ML69KC","B0C6MMKZYY","B0C6MNH1WW","B0C6MMKW2B","B0C6MKMSNM","B0C6MM48LK","B0C6MMNK6Q","B0C6MMN9QK","B0C6MNFCR8","B0C6MNZPWF","B0C6MNPXPM","B0C6MQL6KF","B0C6MNRYT7","B0C6MNQF3X","B0C6MN8GNJ","B0C6MN49P1","B0C6MMYTLG","B0C6MNWFFH","B0C6MQKW7N","B0C6MPBFBB","B0C6MPG97B","B0C6MMMCL5","B0C6MPJBJT","B0C6MNN1HC","B0C6MN78B4","B0C6MMRY6G","B0C6MPYVQ7","B0C6MMQR4F","B0C6MMTFV7","B0C6MPF77D","B0C6MNX25H","B0C6MQ61Q6","B0C6MM7XL8","B0C6MNZSZR","B0C6MNK54M","B0C6MP21FV","B0C6MNM4BJ","B0C6MPKCQS","B0C6MP3TK2","B0C6MSR8FS","B0C6MMCBHV","B0C6MMWD6Z","B0C6MNVWL4","B0C6MNRT4Q","B0C6MRTBLW","B0C6MMHNTD","B0C6MMQQN5","B0C6MQJ5MN","B0C6MNJT43","B0C6MNZ6FF","B0C6MNRKQ7","B0C6MNZ51L","B0C6MNNRZ6","B0C6MNYFSV","B0C6MMZWYV","B0C6MNRCZ6","B0C6MNRBV5","B0C6MQ3HZN","B0C6MNLH9H","B0C6MNLH7F","B0C6MPKCRD","B0C6MPVWRJ","B0C6MNDYCP","B0C6MN3VJG","B0C6MP1VYF","B0C6MQJL21","B0C6MNS9KN","B0C6MPN6G4","B0C6MNRKP9","B0C6MQCVWH","B0C6MNQH2R","B0C6MPRCCN","B0C6ML3CZ8","B0C6MP6T6W","B0C6MP8X8M","B0C6MR6178","B0C6MQNSB8","B0C6MPD84R","B0C6MNRJH7","B0C6MR5RN2","B0C6MN5GVV","B0C6MQ1X9J","B0C6MNJFHZ","B0C6MQ9LM7","B0C6MQRNMG","B0C6MPZJ3L","B0C6MSC82X","B0C6MQB5BP","B0C6MSCXRQ","B0C6MQT48B","B0C6MQ6QZ6","B0C6MN9T68","B0C6MN2LWB","B0C6MMLPZJ","B0C6MN8NFT","B0C6MMW5Z1","B0C6MMVJQ7","B0C6MMH62K","B0C6MNXD54","B0C6MPJBNR","B0C6MMW2TX","B0C6MP6YG8","B0C6MP42MC","B0C6MPJSYP","B0C6MN7673","B0C6MQJXBS","B0C6MMW5YL","B0C6MPF2ZK","B0C6MN33H3","B0C6MQVBL3","B0C6MPMXFB","B0C6MPQQ99","B0C6MMCT4W","B0C6MMPZ1G","B0C6MPMR3N","B0C6MSCHCR","B0C6MN2YHB","B0C6MP97TN","B0C6MQDM65","B0C6MNRKV8","B0C6MNSHXM","B0C6MQSM3C","B0C6MNNS6Q","B0C6MMJ3V8","B0C6MNZV3W","B0C6MN65TR","B0C6PLY2F7","B0C6PMTM85"};
        String[] platformIds = {"B0BVYZ4MM9", "B0BVYXQF1J", "B0BVZ2M3VJ", "B0BQM933HN", "B0BQMCC34R", "B0BQM8H2JM", "B0BQMBC347", "B0BVYZNHXC", "B0BQMC3CNC", "B0BXRQBKGW", "B0BVYZG6VY", "B0BXRQFQRB", "B0BVYYNJNL", "B0BQVWHCN2", "B0BQW2B7VS", "B0BQMCN85B", "B0BQMBHV64", "B0BXRT69ZH", "B0BXRS41S9", "B0BXRPR9DP", "B0BXRQFLRN", "B0BXRJKPXG", "B0BVYYHF5X", "B0BQM9SDCR", "B0BQM9KC5R", "B0BXRL8GCC", "B0BQMDLQVL", "B0BQM8DCN7", "B0BXS17TZW", "B0BQMBGCLC", "B0BQM8RCJZ", "B0BQMCRC6R", "B0BXRP2WKL", "B0BQMC5BMM", "B0BVYYD2GG", "B0BXRMN688", "B0BXRR34Y6", "B0BXRQP9B3", "B0BQMCNNJ3", "B0BVYZRKCP", "B0BQMFXG21", "B0BVYZTPMS", "B0B9XH7MY1", "B0BW8W8WH5", "B0BXRSRMJ5", "B0BVYYNG75", "B0BQM97HYK", "B0B9XKDL64", "B0BXRMZBNG", "B0BQM7T277", "B0BVYYQRJQ", "B0BVZ2L772", "B0BXRQ6P7F", "B0B9XDGM3H", "B0C33N9QYW", "B0C23W7KBT", "B0C23VMQR3", "B0C33SZKTM", "B0C33N8NZ1", "B0C23XSRQC", "B0C1NBVYDC", "B0C33R2NL9", "B0C33P63KQ", "B0C33RCSTY", "B0C23TKNVX", "B0C1NBG8F8", "B0C23WMV3Z", "B0C23W66KT", "B0C33P295C", "B0C1N9XQ1B", "B0C33QJ5C7", "B0C33NWGVB"};
//        String[] platformIds= {"B0C5MXDWRY"};
        for (String platformId : platformIds) {
            try {
                GoodsHead goodsHead = new GoodsHead();
                goodsHead.setPlatform(PlatformTypeEnum.AM.name());
                goodsHead.setShopCode("US23");
                goodsHead.setPlatformGoodsId(platformId);
                List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead);
                if (CollUtil.isEmpty(goodsHeads)) {
                    continue;
                }
                GoodsHead goodsHead1 = goodsHeads.get(0);
                Integer id = goodsHead1.getId();
                String pdmGoodsCode = goodsHead1.getPdmGoodsCode();

                List<GoodsResource> resourceList = goodsResourceService.selectListingGoodsResourceByHeadId(id);
                if (CollUtil.isEmpty(resourceList)) {
                    continue;
                }
                //过滤只剩下主图
                List<GoodsResource> mainResourceList = resourceList.stream()
                        .filter(goodsResource -> ObjUtil.equals(goodsResource.getIsMain(), 1)).collect(Collectors.toList());

                if (CollUtil.isEmpty(mainResourceList)) {
                    continue;
                }

                List<GoodsImage> goodsImages = goodsImageService.selectGoodsImageByGoodsCode(pdmGoodsCode, "1",null);
                if (CollUtil.isEmpty(goodsImages)) {
                    continue;
                }
                GoodsImage goodsImage = goodsImages.get(0);

                GoodsResource goodsResource = mainResourceList.get(0);
                goodsResource.setResourceUrl(goodsImage.getImageUrl());
                goodsResourceService.updateListingGoodsResource(goodsResource);


                baseAmazonProductTask.prodocutTask(goodsHeads, "更新", null);
            } catch (Exception e) {
                log.error("更新异常,platformId:{}", e, platformId);
            }
        }
        System.out.println("更新完成");
    }

    @Autowired
    ProductDocumentRecordService productDocumentRecordService;

    @Test
    public void publishToApi() {
        String[] ids = {"5153"};

        for (String id : ids) {
            ProductDocumentRecord record = new ProductDocumentRecord();
            record.setId(id);
            List<ProductDocumentRecord> records = productDocumentRecordService.listRecord(record);
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            ProductDocumentRecord record1 = records.get(0);
            String url = record1.getUrl();
            //调用Api项目
            AmazonListingFeedVO amazonListingFeedVO = new AmazonListingFeedVO();
            amazonListingFeedVO.setSmcRecordId(String.valueOf(record1.getId()));
            amazonListingFeedVO.setSellerCode("US6");
            amazonListingFeedVO.setFileUrl(url);
            String post = HttpUtils.post("http://112.74.48.9:9002/amazon/order/submit/listing", JSON.toJSONString(amazonListingFeedVO));
//            String post = HttpUtil.post("http://127.0.0.1:9011/order/submit/listing", JSON.toJSONString(amazonListingFeedVO));
            System.out.println(post);
        }
    }



    /**
     * pdm映射插入
     */
    @Autowired
    private IListingAmazonAttributeLineService listingAmazonAttributeLineService;

    @Test
    public void insertPdmHandler() {
        String[] platformGoodsIds = {"B0C65FHHGY", "B0C6M3B8XK", "B0C6MHFQFD", "B0C6MKB312", "B0C6MNWFFH", "B0C6PSL515", "B0C6Y4DN2J", "B0C6Y3MVRL", "B0C6YK48M6"};

        for (String platformGoodsId : platformGoodsIds) {
            GoodsHead head = new GoodsHead();
            head.setPublishingHandler("已处理");
            head.setPlatformGoodsId(String.valueOf(platformGoodsId));
            head.setPlatform(PlatformTypeEnum.AM.name());
            List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(head);

            if (CollUtil.isEmpty(goodsHeadList)) {
                continue;
            }
            GoodsHead goodsHead = goodsHeadList.get(0);

            String pn = getPn(goodsHead.getId());
            String json = "[{\"platformPn\": \"" + pn + "\",\"asin\": \"" + goodsHead.getPlatformGoodsId() + "\",\"platformSku\": \"" + goodsHead.getPlatformGoodsCode() + "\",\"shopCode\": \"" + goodsHead.getShopCode() + "\"}]";
            Long operators = 738L;
            String deptId = "380";

            try {
                operators = Long.valueOf(goodsHead.getCreateBy());
            } catch (NumberFormatException e) {
            }
            SysUser sysUser = userService.selectUserById(operators);
            if (Objects.nonNull(sysUser)) {
                deptId = String.valueOf(sysUser.getDeptId());
            }

            String goodsCode = goodsHead.getPdmGoodsCode();

            Map<String, Object> pdmMap = new HashMap<>();
            pdmMap.put("json", json);
            pdmMap.put("operators", operators);
            pdmMap.put("deptId", deptId);
            pdmMap.put("goodsCode", goodsCode);
            log.info("操作Amazon商品刊登完成,准备添加PDM状态映射:{}", JSON.toJSONString(pdmMap));
            String result = HttpUtils.post("http://47.119.194.156:8083/pdm/MappingGoods/createMapping", pdmMap);
            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
            Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);

            if (ajaxResult.isSuccess()) {
                goodsHead.setPdmStatus(1);
                goodsHeadService.updateListingGoodsHead(goodsHead);
            }
        }
    }

    private String getPn(Integer goodsId) {
        ListingAmazonAttributeLine listingAmazonAttributeLine = new ListingAmazonAttributeLine();
        listingAmazonAttributeLine.setGoodsId(goodsId);
        listingAmazonAttributeLine.setTableType(0);
        List<ListingAmazonAttributeLine> amazonAttributeLines = listingAmazonAttributeLineService.selectListingAmazonAttributeLineList(listingAmazonAttributeLine);
        Optional<ListingAmazonAttributeLine> attributeLine = amazonAttributeLines.stream().filter(f -> Objects.equals(f.getTableName(), "part_number")).findFirst();
        String pn = attributeLine.isPresent() ? attributeLine.get().getTableValue() : "0";
        return pn;
    }


    @Autowired
    private TemplateEbayDescriptionMapper templateEbayDescriptionMapper;

    @Test
    public void getHtml() {
        //skuimage
        TemplateEbayDescription templateEbayDescription = templateEbayDescriptionMapper.selectTemplateEbayDescriptionById(Long.valueOf(75));
        String allHtml = templateEbayDescription.getWidgetConfig();


        //动态添加图片

        Document parse = Jsoup.parse(allHtml);
        Element skuImage = parse.getElementById("skuImage");

        //查找到skuImage下中的所有tr 并且在html中删除
        Elements trs = skuImage.getElementsByTag("tr");
        trs.remove();

        //在skuImage下的最底层element中添加tr
        Element element = skuImage.getElementsByTag("tbody").get(0);
        Element tr = null;
        for (int i = 0; i < 10; i++) {
            //每两次循环添加一个tr,每个tr中包含两个td，
            if (i % 2 == 0) {
                tr = new Element("tr");
                element.appendChild(tr);
            }
            Element td = new Element("td");
            td.append("<td style=\"width: 50%;\"><div class=\"sold_gallery_thumbnail\"> <img src=\"skuOnlineImage" + i + "\" style=\"width: 100%; text-align: center;\" class=\"sold_gallery_thumbnail_img\"/> <div class=\"sold_gallery_main\"><img src=\"skuOnlineImage\"/> </div> </div> </td>");
            tr.appendChild(td);
        }


        //动态添加描述

        Element itemOnlineDesc = parse.getElementById("itemOnlineDesc");
        itemOnlineDesc.append("<p>123</p>");


        //动态添加属性
        Element itemOnlineSpecifics = parse.getElementById("itemOnlineSpecifics");
        itemOnlineSpecifics.append("<tr><td style=\"border:1px none;\">name </td><td style=\"border:1px none;\">wlq</td></tr>");

        //得到删除后的整体的allHtml
        String html = parse.html();
        System.out.println(html);
    }


    @Autowired
    private AmazonListingResultConsumer consumer;

    @Test
    public void testPull12() {

        String text = "{\"mscRecordId\":\"6062\",\"status\":1,\"errorMsg\":\"{}\"}";
//        consumer.doHandle(text);
    }


    @Test
    public void tes123123t() {
        Map<String, String> resultMap = new HashMap<>();

        String json = "Feed Processing Summary:" +
                "\tNumber of records processed\t\t23" +
                "\tNumber of records successful\t\t22" +
                "" +
                "original-record-number\tsku\terror-code\terror-type\terror-message" +
                "18\t2553AHD0011US\t5561\tError\tYou may not use trademarked terms in the keywords attribute. Please review our prohibited seller activities and actions at https://sellercentral.amazon.com/gp/help/help.html?itemID=200386250." +
                "" +
                "18\t2553AHD0011US\t5561\tError\tYou may not use trademarked terms in the keywords attribute. Please review our prohibited seller activities and actions at https://sellercentral.amazon.com/gp/help/help.html?itemID=200386250.";

        String[] lines = json.split("");
        String processedLine = lines[1];
        String successfulLine = lines[2];
        String[] processedColumns = processedLine.split("\t");
        String[] successfulColumns = successfulLine.split("\t");
        int processedItems = Integer.parseInt(processedColumns[3].replaceAll("\r", ""));
        int successfulItems = Integer.parseInt(successfulColumns[3].replaceAll("\r", ""));
        // true为上传成功 false 为失败
        boolean analysisResult = processedItems == successfulItems;

        if (!analysisResult) {
            for (int i = 5; i < lines.length; i++) {
                String[] split = lines[i].split("\t");
                if (split.length < 5) {
                    continue;
                }
                String type = split[3];
                if (Objects.equals(type, "Warning")) {
                    continue;
                }
                String sku = split[1];
                String message = split[4];
                if (Objects.equals(type, "Error")) {
                    if (resultMap.containsKey(sku)) {
                        String errorMessage = resultMap.get(sku);
                        resultMap.put(sku, new StringBuffer(errorMessage).append(message).toString());
                    } else {
                        resultMap.put(sku, message);
                    }
                }
            }
        }
        System.out.println(resultMap);

    }


    @Test
    public void ebayTest() {
        ebayListingPushTask.ebayListingPushTask();
    }

    @Autowired
    private EbayListingUpdateTask ebayListingUpdateTask;

    @Test
    public void ebay1Test() {
        ebayListingUpdateTask.ebayListingUpdateTask();
    }

    @Autowired
    private EbayListingDeleteTask ebayListingDeleteTask;

    @Test
    public void ebay2Test() {
        ebayListingDeleteTask.ebayListingDeleteTask();
    }

    /**
     * ebay同步数据组适配数据以及更新头边234状态为更新中
     *
     * @param
     * @Date 2023/8/23 11:51
     * @return
     * <AUTHOR>
     */
    @Autowired
    private AdsMapper adsMapper;
    @Autowired
    private IListingEbayLineService listingEbayLineService;

    @Autowired
    IListingEbayAdaptiveService adaptiveService;
    @Autowired
    private IListingEbayAdaptiveService listingEbayAdaptiveService;

    @Test
    public void ebayAdapterTest() {
        List<String> goodsCodeList = new ArrayList<>();
        extracted(goodsCodeList);
        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodCodeList(goodsCodeList);
        List<Goods> goodsList = goodsService.selectGoodsByGoodCodeList(query);

        for (Goods goods : goodsList) {
            String goodsCode = goods.getGoodsCode();
            //查询适配数据
            List<AdsFitmentDataEbay> dataEbayList = adsMapper.selectFitmentDataEbayByProductCode(goods.getProductCode());
            //根据商品编码查询主表
            GoodsHead goodsHead = new GoodsHead();
            goodsHead.setPdmGoodsCode(goodsCode);
            goodsHead.setPlatform(PlatformTypeEnum.EB.name());
            List<GoodsHeadVO> goodsHeadVOS = goodsHeadService.selectListingGoodsHeadVOList(goodsHead);
            if (CollectionUtils.isEmpty(goodsHeadVOS)) {
                continue;
            }
            //根据主表id查询eby行信息
            List<Integer> headIdList = goodsHeadVOS.stream().map(GoodsHeadVO::getId).collect(Collectors.toList());

            ListingEbayLine listingEbayLine = new ListingEbayLine();
            listingEbayLine.setHeadList(headIdList);
            List<ListingEbayLine> listingEbayLines = listingEbayLineService.selectListingEbayLineList(listingEbayLine);
            //获取行id
            List<Integer> lineIdList = listingEbayLines.stream().map(ListingEbayLine::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(lineIdList)) {
                continue;
            }
            //根据行id删除适配数据
            Integer[] lineIdArr = lineIdList.toArray(new Integer[lineIdList.size()]);
            if (lineIdArr.length == 0) {
                continue;
            }
            //删除适配信息
            listingEbayAdaptiveService.deleteListingEbayAdaptiveByLineIds(lineIdArr);
            //构造适配数据
            List<ListingEbayAdaptive> allList = new ArrayList<>();
            for (Integer lineId : lineIdList) {
                List<ListingEbayAdaptive> adaptiveList = dataEbayList.stream().map(f -> {
                    ListingEbayAdaptive ebayAdaptive = new ListingEbayAdaptive();
                    ebayAdaptive.setEngine(f.getEngine());
                    ebayAdaptive.setMake(f.getMake());
                    ebayAdaptive.setModel(f.getModel());
                    ebayAdaptive.setTrim(f.getTrim());
                    ebayAdaptive.setYear(f.getYear());
                    ebayAdaptive.setSubmodel(f.getSubmodel());
                    ebayAdaptive.setNotes(f.getNotes());
                    ebayAdaptive.setCreateTime(new Date());
                    ebayAdaptive.setListingLineId(lineId);
                    return ebayAdaptive;
                }).collect(Collectors.toList());
                allList.addAll(adaptiveList);
            }

            List<List<ListingEbayAdaptive>> list1 = ListUtil.split(allList, 500);
            for (List<ListingEbayAdaptive> list : list1) {
                adaptiveService.batchInsertListingEbayAdaptive(list);
            }
            //修改主表状态234的为3
            List<String> headIds = goodsHeadVOS.stream().filter(e -> e.getPublishStatus().equals(PublishStatus.SALEING.getType())
                    || e.getPublishStatus().equals(PublishStatus.UPDATING.getType())
                    || e.getPublishStatus().equals(PublishStatus.UPDATING_FAIL.getType())).map(e -> {
                return String.valueOf(e.getId());
            }).collect(Collectors.toList());

            if (!CollectionUtils.isEmpty(headIds)) {
                GoodsHead goodsHead1 = new GoodsHead();
                goodsHead1.setPublishStatus((PublishStatus.UPDATING.getType()));
                goodsHead1.setIdList(headIds);
                goodsHeadService.updateListingGoodsHeadByIdList(goodsHead1);
            }
        }
    }


    private static void extracted(List<String> goodsCodeList) {
        goodsCodeList.add("KC2EXP15600");
        goodsCodeList.add("KC2EXP15700");
        goodsCodeList.add("KC2EXP15800");
        goodsCodeList.add("KC2EXP16700");
        goodsCodeList.add("KC2EXP17500");
        goodsCodeList.add("KC2EXP17800");
        goodsCodeList.add("KC2EXP18000");
        goodsCodeList.add("KC2EXP18000");
        goodsCodeList.add("KC2EXP18100");
        goodsCodeList.add("KC2EXP25500");
        goodsCodeList.add("KC2EXP26200");
        goodsCodeList.add("KC2EXP26900");
        goodsCodeList.add("KC2EXP27600");
        goodsCodeList.add("KC2EXP27900");
        goodsCodeList.add("KC2EXP28000");
        goodsCodeList.add("KC2EXP28000");
        goodsCodeList.add("KC2EXP28200");
        goodsCodeList.add("KC2EXP28400");
        goodsCodeList.add("KC2EXP28400");
        goodsCodeList.add("KC2EXP28500");
        goodsCodeList.add("KC2EXP28600");
        goodsCodeList.add("KC2EXP29900");
        goodsCodeList.add("KC2EXP30500");
        goodsCodeList.add("KC2EXP30500");
        goodsCodeList.add("KC2EXP31500");
        goodsCodeList.add("ZC2CAC05500U1");
        goodsCodeList.add("ZC2CAC05700U1");
        goodsCodeList.add("ZC2CAC06100U1");
        goodsCodeList.add("ZC2CAC06400U1");
        goodsCodeList.add("ZC2CAC06600U1");
        goodsCodeList.add("ZC2CAC07000U1");
        goodsCodeList.add("ZC2CAC07000U1");
        goodsCodeList.add("ZC2CAC07200U1");
        goodsCodeList.add("ZC2CAC07400U1");
        goodsCodeList.add("ZC2CAC07500U1");
        goodsCodeList.add("ZC2CAC08300U1");
        goodsCodeList.add("ZC2CAC09000U1");
        goodsCodeList.add("ZC2CAC09200U1");
        goodsCodeList.add("ZC2CAC09600U1");
        goodsCodeList.add("ZC2CAC09800U1");
        goodsCodeList.add("ZC2CAC10200U1");
        goodsCodeList.add("ZC2CAC10200U1");
        goodsCodeList.add("ZC2CAC10300U1");
        goodsCodeList.add("ZC2CAC10400U1");
    }

    @Autowired
    private HandlerListingUpdateModuleComposite handlerListingUpdateModuleComposite;
    @Resource
    public BaseEbayProductBiz ebayProductTask;
    @Autowired
    private ListingInfoService listingInfoService;
    @Autowired
    private PlatformListingFactory platformListingFactory;
    ThreadPoolExecutor poolConfig = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, new LinkedBlockingDeque<>(300000));

    @Test
    public void updateEbay() {

        List<String> goodsCodeList = new ArrayList<>();
        extracted(goodsCodeList);
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setSkuList(goodsCodeList);
        goodsHead.setPlatform(PlatformTypeEnum.EB.name());
        goodsHead.setPublishStatusList(PublishStatus.getSaleStatus());
        List<GoodsHeadVO> goodsHeadList = goodsHeadService.getListingGoodsHeadVOList(goodsHead);
        CountDownLatch latch = new CountDownLatch(goodsHeadList.size());
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            return;
        }
        ListingUpdateModuleResolver resolver = handlerListingUpdateModuleComposite.getListingUpdateResolverByModule(PlatformTypeEnum.EB.name() + ListingModuleType.ADAPTIVE.name());
        for (GoodsHeadVO goodsHeadVO : goodsHeadList) {
            poolConfig.execute(() -> {
                ItemDTO dto = new ItemDTO();
                dto.setGoodsHead(goodsHeadVO);
                try {
                    resolver.build(dto);
                } catch (Exception e) {
                    log.error("更新异常,goodsHeadVO:{}", e, goodsHeadVO);
                }
                latch.countDown();
            });
        }

        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }


    @Test
    public void updateAdaptive2Ebay() {
        List<ItemDTO> itemDTOList = new ArrayList<>();
        ListingUpdateModuleResolver resolver = handlerListingUpdateModuleComposite.getListingUpdateResolverByModule(PlatformTypeEnum.EB.name() + ListingModuleType.ADAPTIVE.name());
        ItemDTO dto = new ItemDTO();
        dto.setGoodsHead(goodsHeadService.selectListingGoodsHeadById(629055));
        itemDTOList.add(dto);
    }

@Autowired
    ListingUpdateBuilder listingUpdateBuilder;
    @Test
    public void updateDescription2Ebay() {
        String ids="783316," +
                "960973," +
                "976343," +
                "1048367," +
                "1097088," +
                "1101522," +
                "1101523," +
                "1101524," +
                "1101525," +
                "1101526," +
                "1101527," +
                "1101528," +
                "1101529," +
                "1101530," +
                "1101531," +
                "1101532," +
                "1101533," +
                "1101534," +
                "1101535," +
                "1101536," +
                "1101537," +
                "1101538," +
                "1101539," +
                "1101540," +
                "1101541," +
                "1101542," +
                "1101543," +
                "1101544," +
                "1101545," +
                "1101546," +
                "1101547," +
                "1101548," +
                "1101549," +
                "1101550," +
                "1101551," +
                "1101552," +
                "1101553," +
                "1101554," +
                "1101555," +
                "1101556," +
                "1101557," +
                "1101558," +
                "1101559," +
                "1101560," +
                "1101561," +
                "1101562," +
                "1101563," +
                "1101564," +
                "1101565," +
                "1101566," +
                "1101567," +
                "1101568," +
                "1101569," +
                "1101570," +
                "1101571," +
                "1101572," +
                "1101573," +
                "1101574," +
                "1101575," +
                "1101576," +
                "1101577," +
                "1101578," +
                "1101579," +
                "1101580," +
                "1101581";
        //ids根据,分割转成integer[]

        String[] strArr = ids.split(",");
        Integer[] intArr = new Integer[strArr.length];
        for (int i = 0; i < strArr.length; i++) {
            intArr[i] = Integer.parseInt(strArr[i]);
        }
        List<ItemDTO> itemDTOList = new ArrayList<>();
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(intArr);
        for (GoodsHead goodsHead : goodsHeadList) {
            ItemDTO dto = new ItemDTO();
            dto.setGoodsHead(goodsHead);
            dto.setModuleType(Collections.singletonList(ListingModuleType.DESCRIPTION.name()));
            itemDTOList.add(dto);
        }
        listingUpdateBuilder.updateApi(itemDTOList);

    }

    @Test
    public void testInnerPackUpdate() throws InterruptedException {
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(43);

        // 调用接口更新
        ItemDTO itemDTO = new ItemDTO();
        itemDTO.setGoodsHead(goodsHead);
        itemDTO.setModuleType(Collections.singletonList(ListingModuleType.ATTRIBUTE_V2.name()));
        List<ItemDTO> itemDTOList = new ArrayList<>();
        itemDTOList.add(itemDTO);
        listingUpdateBuilder.updateApi(itemDTOList);
        Thread.sleep(100000);
    }

    @Autowired
    protected IGoodsHeadBackupService goodsHeadBackupService;

    @Test
    public void test111() {
        GoodsHeadBackup goodsHeadBackup = new GoodsHeadBackup();
        goodsHeadBackup.setRemark(OperTypeEnum.INVENTORY_UPDATE.name());
        List<GoodsHeadBackup> headBackupList = goodsHeadBackupService.selectGoodsHeadBackupList(goodsHeadBackup);
        log.info("headBackupList:{}", headBackupList);
    }

    @Test
    public void ebayAdapterTest1() {
        List<String> goodsCodeList = new ArrayList<>();
        extracted(goodsCodeList);
        List<Integer> headIdListA = new ArrayList<>();
        extracted1(headIdListA);
        for (Integer id : headIdListA) {
            GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(id);
            String pdmGoodsCode = goodsHead.getPdmGoodsCode();
            SaleGoodsDTO query = new SaleGoodsDTO();
            query.setGoodsCode(pdmGoodsCode);
            Goods goods = goodsService.selectGoodsByGoodCode(query);
            List<AdsFitmentDataEbay> dataEbayList = adsMapper.selectFitmentDataEbayByProductCode(goods.getProductCode());

            ListingEbayLine listingEbayLine = new ListingEbayLine();
            listingEbayLine.setListingHeadId(id);
            List<ListingEbayLine> listingEbayLines = listingEbayLineService.selectListingEbayLineList(listingEbayLine);
            if (CollectionUtils.isEmpty(listingEbayLines)) {
                return;
            }
            Integer LineId = listingEbayLines.get(0).getId();

            //删除适配信息
            listingEbayAdaptiveService.deleteListingEbayAdaptiveByLineId(LineId);
            //构造适配数据
            List<ListingEbayAdaptive> allList = new ArrayList<>();
            List<ListingEbayAdaptive> adaptiveList = dataEbayList.stream().map(f -> {
                ListingEbayAdaptive ebayAdaptive = new ListingEbayAdaptive();
                ebayAdaptive.setEngine(f.getEngine());
                ebayAdaptive.setMake(f.getMake());
                ebayAdaptive.setModel(f.getModel());
                ebayAdaptive.setTrim(f.getTrim());
                ebayAdaptive.setYear(f.getYear());
                ebayAdaptive.setSubmodel(f.getSubmodel());
                ebayAdaptive.setNotes(f.getNotes());
                ebayAdaptive.setCreateTime(new Date());
                ebayAdaptive.setListingLineId(LineId);
                return ebayAdaptive;
            }).collect(Collectors.toList());
            allList.addAll(adaptiveList);
            List<List<ListingEbayAdaptive>> list1 = ListUtil.split(allList, 500);
            for (List<ListingEbayAdaptive> list : list1) {
                adaptiveService.batchInsertListingEbayAdaptive(list);
            }
            //修改主表状态234的为3
            if (goodsHead.getPublishStatus().equals(PublishStatus.SALEING.getType())
                    || goodsHead.getPublishStatus().equals(PublishStatus.UPDATING.getType())
                    || goodsHead.getPublishStatus().equals(PublishStatus.UPDATING_FAIL.getType())) {
                GoodsHead goodsHead1 = new GoodsHead();
                goodsHead1.setPublishStatus((PublishStatus.UPDATING.getType()));
                goodsHead1.setId(id);
                goodsHeadService.updateListingGoodsHead(goodsHead1);
            }

        }

    }

    private static void extracted1(List<Integer> headIdList) {
        headIdList.add(701647);
        headIdList.add(701643);
        headIdList.add(701633);
        headIdList.add(701426);
        headIdList.add(701038);
        headIdList.add(700947);
        headIdList.add(700801);
        headIdList.add(700786);
        headIdList.add(700739);
        headIdList.add(700531);
        headIdList.add(700479);
        headIdList.add(700437);
        headIdList.add(700368);
        headIdList.add(700352);
        headIdList.add(700112);
        headIdList.add(700056);
        headIdList.add(699000);
        headIdList.add(698953);
        headIdList.add(698837);
        headIdList.add(698814);
        headIdList.add(698774);
        headIdList.add(697144);
        headIdList.add(696986);
        headIdList.add(696628);
    }


    @Test
    public void tset123() {
//        GoodsHead goodsHead =new GoodsHead();
//        goodsHead.setPlatform(PlatformTypeEnum.EB.name());
//        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(goodsHead);
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(new Integer[]{319492,
                314229,
                604343,
                310824,
                636039,
                635188,
                317898,
                307666,
                316121,
                314288,
                311761,
                306671,
                306473,
                320269,
                320234,
                320284,
                306190,
                314018,
                316215,
                310307,
                314405,
                306306,
                585185,
                311888,
                311746,
                311298,
                314276,
                318705,
                310170,
                317140,
                306274,
                307942,
                306682,
                314623,
                48078,
                46169,
                45729,
                43283,
                47263,
                655714,
                644076,
                47446,
                47264,
                45550,
                661890,
                43272,
                43292,
                661385,
                45562,
                656324,
                643685,
                47747,
                677607,
                419640,
                46720,
                663935,
                46526,
                42132,
                42194,
                42135,
                670729,
                670511,
                46232,
                488163,
                488459,
                43713,
                660920,
                667637,
                661885,
                656382,
                660654,
                668724,
                42313,
                663938,
                43393,
                662987,
                656113,
                45567,
                45564,
                651822,
                449089,
                669928,
                45556,
                666039,
                716229,
                660948,
                45780,
                645153,
                644003,
                45928,
                45588,
                45586,
                665900,
                46209,
                45189,
                46205,
                43297,
                46172,
                46161,
                46175,
                42870,
                43123,
                43126,
                43120,
                42318,
                42319,
                652599,
                652593,
                734148,
                43069,
                43067,
                734051,
                45573,
                652702,
                45779,
                651546,
                660161,
                670186,
                42907,
                714703,
                43010,
                43313,
                43133,
                43061,
                42933,
                46482,
                663028,
                47741,
                665277,
                781929,
                47154,
                42898,
                45202,
                42920,
                667634,
                43058,
                668814,
                666948,
                42990,
                666951,
                42976,
                42924,
                42891,
                660643,
                643686,
                655698,
                668818,
                670007,
                643696,
                670377,
                47428,
                655869,
                655870,
                667605,
                660130,
                660119,
                669601,
                419804,
                645049,
                677727,
                677725,
                644206,
                666166,
                660649,
                43727,
                665754,
                660919,
                46811,
                670392,
                42908,
                42897,
                663916,
                44834,
                488560,
                389871,
                488223,
                663548,
                389832,
                42921,
                44582,
                43022,
                663924,
                43450,
                667583,
                667606,
                42322,
                43000,
                43064,
                42903,
                42178,
                42905,
                42282,
                42943,
                46168,
                43504,
                46513,
                42999,
                714792,
                42339,
                43137,
                43369,
                43286,
                43340,
                43301,
                43336,
                43289,
                565762,
                501723,
                43466,
                43344,
                43370,
                488629,
                643655,
                643688,
                42964,
                43021,
                417550,
                42973,
                42127,
                670029,
                42310,
                652014,
                42952,
                250056,
                190957,
                191829,
                192250,
                724382,
                410614,
                260640,
                255676,
                265877,
                272661,
                272642,
                285981,
                286053,
                257626,
                475883,
                475599,
                276779,
                269396,
                280006,
                503716,
                724491,
                543497,
                496923,
                266014,
                262330,
                268996,
                265658,
                268939,
                404918,
                404933,
                466112,
                250846,
                488753,
                281260,
                276100,
                281798,
                723363,
                722689,
                276117,
                723331,
                292129,
                543542,
                487821,
                276085,
                285685,
                543306,
                652142,
                265857,
                257249,
                312464,
                312512,
                486083,
                315219,
                523390,
                310815,
                486965,
                486355,
                316923,
                317461,
                532635,
                194529,
                475202,
                191248,
                531610,
                530393,
                530337,
                199146,
                428369,
                426312,
                312702,
                320933,
                523586,
                312775,
                312796,
                320050,
                189683,
                193871,
                631042,
                313008,
                438361,
                313079,
                311635,
                315568,
                310580,
                556256,
                560908,
                204780,
                88731,
                900992,
                757006,
                763497,
                759744,
                309353,
                352856,
                207355,
                187902,
                354956,
                355937,
                354987,
                610946,
                385557,
                385895,
                384909,
                554656,
                79338,
                308059,
                236343,
                241645,
                379689,
                231725,
                87387,
                232445,
                226715,
                468645,
                528052,
                239562,
                237201,
                231682,
                229897,
                231014,
                230735,
                237235,
                230568,
                468567,
                230993,
                231920,
                250545,
                88358,
                247360,
                632540,
                236260,
                248494,
                226697,
                87642,
                709732,
                243073,
                714927,
                226600,
                247951,
                238467,
                88776,
                232658,
                231437,
                231624,
                88519,
                226843,
                247818,
                87834,
                236501,
                237775,
                357152,
                632595,
                243631,
                236793,
                247454,
                236234,
                40200,
                259537,
                237337,
                235176,
                243517,
                550328,
                471788,
                228074,
                258999,
                248797,
                510900,
                238360,
                260803,
                488838,
                549641,
                240618,
                511036,
                263759,
                381454,
                667840,
                643541,
                418191,
                668940,
                668850,
                418719,
                62619,
                384886,
                667263,
                664761,
                673144,
                671094,
                64833,
                381438,
                673356,
                673357,
                63188,
                62623,
                63223,
                667692,
                418810,
                418730,
                668785,
                234769,
                272780,
                241672,
                267229,
                744598,
                242193,
                264501,
                269684,
                511021,
                236108,
                484096,
                483486,
                264535,
                234546,
                576188,
                269761,
                260394,
                234741,
                305668,
                771782,
                354596,
                502983,
                199361,
                183171,
                207435,
                188386,
                188394,
                308541,
                57883,
                55646,
                55566,
                55441,
                57786,
                58008,
                58108,
                310221,
                303992,
                310089,
                134867,
                145797,
                134530,
                236566,
                226442,
                68321,
                73271,
                123929,
                111298,
                99201,
                149019,
                116889,
                305129,
                634350,
                193514,
                476708,
                529137,
                497030,
                190771,
                532551,
                532224,
                531280,
                504101,
                531224,
                479779,
                147006,
                146995,
                160753,
                160794,
                143701,
                392310,
                134341,
                149840,
                162971,
                475851,
                475837,
                166395,
                551706,
                551303,
                503603,
                551260,
                142738,
                476153,
                166636,
                486466,
                164937,
                145284,
                169058,
                169037,
                488088,
                487844,
                154503,
                159628,
                154470,
                155158,
                137350,
                388787,
                388808,
                141373,
                150634,
                169376,
                159613,
                551274,
                142171,
                489165,
                148113,
                551275,
                489117,
                146294,
                148333,
                161726,
                149253,
                146189,
                166848,
                166115,
                486429,
                489120,
                144739,
                144707,
                148140,
                551219,
                160383,
                485723,
                137907,
                158027,
                489132,
                137490,
                137832,
                152974,
                144353,
                137254,
                153793,
                158059,
                158477,
                169151,
                149651,
                149726,
                149712,
                162901,
                166469,
                636234,
                198847,
                190623,
                183395,
                613328,
                476618,
                191889,
                476792,
                196530,
                207876,
                207762});
        //根据店铺分组
        Map<String, List<GoodsHead>> map = goodsHeadList.stream().collect(Collectors.groupingBy(GoodsHead::getShopCode));
        //对比同步店铺下,标题重复的 只保留createTime 最新的那条就行了 其他的全部删除掉
        CountDownLatch latch = new CountDownLatch(map.size());
        map.forEach((k, v) -> {
            poolConfig.execute(() -> {

                Map<String, List<GoodsHead>> map1 = v.stream().collect(Collectors.groupingBy(GoodsHead::getTitle));
                map1.forEach((k1, v1) -> {
                    if (v1.size() > 1) {
                        List<GoodsHead> collect = v1.stream().sorted(Comparator.comparing(GoodsHead::getCreateTime).reversed()).collect(Collectors.toList());
                        for (int i = 1; i < collect.size(); i++) {
                            GoodsHead goodsHead1 = collect.get(i);
                            if (!PublishStatus.getSaleStatus().contains(goodsHead1.getPublishStatus()))
                                goodsHeadService.deleteListingGoodsHeadByIds("同店铺重复链接,删除", new Integer[]{goodsHead1.getId()});


                        }
                    }
                });
                latch.countDown();
            });


        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.info("全部删除完成");
    }

    @Value("${api.ebay-getItem-url}")
    private String GET_ITEM_URL;
    @Autowired
    @Qualifier("ebayPlatformListingServiceImpl")
    EbayPlatformListingServiceImpl ebayPlatformListingService;

    @Test
    public void syncOnline() {
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setPlatform(PlatformTypeEnum.EB.name());
        goodsHead.setPublishStatusList(PublishStatus.getSaleStatus());
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadList(goodsHead);
        CountDownLatch latch = new CountDownLatch(goodsHeadList.size());
        goodsHeadList.forEach(good -> {
            try {
                poolConfig.execute(() -> {
                    UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
                    urlReplaceEntity.setUrl(GET_ITEM_URL);
                    urlReplaceEntity.setAccountCode(good.getShopCode());
                    urlReplaceEntity.setItemId(good.getPlatformGoodsId());
                    String itemData = HttpUtils.get(Utils.replaceUrl(urlReplaceEntity));
                    AjaxResult itemResp = JSONObject.parseObject(itemData, AjaxResult.class);
                    if (itemResp.isSuccess()) {
                        ItemType item = JSONObject.toJavaObject((JSONObject)itemResp.get(AjaxResult.DATA_TAG) , ItemType.class);
                        good.setPublishStatus((ebayPlatformListingService.getPublishStatus(item.getSellingStatus().getListingStatus())));
                        goodsHeadService.updateListingGoodsHead(good);
                    }
                    latch.countDown();
                });

            } catch (Exception e) {
                log.error("解析异常", e);
            }
        });
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        log.info("全部完成");
    }

    @Autowired
    AmazonListingResultConsumer amazonListingResultConsumer;

    @Test
    public void testConsumer() throws JMSException {
        ProductDocumentRecord param = new ProductDocumentRecord();
        param.setId("14838");
        List<ProductDocumentRecord> records = productDocumentRecordService.listRecord(param);
        if (CollectionUtils.isEmpty(records)){
            return;
        }
        ProductDocumentRecord record = records.get(0);
        List<Integer> listingIds = JSON.parseArray(record.getListingIds(), Integer.class);

        //id,状态(刊登中、更新中、下架中)
        Map<Integer, Integer> headStatusMap = new HashMap<>();

        //2、去更新smc主表状态
        List<GoodsHead> goodsHeadList = amazonListingResultConsumer.updateSmcStatus(JSONObject.parseObject("{\"mscRecordId\":\"14838\",\"status\":1,\"errorMsg\":\"{}\"}"), listingIds,headStatusMap);

        //3、更新pdm状态
        amazonListingResultConsumer.updatePdmStatus(goodsHeadList,headStatusMap);
    }

    @Autowired
    private TimedCirculateTask timedCirculateTask;
    @Test
    public void testTask()  {
        timedCirculateTask.timedReListingTask();
    }

    @Autowired
    private AmazonListingAdapterUpdateTask amazonListingAdapterUpdateTask;

    @Test
    public void updateAdapterJson() {
        amazonListingAdapterUpdateTask.amazonListingAdapterUpdateTask();
    }


    @Autowired
    private TemuInventoryUpdate temuInventoryUpdate;

    @Test
    public void TemuInventoryUpdate()  {
        temuInventoryUpdate.temuInventoryUpdate();

    }



}