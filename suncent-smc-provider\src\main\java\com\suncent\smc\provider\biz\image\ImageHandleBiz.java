package com.suncent.smc.provider.biz.image;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.Utils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.common.utils.thread.FutureUtils;
import com.suncent.smc.common.utils.thread.LogErrorAction;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.ebay.domain.EbayListingGoodsResourceV2;
import com.suncent.smc.persistence.ebay.service.IEbayListingGoodsResourceV2Service;
import com.suncent.smc.persistence.publication.domain.entity.GoodsResource;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsResourceService;
import com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz;
import com.suncent.smc.provider.service.impl.RateLimiterManager;
import com.suncent.smc.system.service.ISysConfigService;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

import static com.suncent.smc.provider.biz.aplus.APlusBiz.IMAGE_DEFAULT_PREFIX;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023-07-20 19:38:00
 */
@Service
@Slf4j
public class ImageHandleBiz {

    @Autowired
    protected IGoodsResourceService goodsResourceService;
    @Resource
    protected IEbayListingGoodsResourceV2Service ebayListingGoodsResourceV2Service;
    @Resource
    private SpringTaskRetry retryable;

    @Value("${image.single-url-upload}")
    private String singleUrlUpload;

    @Value("${api.ebay-uploadEpsImage-url}")
    private String UPLOAD_EPS_IMAGE_URL;

    @Autowired
    IAliOssService aliyunOssService;
    @Value("${aliyun.oss.defaultBucketName}")
    String defaultBucketName;
    @Value("${aliyun.oss.suncentUrlPrefix}")
    String suncentUrlPrefix;
    @Autowired
    private ISysConfigService configService;
    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;
    @Autowired
    private ThreadPoolForMonitorManager threadPoolForMonitorManager;
    
    @Autowired
    private RateLimiterManager rateLimiterManager;

    /**
     * 记录店铺使用次数，用于日志和监控
     */
    private final ConcurrentHashMap<String, AtomicInteger> shopUsageCounter = new ConcurrentHashMap<>();

    // 常量定义，避免魔法值
    private static final String AMAZON_IMAGE_UPLOAD_RATE_LIMITER = "amazon-image-upload";
    private static final String AMAZON_PIPELINE_THREAD_POOL = "amazon-pipeline-listing";
//    private static final String AMAZON_MEDIA_URL_PREFIX = "https://m.media-amazon.com/";
    
    /**
     * 随机数生成器
     */
    private final Random random = new Random();

    /**
     * 异步上传单张图片（优化版本）
     * 使用线程外部限流，避免线程池积压
     *
     * @param image 图片资源
     * @param goodsHead 商品信息
     * @return CompletableFuture
     */
    public CompletableFuture<Void> uploadImageAsync(GoodsResource image, GoodsHead goodsHead) throws Exception {
        // 检查图片URL是否需要上传
        if (StrUtil.isBlank(image.getResourceUrl()) ||
            image.getResourceUrl().startsWith(IMAGE_DEFAULT_PREFIX)) {
            log.debug("图片无需上传，商品ID:{}，图片ID:{}", goodsHead.getId(), image.getId());
            return CompletableFuture.completedFuture(null);
        }

        // 构建店铺级别的限流器名称
        String shopCode = goodsHead.getShopCode();
        String shopRateLimiterName = AMAZON_IMAGE_UPLOAD_RATE_LIMITER + "-" + shopCode;

        // 在线程外部进行限流控制，避免线程池积压
        return rateLimiterManager.executeWithSharedRateLimit(
            shopRateLimiterName,
            AMAZON_IMAGE_UPLOAD_RATE_LIMITER,
            () -> {
                // 获取图片处理线程池
                ThreadPoolExecutor imageProcessThreadPool = threadPoolForMonitorManager
                    .getThreadPoolExecutor(AMAZON_PIPELINE_THREAD_POOL);

                // 在限流控制下创建异步任务
                return CompletableFuture.runAsync(() -> {
                    try {
                        uploadSingleImageSync(goodsHead.getPublishType(), shopCode, image);
                        log.debug("图片上传完成，商品ID:{}，图片ID:{}", goodsHead.getId(), image.getId());
                    } catch (Exception e) {
                        log.error("图片上传失败，商品ID:{}，图片ID:{}", goodsHead.getId(), image.getId(), e);
                        // 不抛出异常，让CompletableFuture正常完成，避免阻塞其他图片
                    }
                }, imageProcessThreadPool);
            }
        );
    }

    /**
     * 同步上传单张图片
     */
    private void uploadSingleImageSync(Integer publishType, String shopCode, GoodsResource goodsResource) {
        try {
            if (StrUtil.isBlank(goodsResource.getResourceUrl()) ||
                goodsResource.getResourceUrl().startsWith(IMAGE_DEFAULT_PREFIX)) {
                return;
            }

            // 调用Amazon API上传图片
            AjaxResult ajaxResult = amazonApiHttpRequestBiz.upoladImageAjaxResult(
                publishType,
                goodsResource.getResourceUrl(),
                shopCode,
                0
            );

            if (ajaxResult.isSuccess()) {
                String relativeUrl = (String) ajaxResult.get("data");
                if (StringUtils.isBlank(relativeUrl)) {
                    throw new RuntimeException(goodsResource.getId() + "图片上传失败,错误" + ajaxResult.get("msg"));
                }

                // 更新图片URL
                goodsResource.setResourceUrl(IMAGE_DEFAULT_PREFIX + relativeUrl);
                goodsResourceService.updateListingGoodsResource(goodsResource);

                log.debug("图片上传成功: resourceId={}", goodsResource.getId());
            } else {
                throw new RuntimeException("图片上传失败: " + ajaxResult.get("msg"));
            }
        } catch (Exception e) {
            log.error("图片上传异常，resourceId:{}", goodsResource.getId(), e);
            throw e;
        }
    }

    /**
     * 图片url换链
     *
     * @param goodsResourcesList
     */
    public void replaceResourcesUrl(List<GoodsResource> goodsResourcesList, String shopCode) throws Exception {
        if (ObjectUtils.isEmpty(goodsResourcesList)) {
            return;
        }
        //删除为空的resource
        goodsResourcesList
                .stream()
                .filter(resource -> StrUtil.isBlank(resource.getResourceUrl()))
                .forEach(resource -> {
                    goodsResourceService.deleteListingGoodsResourceById(resource.getId());
                });
        if (ObjUtil.isEmpty(goodsResourcesList)) {
            return;
        }
        //晒出不为空的resource
        goodsResourcesList = goodsResourcesList
                .stream()
                .filter(resource -> StrUtil.isNotEmpty(resource.getResourceUrl()))
                .collect(Collectors.toList());
        if (StrUtil.isNotEmpty(shopCode)) {
            //eps图片上传
            epsImageUpload(goodsResourcesList, shopCode);
            return;
        }
        for (GoodsResource resource : goodsResourcesList) {
            changeLink(resource);
        }
    }

    /**
     * eps 图片上传
     *
     * @param goodsResourcesList
     */
    public void epsImageUpload(List<GoodsResource> goodsResourcesList, String shopCode) {
        for (GoodsResource goodsResource : goodsResourcesList) {
            //url包含ebay域名
            if (goodsResource.getResourceUrl().contains("https://i.ebayimg.com")) {
                String ebayURL = handleEpsUrl(goodsResource.getResourceUrl());
                if (ebayURL.contains("$_57.PNG")&& !ebayURL.contains("?set_id")) {
                    String url = ossChangeAndEpsUploadUrl(shopCode, ebayURL, String.valueOf(System.currentTimeMillis()),Objects.equals(goodsResource.getIsMain(),1));
                    ebayURL = handleEpsUrl(url);
                }
                goodsResource.setResourceUrl(ebayURL);
                goodsResourceService.updateListingGoodsResource(goodsResource);
                continue;
            }
            //非ebay域名图片进行eps上传拿到链接
            String url = epsImageUploadUrl(shopCode, goodsResource.getResourceUrl(), String.valueOf(System.currentTimeMillis()));
            //对eps 返回的图片进行规则处理
            goodsResource.setResourceUrl(handleEpsUrl(url));
            goodsResourceService.updateListingGoodsResource(goodsResource);
        }
    }

    /**
     * 对于eps返回的图片进行规则处理
     * https://i.ebayimg.com/00/s/MTYwMFgxNjAw/z/67UAAOSw6O1lQLt4/$_1.PNG?set_id=2
     * https://i.ebayimg.com/images/g/BqMAAOSwtLdlQdMe/s-l500.png
     * <p>
     * 提取/ /之间数字跟字母组成的字符串，不包含纯数字 纯字母 符号的字符串
     * <p>
     * 最终转换成
     * https://i.ebayimg.com/00/z/U4AAAOSwAoplQfFA/$_57.PNG
     *
     * @param url
     */
    public String handleEpsUrl(String url) {
        if (!url.contains("https://i.ebayimg.com")) {
            return url;
        }
        String imgName = null;
        Pattern pattern1 = Pattern.compile("\\$\\_\\d+");
        Matcher matcher1 = pattern1.matcher(url);
        if (matcher1.find()) {
            return url.replaceAll("\\$\\_\\d+", "\\$_57");
        }
        Pattern pattern2 = Pattern.compile("/([a-zA-Z0-9]{16})/");
        Matcher matcher2 = pattern2.matcher(url);
        if (matcher2.find()) {
            imgName = matcher2.group(1);
        }
        if (StrUtil.isEmpty(imgName)) {
            //使用正则表达式 把/$_1替换成/$_57
            return url.replaceAll("\\$\\_\\d+", "\\$_57");
        }
        return "https://i.ebayimg.com/00/z/" + imgName + "/$_57.PNG";
    }

    /**
     * eps 图片上传api
     *
     * @param shopCode
     * @param resourceUrl
     * @param resourceName
     * @return
     */
    public String epsImageUploadUrl(String shopCode, String resourceUrl, String resourceName) {
        long noEbayStart = System.currentTimeMillis();
        String url = retryable.retryableEps(() -> getEpsUrl(shopCode, resourceUrl, resourceName));
        long noEbayEnd = System.currentTimeMillis();
        log.info("非ebay域名图片eps换链耗时:{},原图片url:{},换链后图片url:{}", noEbayEnd - noEbayStart, resourceUrl, url);
        if (StringUtils.isEmpty(url)) {
            url = ossChangeAndEpsUploadUrl(shopCode, resourceUrl, resourceName,false);
        }
        return url;
    }

    /**
     * OSS换链之后再进行eps换链
     *
     * @param shopCode
     * @param resourceUrl
     * @param resourceName
     * @return
     */
    public String ossChangeAndEpsUploadUrl(String shopCode, String resourceUrl, String resourceName,boolean isMainImage) {
        //如果eps换链失败,则先用oss图片换链之后，再进行图片换链
        String oldUrl= resourceUrl;
        String finalResourceUrl = resourceUrl;
        resourceUrl = retryable.retryableOss(() -> imageUrlChange(finalResourceUrl));
        if (StringUtils.isEmpty(resourceUrl)&&isMainImage) {
            throw new BusinessException("ebay域名 主图图片oss换链失败,请稍后重试."+oldUrl);
        }
        //ebay域名图片 oss换链失败 副图直接返回原ebay链接
        if (StringUtils.isEmpty(resourceUrl)&&oldUrl.contains("https://i.ebayimg.com")) {
            return oldUrl;
        }
        if (StringUtils.isEmpty(resourceUrl)&& !oldUrl.contains("https://i.ebayimg.com")) {
            throw new BusinessException("非ebay域名图片,oss换链失败,请稍后重试."+oldUrl);
        }
        String ossUrl = resourceUrl;
        resourceUrl = retryable.retryableEps(() -> getEpsUrl(shopCode, ossUrl, resourceName));
        //删除oss图片 并且eps换链成功才删除oss图片
        if (StringUtils.isNotEmpty(resourceUrl)&&StringUtils.isNotEmpty(ossUrl)) {
            //以.com/分割取后面的 ossUrl中的路径文件名
            String fileName = ossUrl.substring(ossUrl.indexOf(".com/") + 5);
            aliyunOssService.deleteObject(defaultBucketName, fileName);
        }
        if (StringUtils.isEmpty(resourceUrl)) {
            throw new BusinessException("图片eps换链失败,请稍后重试."+ossUrl);
        }

        if (oldUrl.contains("https://i.ebayimg.com") && !oldUrl.contains("set_id")) {
            //将oldUrl替换成resourceUrl
            long beginTime = System.currentTimeMillis();
            int num = goodsResourceService.updateListingGoodsResourceUrlByOldUrl(oldUrl, resourceUrl);
            long endTime = System.currentTimeMillis();
            log.info("ebay域名图片eps换链成有效图片,原图片url:{},新图片url:{},耗时:{},更新数据库数量:{}",  oldUrl, resourceUrl,endTime-beginTime,num);
        }
        return resourceUrl;
    }

    /**
     * 获取eps图片上传接口返回的url
     *
     * @param shopCode
     * @param resourceUrl
     * @param resourceName
     * @return
     */
    private String getEpsUrl(String shopCode, String resourceUrl, String resourceName) {
        Pattern pattern = Pattern.compile("[\u4e00-\u9fa5]+");
        Matcher matcher = pattern.matcher(resourceUrl);
        while (matcher.find()) {
            String match = matcher.group();
            String encodedMatch = null;
            try {
                encodedMatch = URLEncoder.encode(match, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new BusinessException("eps 图片上传接口encode转换失败,原url:" + resourceUrl);
            }
            resourceUrl = resourceUrl.replace(match, encodedMatch);
        }
        Map<String, String> pdmMap = new HashMap<>();
        pdmMap.put("accountCode", Utils.getShopCode(shopCode));
        pdmMap.put("mediaURL", resourceUrl);
        pdmMap.put("mediaName", resourceName);
        String result = HttpUtils.post(UPLOAD_EPS_IMAGE_URL, JSON.toJSONString(pdmMap));

        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if (!ajaxResult.isSuccess()) {
            throw new BusinessException("eps 图片上传接口请求失败,原url:" + resourceUrl);
        }
        Object data = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(data) || Objects.equals(data, "null")) {
            throw new BusinessException("eps 图片上传数据获取失败,原url:" + resourceUrl);
        }
        return String.valueOf(data);
    }



    private void changeLink(GoodsResource resource) {
        String url = retryable.retryable(() -> imageUrlChange(resource.getResourceUrl()));
        if (StringUtils.isEmpty(url)) {
            throw new BusinessException("图片换链失败,请稍后重试.");
        }
        resource.setResourceUrl(url);
        goodsResourceService.updateListingGoodsResource(resource);
    }

    /**
     * 图片url换链
     *
     * @param originUrl
     */
    public String imageUrlChange(String originUrl) {
        String linkKey = configService.selectConfigByKey("image_change_link");
        String url = ObjUtil.equals(linkKey,"true") ? imageUrlChangeRemote(originUrl) : imageUrlChangeLocal(originUrl);
        return url;
    }

    /**
     * 图片远程换链
     * @param originUrl
     * @return
     */
    private String imageUrlChangeRemote(String originUrl) {
        Map<String, String> pdmMap = new HashMap<>();
        pdmMap.put("originUrl", originUrl);
        String result = HttpUtils.post(singleUrlUpload, JSON.toJSONString(pdmMap));

        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if (!ajaxResult.isSuccess()) {
            throw new BusinessException("图片换链接口请求失败.");
        }
        Object data = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(data) || Objects.equals(data, "null")) {
            throw new BusinessException("图片换链数据获取失败.");
        }
        log.info("图片远程换链开始,originUrl:{},newOriginUrl:{}", originUrl,data);
        return String.valueOf(data);
    }


    /**
     * 图片本地换链
     * @param originUrl
     * @return
     */
    public String imageUrlChangeLocal(String originUrl) {
        if (StringUtils.isEmpty(originUrl) || !originUrl.contains("http")) {
            return null;
        }
        String newOriginUrl = replaceUrl(originUrl.contains("?") ? originUrl.substring(0, originUrl.indexOf("?")) : originUrl);
        String url = "smc/files/" + DateUtils.getDate() + "/" + System.currentTimeMillis() + RandomUtils.nextInt() + ".jpg";

        //下载originUrl网络图片到本地
        String retryable1 = imageLinkChange(newOriginUrl,url);
        if (StringUtils.isEmpty(retryable1)) {
            return null;
        }
        String newUrl = suncentUrlPrefix + retryable1;
        log.info("图片本地换链开始,originUrl:{},newOriginUrl:{}", originUrl,newUrl);
        return newUrl;
    }

    private String imageLinkChange(String originUrlNew, String url) {
        InputStream inputStream = HttpUtils.getInputStreamByOkHttp(originUrlNew);
        aliyunOssService.putObjectByInputStream(defaultBucketName, url, inputStream);
        return url;
    }

    private String replaceUrl(String urlNew) {
        urlNew = urlNew.replace("https://oss.design.suncentgroup.com", "http://suncent-cloud-design.oss-cn-shenzhen-internal.aliyuncs.com");
        urlNew = urlNew.replace("http://oss.design.suncentgroup.com", "http://suncent-cloud-design.oss-cn-shenzhen-internal.aliyuncs.com");

        urlNew = urlNew.replace("https://oss.cloud.suncentgroup.com", "http://suncent-cloud.oss-cn-shenzhen-internal.aliyuncs.com");
        urlNew = urlNew.replace("http://oss.cloud.suncentgroup.com", "http://suncent-cloud.oss-cn-shenzhen-internal.aliyuncs.com");

        urlNew = urlNew.replace("https://scoss.suncentgroup.com", "http://suncent-cloud.oss-cn-shenzhen-internal.aliyuncs.com");
        urlNew = urlNew.replace("http://scoss.suncentgroup.com", "http://suncent-cloud.oss-cn-shenzhen-internal.aliyuncs.com");

        return urlNew;
    }

    public void replaceAMResourcesUrl(List<GoodsResource> goodsResourceList, Integer publishType, String shopCode, String poolName) {
        if (ObjectUtils.isEmpty(goodsResourceList)) {
            return;
        }

        String useAplusUpload = configService.selectConfigByKey("use_aplus_upload");
        if (ObjUtil.equals(useAplusUpload,"true")) {
            goodsResourceList = goodsResourceList
                    .stream()
                    .filter(resource -> StrUtil.isNotBlank(resource.getResourceUrl()) && !resource.getResourceUrl().startsWith(IMAGE_DEFAULT_PREFIX))
                    .collect(Collectors.toList());
            if (ObjUtil.isEmpty(goodsResourceList)) {
                return;
            }
            doUploadImage(goodsResourceList, publishType, shopCode, poolName);
        } else {
            for (GoodsResource resource : goodsResourceList) {
                changeLink(resource);
            }
        }
    }



    private void doUploadImage(List<GoodsResource> goodsResourceList, Integer publishType, String shopCode, String poolName) {
        // 获取专用于图片处理的线程池
        ThreadPoolExecutor imageProcessThreadPool = threadPoolForMonitorManager.getThreadPoolExecutor(StrUtil.isBlank(poolName) ? "amazon-image-process" : poolName);
        
        // 获取可用于上传图片的店铺列表
        String configShops = configService.selectConfigByKey("amazon_upload_image_shops");
        final String[] availableShops;
        
        // 如果配置不为空，解析店铺列表
        if (StringUtils.isNotBlank(configShops)) {
            String[] tempShops = configShops.split(",");
            // 过滤掉空值
            availableShops = Arrays.stream(tempShops)
                    .filter(StringUtils::isNotBlank)
                    .map(String::trim)
                    .toArray(String[]::new);
        } else {
            availableShops = null;
        }
        
        // 检查是否有有效的店铺配置
        final boolean hasAvailableShops = availableShops != null && availableShops.length > 0;
        
        // 不再使用批处理，直接处理所有资源
        List<CompletableFuture<Void>> allFutures = new ArrayList<>();
        
        // 直接遍历所有资源，为每个资源单独应用速率限制
        for (GoodsResource resource : goodsResourceList) {
            // 为单个资源创建Future任务
            CompletableFuture<Void> future = CompletableFuture.runAsync(() -> {
                try {
                    // 如果有可用店铺列表，则选择一个店铺
                    String targetShopCode = shopCode;
                    if (hasAvailableShops) {
                        // 随机选择一个店铺
                        targetShopCode = availableShops[random.nextInt(availableShops.length)];
                        
                        // 更新店铺使用计数
                        shopUsageCounter.computeIfAbsent(targetShopCode, k -> new AtomicInteger(0))
                                .incrementAndGet();
                        
                        log.info("上传图片使用随机店铺: {}, 原店铺: {}, 随机resourceId: {}", 
                                targetShopCode, shopCode, resource.getId());
                    }
                    
                    // 构建特定店铺的速率限制器名称
                    final String shopRateLimiterName = "amazon-image-upload-" + targetShopCode;
                    final String finalTargetShopCode = targetShopCode;
                    
                    // 在任务内部使用速率限制器保护，共享amazon-image-upload的配置
                    rateLimiterManager.executeWithSharedRateLimit(
                        shopRateLimiterName,
                        "amazon-image-upload",
                        () -> {
                            uploadImagByAplus(publishType, finalTargetShopCode, resource);
                            return null;
                        }
                    );
                } catch (Exception e) {
                    log.error("速率限制执行失败：{}", e.getMessage());
                }
            }, imageProcessThreadPool).whenComplete(new LogErrorAction<>("AM使用A+上传图片", resource));
            
            allFutures.add(future);
        }
        
        // 等待所有Future完成（确保方法返回前所有任务都已完成）
        if (!allFutures.isEmpty()) {
            FutureUtils.sequence(allFutures).join();
        }
        
        // 记录店铺使用情况
        if (!shopUsageCounter.isEmpty()) {
            StringBuilder usageLog = new StringBuilder("店铺使用情况统计: ");
            shopUsageCounter.forEach((shop, count) -> 
                usageLog.append(shop).append("=").append(count.get()).append(", "));
            log.info(usageLog.toString());
        }
    }



    private void uploadImagByAplus(Integer publishType, String shopCode, GoodsResource goodsResource) {
        try {
            if(StrUtil.isBlank(goodsResource.getResourceUrl()) || (StrUtil.isNotBlank(goodsResource.getResourceUrl()) && goodsResource.getResourceUrl().startsWith("https://m.media-amazon.com/"))) {
                return;
            }

            // 添加日志，跟踪上传情况
            log.debug("开始上传图片: resourceId={}, resourceUrl={}", goodsResource.getId(), goodsResource.getResourceUrl());
            if (!EnvUtils.isProdProfile()) {
                // 模拟耗时
                int sleepTime = RandomUtils.nextInt(10000, 20000);
                Thread.sleep(sleepTime);
                log.info("非生产环境，模拟耗时{}ms", sleepTime);
                return;
            }
            long startTime = System.currentTimeMillis();

            AjaxResult ajaxResult = amazonApiHttpRequestBiz.upoladImageAjaxResult(publishType, goodsResource.getResourceUrl(), shopCode, 0);
            if(ajaxResult.isSuccess()) {
                String relativeUrl = (String) ajaxResult.get("data");
                if(StringUtils.isBlank(relativeUrl)) {
                    throw new RuntimeException(goodsResource.getId() + "图片上传失败,错误" + ajaxResult.get("msg"));
                }
                goodsResource.setResourceUrl(IMAGE_DEFAULT_PREFIX + relativeUrl);
                goodsResourceService.updateListingGoodsResource(goodsResource);

                // 添加上传完成日志及耗时统计
                long costTime = System.currentTimeMillis() - startTime;
                log.info("图片上传完成: resourceId={}, costTime={}ms", goodsResource.getId(), costTime);
            }
        } catch (Exception e) {
            log.error("AM图片上传失败,原因:{}", e.getMessage());
        }
    }

    public void replaceEbayResourcesUrl(List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s, String shopCode) {
        if (ObjUtil.isEmpty(ebayListingGoodsResourceV2s)) {
            return;
        }
        //删除为空的resource
        ebayListingGoodsResourceV2s
                .stream()
                .filter(resource -> StrUtil.isBlank(resource.getResourceUrl()))
                .forEach(resource -> {
                    ebayListingGoodsResourceV2Service.deleteEbayListingGoodsResourceV2ById(resource.getId());
                });
        if (ObjUtil.isEmpty(ebayListingGoodsResourceV2s)) {
            return;
        }
        //筛出不为空的resource
        ebayListingGoodsResourceV2s = ebayListingGoodsResourceV2s
                .stream()
                .filter(resource -> StrUtil.isNotEmpty(resource.getResourceUrl()))
                .collect(Collectors.toList());
        if (StrUtil.isNotEmpty(shopCode)) {
            //eps图片上传
            ebayEpsImageUpload(ebayListingGoodsResourceV2s, shopCode);
            return;
        }
        for (EbayListingGoodsResourceV2 resource : ebayListingGoodsResourceV2s) {
            changeEbayLink(resource);
        }
    }

    private void changeEbayLink(EbayListingGoodsResourceV2 resource) {
        String url = retryable.retryable(() -> imageUrlChange(resource.getResourceUrl()));
        if (StringUtils.isEmpty(url)) {
            throw new BusinessException("图片换链失败,请稍后重试.");
        }
        resource.setResourceUrl(url);
        ebayListingGoodsResourceV2Service.updateEbayListingGoodsResourceV2(resource);
    }

    /**
     * ebay eps 图片上传
     *
     * @param ebayListingGoodsResourceV2s
     * @param shopCode
     */
    private void ebayEpsImageUpload(List<EbayListingGoodsResourceV2> ebayListingGoodsResourceV2s, String shopCode) {
        for (EbayListingGoodsResourceV2 goodsResource : ebayListingGoodsResourceV2s) {
            //url包含ebay域名
            if (goodsResource.getResourceUrl().contains("https://i.ebayimg.com")) {
                String ebayURL = handleEpsUrl(goodsResource.getResourceUrl());
                if (ebayURL.contains("$_57.PNG") && !ebayURL.contains("?set_id")) {
                    String url = ossChangeAndEpsUploadUrl(shopCode, ebayURL, String.valueOf(System.currentTimeMillis()), Objects.equals(goodsResource.getIsMain(), 1));
                    ebayURL = handleEpsUrl(url);
                }
                goodsResource.setResourceUrl(ebayURL);
                ebayListingGoodsResourceV2Service.updateEbayListingGoodsResourceV2(goodsResource);
                continue;
            }
            //非ebay域名图片进行eps上传拿到链接
            String url = epsImageUploadUrl(shopCode, goodsResource.getResourceUrl(), String.valueOf(System.currentTimeMillis()));
            //对eps 返回的图片进行规则处理
            goodsResource.setResourceUrl(handleEpsUrl(url));
            ebayListingGoodsResourceV2Service.updateEbayListingGoodsResourceV2(goodsResource);
        }
    }
}