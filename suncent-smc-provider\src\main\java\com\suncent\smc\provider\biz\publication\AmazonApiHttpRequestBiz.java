package com.suncent.smc.provider.biz.publication;


import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alicp.jetcache.anno.CacheInvalidate;
import com.alicp.jetcache.anno.CacheType;
import com.alicp.jetcache.anno.Cached;
import com.google.common.collect.Lists;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.enums.PublishType;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.common.utils.http.OkHttpUtil;
import com.suncent.smc.persistence.amazon.service.IAmazonFrontDetailService;
import com.suncent.smc.persistence.amazon.service.IAmazonFrontDetailV2Service;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingJSONFeedVO;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.provider.biz.configuration.domain.DefinitionsDTO;
import com.suncent.smc.provider.biz.publication.dto.AmazonVcDfInventorySubmitDTO;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/11/01 11:22
 */
@Slf4j
@Component
public class AmazonApiHttpRequestBiz {

    @Value("${api.amazon-vc-ip}")
    private String amazonVcIp;
    @Value("${api.amazon-sc-ip}")
    private String amazonScIp;
    @Value("${api.amazon-delete-v2-url}")
    private String amazonDeleteV2Url;

    @Value("${api.amazon-update-v2-url}")
    private String amazonUpdateV2Url;
    @Value("${api.amazon-select-v2-url}")
    private String amazonSelectV2Url;

    @Value("${api.amazon-upload-v2-url}")
    private String amazonUploadV2Url;

    @Value("${api.amazon-category-get-attribute-url}")
    private String getCategoryAttributeUrl;
    @Value("${api.amazon-category-get-category-info-url}")
    private String getCategoryInfoUrl;
    @Value("${api.amazon-real-time-sales-report-url}")
    private String realTimeSalesReportUrl;
    @Value("${api.amazon-vc-detail-v2-url}")
    protected String getVCDetailV2Url;
    @Value("${api.amazon-vc-upload-image}")
    public String uploadVCImage;

    @Value("${api.submit-inventory-update-url}")
    public String submitInventoryUpdate;

    @Value("${api.get-transaction-status-call-url}")
    public String getTransactionStatusCall;

    @Autowired
    public SpringTaskRetry retryable;
    @Value("${api.amazon-detail-url}")
    public String getDetailUrl;
    @Value("${api.amazon-vc-detail-url}")
    public String getVCDetailUrl;

    @Value("${api.amazon-vc-all-listing-url}")
    public String getAllListingUrl;

    @Value("${api.amazon-front-detail-url}")
    public String getFrontDetailUrl;


    @Autowired
    private IAmazonFrontDetailService amazonFrontDetailService;

    @Autowired
    private IAmazonFrontDetailV2Service amazonFrontDetailV2Service;

    @Autowired
    private IGoodsHeadService goodsHeadService;
    /**
     * 获取sellerId  sc 直接为空
     * @param publishType
     * @return
     */
    private String getSellerId(Integer publishType) {
        String sellerId = "";
        if (ObjUtil.equals(publishType, PublishType.VCDF.getType())){
            sellerId = "WM741";
        }
        if (ObjUtil.equals(publishType, PublishType.VCPO.getType())){
            sellerId = "IH75B";
        }
        return sellerId;
    }


    /**
     * 获取url
     * @param publishType
     * @param url
     * @return
     */
    private String getUrl(Integer publishType,String url) {
        if (ObjUtil.equals(publishType, PublishType.VCDF.getType())){
            return amazonVcIp +url;
        }
        if (ObjUtil.equals(publishType, PublishType.VCPO.getType())){
            return amazonVcIp +url;
        }
        return amazonScIp + url;
    }

    /**
     * 删除商品
     * @param shopCode
     * @param publishType
     * @param platformGoodsCode
     * @return
     */
    public void deleteSkuV2(String shopCode,Integer publishType, String platformGoodsCode) {
        if (ObjUtil.isEmpty(shopCode) || ObjUtil.isEmpty(platformGoodsCode) || ObjUtil.isEmpty(publishType)){
            return;
        }
        if (!EnvUtils.isProdProfile()) {
            log.info("非生产环境，跳过删除商品sku");
            return;
        }
        String url = getUrl(publishType,  amazonDeleteV2Url);
        String sellerId = getSellerId(publishType);

        AmazonListingVO amazonListingVO = new AmazonListingVO();
        amazonListingVO.setSellerId(sellerId);
        amazonListingVO.setPlatformGoodsCode(platformGoodsCode);
        amazonListingVO.setShopCode(shopCode);

        String result = HttpUtils.postV2(url, JSON.toJSONString(amazonListingVO));
        if(ObjUtil.isEmpty(result)){
            return;
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            throw new RuntimeException("删除商品sku失败"+ajaxResult.get(AjaxResult.MSG_TAG));
        }
    }

    public AjaxResult updateApi(String platformGoodsCode, String shopCode, String publishType, List<AmazonListingJSONFeedVO.Attributes> attributesParam, String productType) {
        AmazonListingJSONFeedVO feed = AmazonListingJSONFeedVO.builder()
                .sku(platformGoodsCode)
                .productType(productType)
                .sellerCode(shopCode)
                .attributeList(attributesParam)
                .build();

        AjaxResult ajaxResult = updateSkuV2(feed, shopCode, Integer.valueOf(publishType), platformGoodsCode);
        if(!ajaxResult.isSuccess()){
            throw new BusinessException(String.valueOf(ajaxResult.get(AjaxResult.MSG_TAG)));
        }
        return ajaxResult;
    }

    public AjaxResult updateSkuV2(AmazonListingJSONFeedVO feed,String shopCode,Integer publishType, String platformGoodsCode) {
        AjaxResult ajaxResult = updateSkuV2(feed, shopCode, publishType, platformGoodsCode, false);
        if(!ajaxResult.isSuccess()){
            String logMsg = String.format("shopCode: %s, publishType: %s, platformGoodsCode: %s", shopCode, publishType, platformGoodsCode);
            // {"issues":[{"attributeNames":["product_type"],"code":"4005033","message":"The value for 'Product Type' cannot be edited. Revert the value to 'VEHICLE_LIGHT_ASSEMBLY' and resubmit.","severity":"ERROR"}],"sku":"3217LAH3165US","status":"INVALID","submissionId":"c30dbd01da374d27a8256c0169c12a4f"}
            String msg = (String) ajaxResult.get(AjaxResult.MSG_TAG);
            if (StrUtil.isBlank(msg)) {
                log.error(logMsg +"接口异常：{}", ajaxResult);
                throw new BusinessException("请求失败");
            }
            // 如果是由于product_type导致的错误，则尝试使用提示的product_type进行更新
            boolean canRetry = canRetry(msg, logMsg, feed);
            if (canRetry) {
                ajaxResult = updateSkuV2(feed, shopCode, publishType, platformGoodsCode, true);
                if (!ajaxResult.isSuccess()) {
                    throw new BusinessException(String.valueOf(ajaxResult.get(AjaxResult.MSG_TAG)));
                }
            }else{
                throw new BusinessException(msg);
            }
        }
        return ajaxResult;
    }


    private boolean canRetry(String msg, String logMsg, AmazonListingJSONFeedVO feed) {
        try {
            if (StrUtil.isBlank(msg) || !msg.contains("The value for 'Product Type' cannot be edited. Revert the value to")) {
                return false;
            }

            JSONObject jsonObject = JSON.parseObject(msg);
            if (!jsonObject.containsKey("issues")) {
                return false;
            }

            JSONArray issues = jsonObject.getJSONArray("issues");
            boolean isProductType = false;
            String productType = null;
            for (int i = 0; i < issues.size(); i++) {
                JSONObject issue = issues.getJSONObject(i);
                // message 不包含 The value for 'Product Type' cannot be edited. Revert the value to 则跳过
                if (!issue.containsKey("message") || !issue.getString("message").contains("The value for 'Product Type' cannot be edited. Revert the value to")) {
                    continue;
                }
                if (issue.containsKey("attributeNames") && issue.getJSONArray("attributeNames").size() > 0) {
                    for (int j = 0; j < issue.getJSONArray("attributeNames").size(); j++) {
                        String attributeName = issue.getJSONArray("attributeNames").getString(j);
                        if (attributeName.equals("product_type")) {
                            isProductType = true;
                            productType = parseProductType(issue.getString("message"));
                            break;
                        }
                    }
                }
            }
            if (isProductType && StrUtil.isNotBlank(productType)) {
                // 尝试获取product_type的值用来更新
                feed.setProductType(productType);
                return true;
            }
        } catch (Exception e) {
            log.error(logMsg +", canRetry error: {}", e);
        }
        return false;
    }

    // 正则表达式
    private static final Pattern PRODUCT_TYPE_PATTERN = Pattern.compile("The value for 'Product Type' cannot be edited. Revert the value to '(.*)' and resubmit.");

    private String parseProductType(String message) {
        // The value for 'Product Type' cannot be edited. Revert the value to 'VEHICLE_LIGHT_ASSEMBLY' and resubmit.
        Matcher matcher = PRODUCT_TYPE_PATTERN.matcher(message);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }


    /**
     * 更新商品(部分更新)
     * @param feed
     * @param shopCode
     * @param publishType
     * @param platformGoodsCode
     * @return
     */
    public AjaxResult updateSkuV2(AmazonListingJSONFeedVO feed,String shopCode,Integer publishType, String platformGoodsCode, boolean throwException) {
        if (ObjUtil.isEmpty(shopCode) || ObjUtil.isEmpty(platformGoodsCode) || ObjUtil.isEmpty(publishType)){
            throw new RuntimeException("更新商品sku失败,参数缺失.");
        }
        if (!EnvUtils.isProdProfile()) {
            return null;
        }
        String url = getUrl(publishType,  amazonUpdateV2Url);
        String sellerId = getSellerId(publishType);

        feed.setSellerId(sellerId);

        String result = HttpUtils.postV2(url, JSON.toJSONString(feed));
        if(ObjUtil.isEmpty(result)){
            throw new RuntimeException("更新商品sku失败,返回空数据.");
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            if (throwException) {
                throw new RuntimeException("更新商品sku失败"+ajaxResult.get(AjaxResult.MSG_TAG));
            }
        }
        return ajaxResult;
    }


    /**
     * 获取商品详情
     * @param shopCode
     * @param publishType  sc链接可以为空
     * @param platformGoodsCode
     */
    public AjaxResult getDetailBySkuV2(String shopCode,Integer publishType, String platformGoodsCode) {
        if (ObjUtil.isEmpty(shopCode) || ObjUtil.isEmpty(platformGoodsCode)){
            throw new RuntimeException("通过sku查询商品详情失败,参数缺失.");
        }
        String url = getUrl(publishType,  amazonSelectV2Url);
        String sellerId = getSellerId(publishType);

        AmazonListingVO amazonListingVO = new AmazonListingVO();
        amazonListingVO.setSellerId(sellerId);
        amazonListingVO.setPlatformGoodsCode(platformGoodsCode);
        amazonListingVO.setShopCode(shopCode);

//        String result = HttpUtils.post(url, JSON.toJSONString(amazonListingVO));
        // log打印耗时
        long startTime = System.currentTimeMillis();
        String result = retryable.retryableToApiMsg(() -> HttpUtils.postV2(url, JSON.toJSONString(amazonListingVO)));
        long endTime = System.currentTimeMillis();
        log.info("通过sellerSku获取详情耗时:{}ms", endTime - startTime);

        if(ObjUtil.isEmpty(result)){
            throw new RuntimeException("通过sellerSku获取详情为空.");
        }
        AjaxResult detailResult = JSONObject.parseObject(result, AjaxResult.class);
        if( !detailResult.isSuccess() ){
            log.error("通过sellerSku获取详情失败,店铺为:{},platformGoodsCode:{},异常信息为:{}", shopCode, platformGoodsCode,detailResult.get(AjaxResult.MSG_TAG));
            return detailResult;
        }
        return detailResult;
    }

    /**
     * 上传商品
     * @return
     */
    public AjaxResult uploadSkuV2(Integer publishType, AmazonListingJSONFeedVO feed) {
        if (ObjUtil.isEmpty(feed)){
            throw new RuntimeException("上传商品sku失败,参数缺失.");
        }
        if (!EnvUtils.isProdProfile()) {
            int sleepTime = RandomUtils.nextInt(5000, 6000);
            log.info("非生产环境，模拟耗时{}ms", sleepTime);
            try {
                Thread.sleep(sleepTime);
            } catch (InterruptedException e) {
                log.error("上传商品sku失败,模拟耗时失败,异常信息为:{}", e.getMessage());
            }
            return AjaxResult.success();
        }
        String url = getUrl(publishType,  amazonUploadV2Url);
        String sellerId = getSellerId(publishType);

        feed.setSellerId(sellerId);

        String result = HttpUtils.postV2(url, JSON.toJSONString(feed));
        if(ObjUtil.isEmpty(result)){
            throw new RuntimeException("更新商品sku失败,返回空数据.");
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        return ajaxResult;
    }


    /**
     * 上传商品
     * @return
     */
    public AjaxResult uploadSkuV2Wapper(Integer publishType, AmazonListingJSONFeedVO feed) {
        if (ObjUtil.isEmpty(feed)){
            throw new RuntimeException("上传商品sku失败,参数缺失.");
        }
        if (!EnvUtils.isProdProfile()) {
            log.info("非生产环境,不上传链接");
            try {
                Thread.sleep(10000);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            return AjaxResult.success();
        }

        String url = getUrl(publishType,  amazonUploadV2Url);
        String sellerId = getSellerId(publishType);
        feed.setSellerId(sellerId);
        String result = HttpUtils.postV2(url, JSON.toJSONString(feed));
        if(ObjUtil.isEmpty(result)){
            throw new RuntimeException("更新商品sku失败,返回空数据.");
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            String logMsg = String.format("shopCode: %s, publishType: %s, platformGoodsCode: %s", feed.getSellerCode(), publishType, feed.getSku());
            // {"issues":[{"attributeNames":["product_type"],"code":"4005033","message":"The value for 'Product Type' cannot be edited. Revert the value to 'VEHICLE_LIGHT_ASSEMBLY' and resubmit.","severity":"ERROR"}],"sku":"3217LAH3165US","status":"INVALID","submissionId":"c30dbd01da374d27a8256c0169c12a4f"}
            String msg = (String) ajaxResult.get(AjaxResult.MSG_TAG);
            if (StrUtil.isBlank(msg)) {
                log.error(logMsg +"接口异常：{}", ajaxResult);
                return ajaxResult;
            }
            // 如果是由于product_type导致的错误，则尝试使用提示的product_type进行更新
            boolean canRetry = canRetry(msg, logMsg, feed);
            if (canRetry) {
                result = HttpUtils.postV2(url, JSON.toJSONString(feed));
                if(ObjUtil.isEmpty(result)){
                    throw new RuntimeException("更新商品sku失败,返回空数据.");
                }
                ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
                return ajaxResult;
            }
        }
        return ajaxResult;
    }


    /**
     * Amazon属性模板
     * @param shopCode
     * @param productType
     * @return
     */
    @Cached(name = "AmazonApiHttpRequestBiz:getAMAttributesResult", key = "#shopCode+'_'+#productType", cacheType = CacheType.REMOTE, expire = 1, timeUnit = TimeUnit.DAYS, cacheNullValue= false, localLimit = 50)
    public AjaxResult getAMAttributesResult(String shopCode, String productType) {
        Map<String, String> detailMap = new HashMap<>();
        detailMap.put("sellerCode", shopCode);
        detailMap.put("productType", productType);
        String url = shopCode.contains("VC") ? amazonVcIp + getCategoryAttributeUrl : amazonScIp + getCategoryAttributeUrl;
        String result = HttpUtils.postV2(url, JSON.toJSONString(detailMap));
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess() ){
            log.error("亚马逊定时处理商品同步--通过productType获取品类配置失败,店铺为:{},productType为:{},异常信息为:{}", shopCode, productType,ajaxResult.get(AjaxResult.MSG_TAG));
            return null;
        }
        return ajaxResult;
    }

    /**
     * 缓存获取Amazon属性模板，如果缓存失效则重新获取
     * @param shopCode
     * @param productType
     * @return
     */
    public AjaxResult getAMAttributesResultCache(String shopCode, String productType) {
        AmazonApiHttpRequestBiz amazonApiHttpRequestBiz = (AmazonApiHttpRequestBiz) AopContext.currentProxy();
        AjaxResult amAttributesResult = amazonApiHttpRequestBiz.getAMAttributesResult(shopCode, productType);
        if (!amAttributesResult.isSuccess()) {
            return amAttributesResult;
        }
        // 获取schema的具体内容
        String resource = amazonApiHttpRequestBiz.getAMAttributesResultDetail(shopCode, productType, "schema");
        if(StringUtils.isNotBlank(resource) && resource.contains("The provided token has expired.")) {
            amazonApiHttpRequestBiz.deleteAMattributesResult(shopCode, productType);
            amazonApiHttpRequestBiz.deleteAMAttributesResultDetail(shopCode, productType, "schema");
            amAttributesResult = amazonApiHttpRequestBiz.getAMAttributesResult(shopCode, productType);
        }
        return amAttributesResult;
    }


    /**
     * 通过amazon类目主节点id获取对应类目层级详细信息
     * @param siteCode
     * @param amazonCategoryId
     * @return
     */
    public AjaxResult getCategoryInfoResult(String siteCode, String amazonCategoryId) {
        Map<String, String> detailMap = new HashMap<>();
        detailMap.put("siteCode", siteCode);
        detailMap.put("amazonCategoryId", amazonCategoryId);
        String url = amazonScIp + getCategoryInfoUrl;
        String result = HttpUtils.postV2(url, JSON.toJSONString(detailMap));
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if( !ajaxResult.isSuccess() ){
            log.error("通过amazon类目主节点id获取对应类目层级详细信息失败,siteCode为:{},amazonCategoryId为:{},异常信息为:{}", siteCode, amazonCategoryId,ajaxResult.get(AjaxResult.MSG_TAG));
            return ajaxResult;
        }
        return ajaxResult;

    }

    @CacheInvalidate(name = "AmazonApiHttpRequestBiz:getAMAttributesResult", key = "#shopCode+'_'+#productType")
    public void deleteAMattributesResult(String shopCode, String productType) {
        // 删除缓存
    }

    /**
     * amazon属性模板的内容
     * @param shopCode
     * @param productType
     * @param source
     * @return
     */
    @Cached(name = "AmazonApiHttpRequestBiz:getAMAttributesResult:detail", key = "#shopCode+'_'+#productType+'_'+#source", cacheType = CacheType.REMOTE, expire = 1, timeUnit = TimeUnit.DAYS, cacheNullValue= false, localLimit = 50)
    public String getAMAttributesResultDetail(String shopCode, String productType, String source) {
        AjaxResult amAttributesResult = ((AmazonApiHttpRequestBiz) AopContext.currentProxy()).getAMAttributesResult(shopCode, productType);
        if (!amAttributesResult.isSuccess()) {
            return null;
        }

        DefinitionsDTO definitionsDTO = JSONUtil.toBean(amAttributesResult.get(AjaxResult.DATA_TAG).toString(), DefinitionsDTO.class, false);
        DefinitionsDTO.SchemaDTO schema = definitionsDTO.getSchema();
        String resource = schema.getLink().getResource();
        return OkHttpUtil.get(resource);
    }

    @CacheInvalidate(name = "AmazonApiHttpRequestBiz:getAMAttributesResult:detail", key = "#shopCode+'_'+#productType+'_'+#source")
    public void deleteAMAttributesResultDetail(String shopCode, String productType, String source) {
        // 删除缓存
    }


    public AjaxResult upoladImageAjaxResult(Integer publishType,String resourceUrl,  String shopCode, Integer count) {
        String url = getUrl(publishType, uploadVCImage);
        Map<String, String> formData = new HashMap<>();
        formData.put("siteCode", "US");
        formData.put("shopCode", shopCode);
        formData.put("resourceUrl", resourceUrl);

        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "multipart/form-data");
        String result = OkHttpUtil.postForm(url, formData, headers);
        AjaxResult ajaxResult = JSONUtil.toBean(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()) {
            if (count > 2) {
                log.error("AM图片换链失败,imageUrl:{},errorMsg:{}",resourceUrl, ajaxResult.get(AjaxResult.MSG_TAG));
                throw new RuntimeException("AM图片换链失败:"+ajaxResult.get(AjaxResult.MSG_TAG));
            }
            upoladImageAjaxResult(publishType, resourceUrl, shopCode, count + 1);
        }
        return ajaxResult;
    }

    public AjaxResult getApiListing(String shopCode, List<String> skus, List<String> asinList, Integer limit, Long lastId) {
        String url;
        if (shopCode.contains("VC")) {
            url = amazonVcIp + getAllListingUrl;
        } else {
            url = amazonScIp + getAllListingUrl;
        }
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("sellerCode", shopCode);
        if (shopCode.contains("DE")) {
            detailMap.put("site", "DE");
        }else if (shopCode.contains("MX")) {
            detailMap.put("site", "MX");
        } else if (shopCode.contains("UK")) {
            detailMap.put("site", "UK");
        }else {
            detailMap.put("site", "US");
        }
        if (ObjUtil.isNotEmpty(skus)) {
            detailMap.put("skuList", skus);
        }
        if (ObjUtil.isNotEmpty(asinList)) {
            detailMap.put("asinList", asinList);
        }
        if (ObjUtil.isNotEmpty(limit)) {
            detailMap.put("limit", limit);
        }
        if (lastId != null) {
            detailMap.put("lastId", lastId);
        }
        String result = HttpUtils.postV2(url, JSON.toJSONString(detailMap));
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if( !ajaxResult.isSuccess() ){
            log.error("获取vc所有listing失败,shopCode为:{},异常信息为:{}", shopCode,ajaxResult.get(AjaxResult.MSG_TAG));
            return ajaxResult;
        }
        return ajaxResult;
    }


    /**
     * 提交vc库存更新请求
     */
    public AjaxResult submitVcInventoryUpdate(AmazonVcDfInventorySubmitDTO submitDTO) {
        String result = HttpUtils.post(submitInventoryUpdate, JSON.toJSONString(submitDTO));
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if (!ajaxResult.isSuccess()) {
            log.error("提交vc库存更新请求失败,请求参数为:{},异常信息为:{}", JSON.toJSONString(submitDTO), ajaxResult.get(AjaxResult.MSG_TAG));
            return ajaxResult;
        }
        return ajaxResult;
    }

    /**
     * 获取vc库存更新结果
     */
    public AjaxResult getVcInventoryUpdateResult(AmazonVcDfInventorySubmitDTO submitDTO) {
        String result = HttpUtils.post(getTransactionStatusCall, JSON.toJSONString(submitDTO));
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if (!ajaxResult.isSuccess()) {
            log.error("获取vc库存更新结果失败,请求参数为:{},异常信息为:{}", JSON.toJSONString(submitDTO), ajaxResult.get(AjaxResult.MSG_TAG));
            return ajaxResult;
        }
        return ajaxResult;
    }

    public AjaxResult listRealTimeSalesReport(String shopCode, List<String> asins) {
        String url = amazonVcIp + realTimeSalesReportUrl;
        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("shopCode", shopCode);
        detailMap.put("asins", asins);
        detailMap.put("siteCode", "US");
        String result = HttpUtils.post(url, JSON.toJSONString(detailMap));
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if( !ajaxResult.isSuccess() ){
            log.error("获取亚马逊实时销售报告失败,shopCode为:{},asins为:{},异常信息为:{}", shopCode, asins,ajaxResult.get(AjaxResult.MSG_TAG));
            return ajaxResult;
        }
        return ajaxResult;
    }

    /**
     * Amazon 获取商品详情-AM后台接口数据
     * @param shopCode
     * @param asin
     * @return
     */
    public AjaxResult getBackendDetailResult(String shopCode, Integer publishType, String asin, String sku) {
        if(ObjUtil.isEmpty(shopCode)){
            return AjaxResult.error("店铺为空");
        }
        if (ObjUtil.isEmpty(asin) && ObjUtil.isEmpty(sku)){
            return AjaxResult.error("asin和sku都为空");
        }
        if (ObjUtil.isNotEmpty(asin) && ObjUtil.isNotEmpty(sku)){
            return AjaxResult.error("asin和sku不能同时存在");
        }
        String sellerId = getSellerId(publishType);
        String url = getUrl(publishType, getVCDetailV2Url);


        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("shopCode", shopCode);
        detailMap.put("sellerId", sellerId);
        if (StrUtil.isNotBlank(asin)) {
            List<String> identifiers = Lists.newArrayList(asin);
            detailMap.put("identifiers", identifiers);
            detailMap.put("identifiersType", "ASIN");
        }
        if (StrUtil.isNotBlank(sku)) {
            List<String> identifiers = Lists.newArrayList(sku);
            detailMap.put("identifiers", identifiers);
            detailMap.put("identifiersType", "SKU");
        }

        String detailString = retryable.retryableToApiMsg(() -> HttpUtils.postV2(url, JSON.toJSONString(detailMap)));
        AjaxResult detailResult = JSONObject.parseObject(detailString, AjaxResult.class);
        if( !detailResult.isSuccess() ){
            log.error("亚马逊定时处理商品同步--通过asin获取详情失败,店铺为:{},asin为:{},sku:{},异常信息为:{}", shopCode, asin, sku,detailResult.get(AjaxResult.MSG_TAG));
            return detailResult;
        }
        JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
        if (Objects.isNull(itemInfo)){
            return detailResult;
        }
        Integer numberOfResults = itemInfo.getInteger("numberOfResults");
        if (Objects.isNull(numberOfResults) || numberOfResults == 0){
            return detailResult;
        }
        if (numberOfResults > 2){
            log.info("亚马逊定时处理商品同步--通过asin获取详情,店铺为:{},asin为:{},sku:{},获取到多个商品详情", shopCode, asin, sku);
        }
        return detailResult;
    }


    /**
     * Amazon 获取商品详情
     * @param shopCode
     * @param asin
     * @return
     */
    public AjaxResult getFrontDetailResult(String shopCode,Integer publishType, String asin, String sku) {
        if(ObjUtil.isEmpty(shopCode)){
            return AjaxResult.error("店铺为空");
        }
        if (ObjUtil.isEmpty(asin) && ObjUtil.isEmpty(sku)){
            return AjaxResult.error("asin和sku不能都为空");
        }
        if (ObjUtil.isNotEmpty(asin) && ObjUtil.isNotEmpty(sku)){
            return AjaxResult.error("asin和sku不能同时存在");
        }
        String sellerId = getSellerId(publishType);
        String url = getUrl(publishType, getFrontDetailUrl);


        Map<String, Object> detailMap = new HashMap<>();
        detailMap.put("shopCode", shopCode);
        detailMap.put("sellerId", sellerId);
        if (StrUtil.isNotBlank(asin)) {
            List<String> identifiers = Lists.newArrayList(asin);
            detailMap.put("identifiers", identifiers);
            detailMap.put("identifiersType", "ASIN");
        }
        if (StrUtil.isNotBlank(sku)) {
            List<String> identifiers = Lists.newArrayList(sku);
            detailMap.put("identifiers", identifiers);
            detailMap.put("identifiersType", "SKU");
        }

        String detailString = retryable.retryableToApiMsg(() -> HttpUtils.postV2(url, JSON.toJSONString(detailMap)));
        AjaxResult detailResult = JSONObject.parseObject(detailString, AjaxResult.class);
        if( !detailResult.isSuccess() ){
            log.error("通过getFrontDetailResult获取详情失败,店铺为:{},asin为:{},sku: {},异常信息为:{}", shopCode, asin, sku,detailResult.get(AjaxResult.MSG_TAG));
            return detailResult;
        }
        JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
        if (Objects.isNull(itemInfo)){
            return detailResult;
        }
        Integer numberOfResults = itemInfo.getInteger("numberOfResults");
        if (Objects.isNull(numberOfResults) || numberOfResults == 0){
            return detailResult;
        }
        if (numberOfResults > 2){
            log.info("通过getFrontDetailResult获取详情失败,店铺为:{},asin为:{},sku:{},获取到多个商品详情", shopCode, asin,sku);
        }
        return detailResult;
    }

    /**
     * 通过asin查询数据
     *
     * @param shopCode
     * @param asin
     * @param publishType
     * @param detailResultSkuJsonObj
     * @return
     */
    public AjaxResult getDetailResult(String shopCode, String asin, Integer publishType, JSONObject detailResultSkuJsonObj) {
        Map<String, String> detailMap = new HashMap<>();
        detailMap.put("sellerCode", shopCode);
        detailMap.put("asin", asin);
        String url= shopCode.startsWith("VC") ? getVCDetailUrl : getDetailUrl;
        String detailString = retryable.retryableToApiMsg(() -> HttpUtils.postV2(url, JSON.toJSONString(detailMap)));
        AjaxResult detailResult = JSONObject.parseObject(detailString, AjaxResult.class);
        if( !detailResult.isSuccess() ){
            log.error("亚马逊定时处理商品同步--通过asin获取详情失败,店铺为:{},asin为:{},异常信息为:{}", shopCode, asin,detailResult.get(AjaxResult.MSG_TAG));
            return detailResult;
        }
        //没有发布类型直接出去
        if (ObjUtil.isEmpty(publishType)){
            return detailResult;
        }

        //有发布类型  在新增补齐一下属性
        try {
            if (detailResultSkuJsonObj != null && detailResultSkuJsonObj.containsKey("attributes")) {
                //获取新版属性
                JSONObject attributes = detailResultSkuJsonObj.getJSONObject("attributes");

                //放入旧版属性
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
                jsonObject.put("attributes", attributes);
                detailResult.put(AjaxResult.DATA_TAG, jsonObject);
                return detailResult;
            }
        } catch (Exception e) {
            log.error("获取新版属性失败,shopCode:{},asin:{}",shopCode,asin,e);
            return detailResult;
        }

        return detailResult;
    }

    /**
     * 异步获取后台商品详情 - 直接返回OkHttp的CompletableFuture
     */
    public CompletableFuture<AjaxResult> getBackendDetailResultAsync(String shopCode, Integer publishType, String asin, String sku) {
        // 参数校验
        if(ObjUtil.isEmpty(shopCode)){
            return CompletableFuture.completedFuture(AjaxResult.error("店铺为空"));
        }
        if (ObjUtil.isEmpty(asin) && ObjUtil.isEmpty(sku)){
            return CompletableFuture.completedFuture(AjaxResult.error("asin和sku都为空"));
        }
        if (ObjUtil.isNotEmpty(asin) && ObjUtil.isNotEmpty(sku)){
            return CompletableFuture.completedFuture(AjaxResult.error("asin和sku不能同时存在"));
        }

        try {
            String sellerId = getSellerId(publishType);
            String url = getUrl(publishType, getVCDetailV2Url);

            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("shopCode", shopCode);
            detailMap.put("sellerId", sellerId);
            if (StrUtil.isNotBlank(asin)) {
                List<String> identifiers = Lists.newArrayList(asin);
                detailMap.put("identifiers", identifiers);
                detailMap.put("identifiersType", "ASIN");
            }
            if (StrUtil.isNotBlank(sku)) {
                List<String> identifiers = Lists.newArrayList(sku);
                detailMap.put("identifiers", identifiers);
                detailMap.put("identifiersType", "SKU");
            }

            // 直接使用OkHttp的异步请求，不再嵌套CompletableFuture
            return OkHttpUtil.postJsonAsync(url, JSON.toJSONString(detailMap))
                .thenApply(detailString -> {
                    try {
                        AjaxResult detailResult = JSONObject.parseObject(detailString, AjaxResult.class);
                        if( !detailResult.isSuccess() ){
                            log.error("异步获取后台详情失败,店铺为:{},asin为:{},sku:{},异常信息为:{}",
                                shopCode, asin, sku, detailResult.get(AjaxResult.MSG_TAG));
                            return detailResult;
                        }

                        JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
                        if (Objects.isNull(itemInfo)){
                            return detailResult;
                        }
                        Integer numberOfResults = itemInfo.getInteger("numberOfResults");
                        if (Objects.isNull(numberOfResults) || numberOfResults == 0){
                            return detailResult;
                        }
                        if (numberOfResults > 2){
                            log.info("异步获取后台详情,店铺为:{},asin为:{},sku:{},获取到多个商品详情", shopCode, asin, sku);
                        }
                        return detailResult;
                    } catch (Exception e) {
                        log.error("解析后台详情响应异常,店铺为:{},asin为:{},sku:{}", shopCode, asin, sku, e);
                        return AjaxResult.error("解析响应异常: " + e.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("异步获取后台详情异常,店铺为:{},asin为:{},sku:{}", shopCode, asin, sku, throwable);
                    return AjaxResult.error("异步获取后台详情异常: " + throwable.getMessage());
                });

        } catch (Exception e) {
            log.error("构建后台详情请求异常,店铺为:{},asin为:{},sku:{}", shopCode, asin, sku, e);
            return CompletableFuture.completedFuture(AjaxResult.error("构建请求异常: " + e.getMessage()));
        }
    }

    /**
     * 异步获取前台商品详情 - 直接返回OkHttp的CompletableFuture
     */
    public CompletableFuture<AjaxResult> getFrontDetailResultAsync(String shopCode, Integer publishType, String asin, String sku) {
        // 参数校验
        if(ObjUtil.isEmpty(shopCode)){
            return CompletableFuture.completedFuture(AjaxResult.error("店铺为空"));
        }
        if (ObjUtil.isEmpty(asin) && ObjUtil.isEmpty(sku)){
            return CompletableFuture.completedFuture(AjaxResult.error("asin和sku不能都为空"));
        }
        if (ObjUtil.isNotEmpty(asin) && ObjUtil.isNotEmpty(sku)){
            return CompletableFuture.completedFuture(AjaxResult.error("asin和sku不能同时存在"));
        }

        try {
            String sellerId = getSellerId(publishType);
            String url = getUrl(publishType, getFrontDetailUrl);

            Map<String, Object> detailMap = new HashMap<>();
            detailMap.put("shopCode", shopCode);
            detailMap.put("sellerId", sellerId);
            if (StrUtil.isNotBlank(asin)) {
                List<String> identifiers = Lists.newArrayList(asin);
                detailMap.put("identifiers", identifiers);
                detailMap.put("identifiersType", "ASIN");
            }
            if (StrUtil.isNotBlank(sku)) {
                List<String> identifiers = Lists.newArrayList(sku);
                detailMap.put("identifiers", identifiers);
                detailMap.put("identifiersType", "SKU");
            }

            // 直接使用OkHttp的异步请求，不再嵌套CompletableFuture
            return OkHttpUtil.postJsonAsync(url, JSON.toJSONString(detailMap))
                .thenApply(detailString -> {
                    try {
                        AjaxResult detailResult = JSONObject.parseObject(detailString, AjaxResult.class);
                        if( !detailResult.isSuccess() ){
                            log.error("异步获取前台详情失败,店铺为:{},asin为:{},sku:{},异常信息为:{}",
                                shopCode, asin, sku, detailResult.get(AjaxResult.MSG_TAG));
                            return detailResult;
                        }

                        JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
                        if (Objects.isNull(itemInfo)){
                            return detailResult;
                        }
                        Integer numberOfResults = itemInfo.getInteger("numberOfResults");
                        if (Objects.isNull(numberOfResults) || numberOfResults == 0){
                            return detailResult;
                        }
                        if (numberOfResults > 2){
                            log.info("异步获取前台详情,店铺为:{},asin为:{},sku:{},获取到多个商品详情", shopCode, asin, sku);
                        }
                        return detailResult;
                    } catch (Exception e) {
                        log.error("解析前台详情响应异常,店铺为:{},asin为:{},sku:{}", shopCode, asin, sku, e);
                        return AjaxResult.error("解析响应异常: " + e.getMessage());
                    }
                })
                .exceptionally(throwable -> {
                    log.error("异步获取前台详情异常,店铺为:{},asin为:{},sku:{}", shopCode, asin, sku, throwable);
                    return AjaxResult.error("异步获取前台详情异常: " + throwable.getMessage());
                });

        } catch (Exception e) {
            log.error("构建前台详情请求异常,店铺为:{},asin为:{},sku:{}", shopCode, asin, sku, e);
            return CompletableFuture.completedFuture(AjaxResult.error("构建请求异常: " + e.getMessage()));
        }
    }

    /**
     * 保存Amazon前台详情数据到双表
     * V1表：使用简单逻辑，仅用于同步
     * V2表：使用复杂逻辑，用于前端查询和属性灵活匹配
     *
     * @param shopCode     店铺编码
     * @param goodsId
     * @param asin         ASIN
     * @param sku          SKU
     * @param detailResult 详情结果
     */
    public void saveFrontDetailData(String shopCode, Long goodsId, String asin, String sku, AjaxResult detailResult) {
        try {
            // 获取数据
            Object dataObj = detailResult.get(AjaxResult.DATA_TAG);
            if (dataObj == null) {
                log.error("Amazon前台详情数据为空，跳过保存, shopCode: {}, asin: {}, sku: {}", shopCode, asin, sku);
                return;
            }

            if (StrUtil.isBlank(asin)) {
                log.error("无法确定ASIN，跳过保存前台数据, shopCode: {}, sku: {}", shopCode, sku);
                return;
            }

            if (goodsId == null) {
                log.error("无法获取headId, shopCode: {}, asin: {}, sku: {}", shopCode, asin, sku);
                return;
            }

            // 双表同步策略
            // 1. 保存到V1表（简单逻辑，用于同步）
            amazonFrontDetailService.saveAmazonFrontDetailData(
                    goodsId,
                    shopCode,
                    asin,
                    dataObj
            );

            // 2. 保存到V2表（复杂逻辑，用于前端查询）
            amazonFrontDetailV2Service.saveAmazonFrontDetailDataV2(
                    goodsId,
                    shopCode,
                    asin,
                    dataObj
            );

            log.info("成功保存Amazon前台详情数据到双表, shopCode: {}, asin: {}, sku: {}, headId: {}",
                    shopCode, asin, sku, goodsId);

        } catch (Exception e) {
            log.error("保存Amazon前台详情数据失败, shopCode: {}, asin: {}, sku: {}",
                    shopCode, asin, sku, e);
        }
    }



}
