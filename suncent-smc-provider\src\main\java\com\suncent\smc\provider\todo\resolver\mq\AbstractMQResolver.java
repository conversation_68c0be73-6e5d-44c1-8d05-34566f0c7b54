package com.suncent.smc.provider.todo.resolver.mq;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONObject;
import com.suncent.rocketmq.rebot.model.TextMessage;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.security.Md5Utils;
import com.suncent.smc.persistence.mq.domain.entity.MqConsumeRecord;
import com.suncent.smc.persistence.mq.service.IMqConsumeRecordService;
import com.suncent.smc.persistence.publication.domain.dto.TodoDataDTO;
import com.suncent.smc.persistence.publication.domain.vo.GoodsHeadCountVO;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.todo.service.IBrandAdaptImgTodoService;
import com.suncent.smc.persistence.todo.service.IPdmChangeTodoService;
import com.suncent.smc.provider.todo.HandlerTodoComposite;
import com.suncent.smc.system.service.ISysConfigService;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractMQResolver {

    @Autowired
    protected IMqConsumeRecordService mqConsumeRecordService;
    @Autowired
    protected RedissonClient redissonClient;
    @Autowired
    protected IGoodsHeadService goodsHeadService;
    @Autowired
    protected ISysConfigService sysConfigService;
    @Autowired
    protected IPdmChangeTodoService pdmChangeTodoService;
    @Resource
    protected HandlerTodoComposite handlerTodoComposite;
    @Autowired
    protected IBrandAdaptImgTodoService brandAdaptImgTodoService;

    public TodoDataDTO execute(TodoDataDTO todoData) {
        TextMessage mqMessage = todoData.getMqMessage();
        String msg = mqMessage.getMsg();
        JSONObject jsonObject = JSONObject.parseObject(msg);
        String todoType = jsonObject.getString("todoType");
        String subType = jsonObject.getString("subType");
        String businessKey = mqMessage.getKey() + "_" + todoType + "_" + subType;

        List<String> skus = JSONObject.parseArray(jsonObject.getString("skus"), String.class);
        if (CollectionUtil.isEmpty(skus)) {
            log.error("key:{},topic :{},tag:{},todoType:{}，skus为空", mqMessage.getKey(), mqMessage.getTopic(), mqMessage.getTag(), todoType);
            return null;
        }
        skus = skus.stream().distinct().sorted().collect(Collectors.toList());
        businessKey = Md5Utils.hash(businessKey + "_" + skus);

        // 消息落库
        MqConsumeRecord mqConsumeRecord = saveMsg(mqMessage, jsonObject, businessKey);
        if (mqConsumeRecord == null) {
            log.info("key:{},topic:{},tag:{},todoType:{}，重复消费，跳过不处理", mqMessage.getKey(), mqMessage.getTopic(), mqMessage.getTag(), todoType);
            return null;
        }

        // 子类实现加锁的key
        String lockKey = null;
        try {
            lockKey = getLockKey(mqMessage, jsonObject);
        } catch (Exception ex) {
            log.error(String.format("获取锁的key失败，key:%s,topic:%s,tag:%s,todoType:%s,获取lockKey异常", mqMessage.getKey(), mqMessage.getTopic(), mqMessage.getTag(), todoType), ex);
        }
        if (StringUtils.isBlank(lockKey)) {
            log.error("获取锁的key失败，key:{},topic:{},tag:{},todoType:{},lockKey为空", mqMessage.getKey(), mqMessage.getTopic(), mqMessage.getTag(), todoType);
            makeRecordFailed(mqConsumeRecord);
            return null;
        }

        log.info("key:{},topic:{},tag:{},todoType:{}，lockKey:{}", mqMessage.getKey(), mqMessage.getTopic(), mqMessage.getTag(), todoType, lockKey);
        RLock lock = redissonClient.getLock(lockKey);
        boolean locked;
        try {
            // 尝试获取锁,第一个参数是最大等待时间，第二个参数是持有锁的时间，设置为-1表示自动续期
            locked = lock.tryLock(60, -1, TimeUnit.SECONDS);
        }
        // 捕获中断异常，一般是服务重启导致的
        catch (InterruptedException e) {
            log.error("Interrupted while trying to acquire lock for key: {}", lockKey, e);
            makeRecordFailed(mqConsumeRecord);
            return null;
        }

        try {
            // 获取锁失败，由后续定时任务补偿
            if (!locked) {
                log.warn("Failed to acquire lock for key: {}", lockKey);
                makeRecordFailed(mqConsumeRecord);
                return null;
            }

            // 标记消息为处理中
            makeRecordProcessing(mqConsumeRecord);

            // 顺序保证
            boolean oldData = isOldData(mqConsumeRecord);
            // 如果是旧数据，直接标记为成功
            if (oldData) {
                makeRecordSuccess(mqConsumeRecord);
                return null;
            }

            // 统计对应SKU是否有链接数据
            List<GoodsHeadCountVO> onlineListings = goodsHeadService.countOnlineListingGroup(skus);
            // 如果没有在线链接，直接标记为成功
            if (notExistOnlineListing(onlineListings)) {
                makeRecordSuccess(mqConsumeRecord);
                return null;
            }


            // 子类实现具体的业务逻辑
            try {
                doExecute(mqMessage, onlineListings);
                makeRecordSuccess(mqConsumeRecord);
                return todoData;
            } catch (Exception e) {
                log.error("key:{},topic:{},tag:{},todoType:{}，处理异常", mqMessage.getKey(), mqMessage.getTopic(), mqMessage.getTag(), todoType, e);
                makeRecordFailed(mqConsumeRecord);
            }

        } finally {
            try {
                if (locked) {
                    lock.unlock();
                }
            }
            // 其他线程已经释放锁
            catch (IllegalMonitorStateException e) {
                log.error("Attempted to unlock a lock not held by current thread: {}", lockKey, e);
            }
        }
        return null;
    }

    /**
     * 判断是否存在在线链接
     *
     * @param goodsHeadCountVOS
     * @return
     */
    private boolean notExistOnlineListing(List<GoodsHeadCountVO> goodsHeadCountVOS) {
        if (CollectionUtil.isEmpty(goodsHeadCountVOS)) {
            return true;
        }

        return goodsHeadCountVOS.stream().noneMatch(goodsHeadCountVO -> goodsHeadCountVO.getOnlineCount() > 0);
    }

    /**
     * 子类实现具体的业务逻辑
     *
     * @param mqMessage
     * @param onlineListings
     * @return
     */
    public abstract TodoDataDTO doExecute(TextMessage mqMessage, List<GoodsHeadCountVO> onlineListings);

    /**
     * 标记消息为成功
     *
     * @param mqConsumeRecord
     */
    protected void makeRecordSuccess(MqConsumeRecord mqConsumeRecord) {
        updateMqConsumeRecordStatus(mqConsumeRecord, MqConsumeRecord.StatusEnum.CONSUMED_SUCCESS.getCode());
    }

    /**
     * 更新消息状态
     *
     * @param mqConsumeRecord
     * @param status
     */
    protected void updateMqConsumeRecordStatus(MqConsumeRecord mqConsumeRecord, int status) {
        MqConsumeRecord updateRecord = new MqConsumeRecord();
        updateRecord.setId(mqConsumeRecord.getId());
        updateRecord.setStatus(status);
        mqConsumeRecordService.updateMqConsumeRecord(updateRecord);
    }

    /**
     * 标记消息为处理中
     *
     * @param mqConsumeRecord
     */
    private void makeRecordProcessing(MqConsumeRecord mqConsumeRecord) {
        updateMqConsumeRecordStatus(mqConsumeRecord, MqConsumeRecord.StatusEnum.CONSUMING.getCode());
    }

    /**
     * 标记消息为失败
     *
     * @param mqConsumeRecord
     */
    protected void makeRecordFailed(MqConsumeRecord mqConsumeRecord) {
        updateMqConsumeRecordStatus(mqConsumeRecord, MqConsumeRecord.StatusEnum.CONSUMED_FAILED.getCode());
    }

    /**
     * 判断是否是旧数据
     *
     * @param mqConsumeRecord
     * @return
     */
    private boolean isOldData(MqConsumeRecord mqConsumeRecord) {
        // 判断是否是旧数据
        MqConsumeRecord latestRecord = mqConsumeRecordService.getLatestRecord(mqConsumeRecord);
        if (latestRecord == null || latestRecord.getId().equals(mqConsumeRecord.getId())) {
            return false;
        }
        // 判断当前记录是否是最新的
        return mqConsumeRecord.getMqCreateTime() < latestRecord.getMqCreateTime();
    }

    protected String getLockKey(TextMessage mqMessage, JSONObject jsonObject) {
        return mqMessage.getTopic() + ":" + mqMessage.getTag() + ":" + mqMessage.getKey() + ":" + jsonObject.getString("todoType");
    }


    private MqConsumeRecord saveMsg(TextMessage mqMessage, JSONObject jsonObject, String businessKey) {
        // 消息落库
        try {
            Long createTime = jsonObject.getLong("createTime");
            String md5 = getMd5(mqMessage);
            Integer countByMd5 = mqConsumeRecordService.countByMd5(md5);
            if (countByMd5 > 0) {
                return null;
            }

            MqConsumeRecord mqConsumeRecord = buildMqConsumeRecord(mqMessage, createTime, businessKey, md5);
            mqConsumeRecordService.insertMqConsumeRecord(mqConsumeRecord);
            return mqConsumeRecord;
        } catch (DuplicateKeyException e) {
            // 捕获唯一索引冲突的异常，表明记录已存在
            log.warn("Record with MD5 already exists, skipping processing: {}", e.getMessage());
        }
        return null;
    }


    private String getMd5(TextMessage mqMessage) {
        return Md5Utils.hash( mqMessage.getTopic() + mqMessage.getTag() + mqMessage.getSource() +  mqMessage.getMsg());
    }

    private MqConsumeRecord buildMqConsumeRecord(TextMessage mqMessage, Long createTime, String businessKey, String md5) {
        MqConsumeRecord mqConsumeRecord = new MqConsumeRecord();
        mqConsumeRecord.setTopic(mqMessage.getTopic());
        mqConsumeRecord.setTag(mqMessage.getTag());
        mqConsumeRecord.setMsgKey(mqMessage.getKey());
        mqConsumeRecord.setMsg(mqMessage.getMsg());
        mqConsumeRecord.setStatus(MqConsumeRecord.StatusEnum.UNCONSUMED.getCode());
        mqConsumeRecord.setRetryCount(0);
        mqConsumeRecord.setRetryMaxCount(3);
        mqConsumeRecord.setMqCreateTime(createTime);
        mqConsumeRecord.setBusinessKey(businessKey);
        mqConsumeRecord.setMd5(md5);
        return mqConsumeRecord;
    }


}
