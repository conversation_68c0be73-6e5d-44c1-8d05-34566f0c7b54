package com.suncent.smc.persistence.configuration.store.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.suncent.smc.common.api.domain.ShortDescriptionApiRequest;
import com.suncent.smc.common.api.domain.ShortDescriptionApiResponse;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigShortDescriptionModule;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreShortDescription;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreShortDescriptionService;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.configuration.store.service.IShortDescriptionApiService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短描述配置API服务实现
 *
 * <AUTHOR>
 * @date 2024-12-24
 */
@Slf4j
@Service
public class ShortDescriptionApiServiceImpl implements IShortDescriptionApiService {

    @Autowired
    private IConfigStoreShortDescriptionService configStoreShortDescriptionService;

    @Autowired
    private IConfigStoreInfoService configStoreInfoService;

    @Autowired
    private IShopService shopService;

    @Override
    public ShortDescriptionApiResponse getShortDescriptionConfig(ShortDescriptionApiRequest request) {
        // 参数校验
        if (StringUtils.isEmpty(request.getShopCode())) {
            throw new BusinessException("店铺编码不能为空");
        }

        log.info("API请求获取短描述配置，店铺编码：{}, 平台：{}", request.getShopCode(), request.getPlatform());

        // 1. 根据店铺编码查找店铺ID
        Integer storeId = findStoreIdByShopCode(request.getShopCode());
        if (storeId == null) {
            throw new BusinessException("未找到店铺编码对应的店铺信息：" + request.getShopCode());
        }

        // 2. 查询短描述配置
        ConfigStoreShortDescription config = configStoreShortDescriptionService.selectConfigStoreShortDescriptionByStoreId(storeId);
        if (config == null) {
            throw new BusinessException("店铺" + request.getShopCode() + "未配置模块");
        }

        // 3. 构建响应数据
        return buildApiResponse(request.getShopCode(), request.getPlatform(), config);
    }

    @Override
    public ConfigStoreShortDescription getByShopCode(String shop) {
        // 1. 根据店铺编码查找店铺ID
        Integer storeId = findStoreIdByShopCode(shop);
        if (storeId == null) {
            return null;
        }

        // 2. 查询短描述配置
        return configStoreShortDescriptionService.selectConfigStoreShortDescriptionByStoreId(storeId);
    }


    /**
     * 根据店铺编码查找店铺ID
     *
     * 说明：短描述配置表中的store_id对应的是ConfigStoreInfo表的主键ID
     * 需要通过shop_code查询ConfigStoreInfo表获取对应的ID
     */
    private Integer findStoreIdByShopCode(String shopCode) {
        try {
            log.debug("根据店铺编码查找店铺配置ID：{}", shopCode);

            // 查询店铺配置信息表
            ConfigStoreInfo storeInfo = configStoreInfoService.selectConfigStoreInfoByShopCode(shopCode);

            if (storeInfo == null) {
                log.warn("未找到店铺编码对应的配置信息：{}", shopCode);
                return null;
            }

            log.debug("找到店铺配置，店铺编码：{}，配置ID：{}，平台：{}，站点：{}",
                    shopCode, storeInfo.getId(), storeInfo.getPlatform(), storeInfo.getSite());

            return storeInfo.getId();

        } catch (Exception e) {
            log.error("根据店铺编码查找店铺配置ID失败：{}", shopCode, e);
            return null;
        }
    }

    /**
     * 构建API响应数据
     */
    private ShortDescriptionApiResponse buildApiResponse(String shopCode, String platform, ConfigStoreShortDescription config) {
        ShortDescriptionApiResponse response = new ShortDescriptionApiResponse();

        // 基本信息
        response.setShopCode(shopCode);
        response.setPlatform(platform);
        response.setConfigId(config.getId());
        response.setConfigName(config.getConfigName());
        response.setConfigDescription(config.getDescription());

        // 获取店铺名称
        try {
            String shopName = shopService.selectShopNameByShopCode(shopCode);
            response.setShopName(shopName);
        } catch (Exception e) {
            log.warn("获取店铺名称失败：{}", shopCode, e);
            response.setShopName(shopCode);
        }

        // 模块配置列表
        List<ShortDescriptionApiResponse.ModuleConfig> moduleList = new ArrayList<>();
        if (CollUtil.isNotEmpty(config.getModuleList())) {
            moduleList = config.getModuleList().stream()
                    .filter(module -> module.getStatus() == 1)
                    .map(this::convertToModuleConfig)
                    .collect(Collectors.toList());
        }
        response.setModuleList(moduleList);

        log.info("成功构建短描述配置响应，店铺：{}, 平台：{}, 模块数量：{}", shopCode, platform, moduleList.size());

        return response;
    }

    /**
     * 转换模块配置
     */
    private ShortDescriptionApiResponse.ModuleConfig convertToModuleConfig(ConfigShortDescriptionModule module) {
        ShortDescriptionApiResponse.ModuleConfig config = new ShortDescriptionApiResponse.ModuleConfig();
        
        config.setModuleId(module.getId());
        config.setModuleCode(module.getModuleCode());
        config.setModuleName(module.getModuleName());
        config.setDisplayName(module.getDisplayName());
        config.setContentFormat(module.getContentFormat());
        config.setDisplayOrder(module.getDisplayOrder());
        config.setStatus(module.getStatus());
        
        // 状态描述
        config.setStatusText(module.getStatus() == 1 ? "启用" : "禁用");
        
        // 内容格式描述
        config.setContentFormatText(getContentFormatText(module.getContentFormat()));
        
        return config;
    }

    /**
     * 获取内容格式描述
     */
    private String getContentFormatText(String contentFormat) {
        if (StringUtils.isEmpty(contentFormat)) {
            return "未知格式";
        }
        
        switch (contentFormat) {
            case "text":
                return "文本";
            case "bullet":
                return "项目符号列表";
            case "numbered":
                return "项目数字列表";
            case "table":
                return "表格";
            default:
                return "未知格式";
        }
    }
}
