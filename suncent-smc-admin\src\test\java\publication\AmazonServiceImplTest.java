package publication;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.suncent.smc.SuncentSmcApplication;
import com.suncent.smc.common.constant.Constants;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.enums.PublishStatus;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.oss.IAliOssService;
import com.suncent.smc.persistence.ads.domain.ItDemand;
import com.suncent.smc.persistence.ads.service.impl.AdsServiceImpl;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.configuration.store.service.IConfigStoreInfoService;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.pdm.service.IMappingGoodsService;
import com.suncent.smc.persistence.publication.domain.dto.AmazonAllListingsReportDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingFeedVO;
import com.suncent.smc.persistence.publication.domain.vo.AmazonListingJSONFeedVO;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.persistence.todo.service.IAmazonVariantMonitoringTodoService;
import com.suncent.smc.provider.biz.consumer.AmazonListingResultHandler;
import com.suncent.smc.provider.biz.image.ImageHandleBiz;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.pdm.CreatePlatformCode;
import com.suncent.smc.provider.biz.publication.AmazonApiHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.AmazonProductBiz;
import com.suncent.smc.provider.biz.publication.BaseAmazonProductTask;
import com.suncent.smc.provider.biz.publication.PDMHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.service.impl.AmazonPlatformListingServiceImpl;
import com.suncent.smc.provider.biz.task.PullFileRelatedTaskBiz;
import com.suncent.smc.provider.biz.webhook.WebhookBiz;
import com.suncent.smc.quartz.task.listing.BiDataSyncTask;
import com.suncent.smc.quartz.task.listing.PictureChainChangeTask;
import com.suncent.smc.quartz.task.listing.am.*;
import com.suncent.smc.quartz.task.listing.eb.EbayWebhookTask;
import com.suncent.smc.quartz.task.todo.VariantMonitoringTodoTask;
import com.suncent.smc.web.controller.consumer.VcdfReportResultInventoryConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.suncent.smc.common.utils.DateUtils.dateTimeNow;

/**
 *
 * <AUTHOR>
 * @since 2023-01-11 10:00:00
 */
@Slf4j
@SpringBootTest(classes = {SuncentSmcApplication.class})
@RunWith(SpringRunner.class)
public class AmazonServiceImplTest {
    @Autowired
    IGoodsService goodsService;

    @Autowired
    IListingAttributeTempService listingAttributeTempService;

    @Autowired
    private IListingAmazonAttributeLineService listingAmazonAttributeLineService;

    @Autowired
    private IGoodsHeadService goodsHeadService;

    @Autowired
    private AmazonPlatformListingServiceImpl amazonPlatformListingService;
    @Autowired
    private VariantMonitoringTodoTask variantMonitoringTodoTask;
    @Autowired
    private IAmazonVariantMonitoringTodoService variantMonitoringTodoService;

    @Test
    public void amSaveAttribute1() {
        variantMonitoringTodoTask.variantMonitoringTodoTask();
    }
    @Autowired
    private BiDataSyncTask biDataSyncTask;
    @Autowired
    private AmazonApiHttpRequestBiz amazonApiHttpRequestBiz;

    /**
     * Amazon刊登失败后，品类配置设置必填值，一键保存
     */
//    @Test
//    public void amSaveAttribute() {
//        List<GoodsHead> heads = goodsHeadService.selectOnlineListingByPlatfromAndShopCode(PlatformTypeEnum.AM.name(),"VC1");
//        for (GoodsHead goodsHead : heads) {
//            try {
//                if (ObjectUtils.isEmpty(goodsHead.getPlatformGoodsId())){
//                    continue;
//                }
//
//                AjaxResult detailResult = amazonApiHttpRequestBiz.getDetailResult("VC1", goodsHead.getPlatformGoodsId(),null);
//                if (detailResult.isSuccess()) {
//                    JSONObject itemInfo = JSONObject.parseObject(JSON.toJSONString(detailResult.get(AjaxResult.DATA_TAG)));
//                    JSONArray relationships = itemInfo.getJSONArray("relationships");
//                    Object relationshipsObj = JSONObject.parseObject(JSON.toJSONString(relationships.get(0))).get("relationships");
//                    if (ObjUtil.isNotEmpty(relationshipsObj)){
//                        List<AmazonRelationshipsDTO> amazonRelationshipsDTOS = JSON.parseArray(JSON.toJSONString(relationshipsObj), AmazonRelationshipsDTO.class);
//                        if (amazonRelationshipsDTOS.size()>0){
//                            AmazonRelationshipsDTO amazonRelationshipsDTO = amazonRelationshipsDTOS.get(0);
//                            List<String> parentAsins = amazonRelationshipsDTO.getParentAsins();
//                            List<String> childAsins = amazonRelationshipsDTO.getChildAsins();
//                            if (CollUtil.isNotEmpty(childAsins)){
//                                variantMonitoringTodoService.insertAmazonVariantMonitoringTodos(goodsHead.getShopCode(), goodsHead.getPlatformGoodsId(), Long.valueOf(goodsHead.getCreateBy()));
//                            }
//                        }
//                    }
//
//                }
//            } catch (Exception e) {
//                log.error("平台商品ID转换异常：" + goodsHead.getPlatformGoodsId(), e);
//            }
//
//
//        }
//
//    }


    @Autowired
    private PullFileRelatedTaskBiz pullFileRelatedTaskBiz;
    @Autowired
    private IPlatformCategoryService platformCategoryService;

    @Test
    public void createPlatformCategory() {
        //new 个数组
        pullFileRelatedTaskBiz.doHandlePullFiledDictTaskV2("15732491");
//        pullFileRelatedTaskBiz.doHandlePullFiledDictTaskV2("10304191");
        System.out.println("结束");
    }

    @Autowired
    private AmazonListingAsinTask amazonListingAsinTask;

    @Test
    public void amazonListingAsinTask(){
        amazonListingAsinTask.amazonListingAsinTask();
    }



    @Autowired
    private IAliOssService aliyunOssService;
    @Value("${aliyun.oss.defaultBucketName}")
    public String defaultBucketName;
    @Autowired
    private IGoodsResourceService goodsResourceService;
    @Value("${aliyun.oss.urlPrefix}")
    public String urlPrefix;
    @Autowired
    private ImageHandleBiz imageHandleBiz;

    /**
     * 图片刷新
     * SELECT * from sc_smc_listing_goods_resource where goods_id in ( SELECT id from sc_smc_listing_goods_head where platform="AM" and publish_status=1 and del_flag=0 )
     */
    @Test
    public void imageUpdate() {
        GoodsHead goodsHead = new GoodsHead();
        goodsHead.setPlatform(PlatformTypeEnum.EB.name());
        goodsHead.setPublishStatus(1);
        List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead);

        for (GoodsHead head : goodsHeads) {
            Integer headId = head.getId();
            List<GoodsResource> resourceList = goodsResourceService.selectListingGoodsResourceByHeadId(headId);
            if (ObjectUtils.isEmpty(resourceList)) {
                continue;
            }
            for (GoodsResource goodsResource : resourceList) {
                try {
                    if (StringUtils.isEmpty(goodsResource.getResourceUrl())) {
                        continue;
                    }
                    if ( goodsResource.getResourceUrl().contains("i.ebayimg.com")) {
                        continue;
                    }
                    String url1 = imageHandleBiz.epsImageUploadUrl(head.getShopCode(), goodsResource.getResourceUrl(), System.currentTimeMillis() + goodsResource.getResourceName());

//                String newFileName = System.currentTimeMillis() + ".jpg";
//                String url = "smc/files/" + DateUtils.getDate() + "/" + newFileName;
//                imageLinkChange(url, goodsResource.getResourceUrl());
                    goodsResource.setResourceUrl(url1);
                    goodsResourceService.updateListingGoodsResource(goodsResource);
                } catch (Exception e) {
                    log.error("图片刷新失败,原因:{}", e.getMessage());
                }
            }

        }

    }

    @Autowired
    private PictureChainChangeTask pictureChainChangeTask;
    @Test
    public void imageLinkChange123() {
        pictureChainChangeTask.pictureChainChangeTask();
    }




    @Autowired
    private AdsServiceImpl adsService;

    /**
     * 回写适配数据
     */
    @Test
    public void adapte(){
        String[] asins= {"B0C7BKRRG2","B0C7BS3XTC","B0C7B97R12","B0C7BHY7XZ","B0C7BJNH21","B0C7B6S4ZF","B0C7BL313P","B0C7BF4TFX","B0C7BMBQL9","B0C7BB3NZD","B0C7BS5JJ6","B0C7BDWR5D","B0C7BH1GSC","B0C7BMCSR7","B0C7BB679J","B0C7BHKQRQ","B0C7B9931G","B0C7B6B2G1","B0C7BHHG22","B0C7BPRN17","B0C7BJGLFG","B0C7BJ8BMZ","B0C7B4PCR1","B0C7BB9Z4B","B0C7BH259M","B0C7B2Z8CM","B0C7BHRJ1Q","B0C7BHRJ1L","B0C7B69QLF","B0C7B9P7DT","B0C7BD2DCZ","B0C7BD232N","B0C7BDZ7M8","B0C7B6GYRB"};
        for (String asin :asins){
            GoodsHead goodsHead = new GoodsHead();
            goodsHead.setPlatformGoodsId(asin);
            List<GoodsHead> goodsHeads = goodsHeadService.selectListingGoodsHeadList(goodsHead);

            if (CollectionUtils.isEmpty(goodsHeads)){
                continue;
            }
            GoodsHead goodHead = goodsHeads.get(0);


            ListingAmazonAttributeLine listingAmazonAttributeLine = new ListingAmazonAttributeLine();
            listingAmazonAttributeLine.setGoodsId(goodHead.getId());
            listingAmazonAttributeLine.setTableType(0);
            List<ListingAmazonAttributeLine> amazonAttributeLines = listingAmazonAttributeLineService.selectListingAmazonAttributeLineList(listingAmazonAttributeLine);

            //回写到适配数据就不重复写了
            List<Map<String, String>> statusByAsin = adsService.getStatusByAsin(Arrays.asList(goodHead.getPlatformGoodsId()));
            if (!CollectionUtils.isEmpty(statusByAsin)){
                return;
            }

            Optional<ListingAmazonAttributeLine> attributeLine = amazonAttributeLines.stream().filter(f -> Objects.equals(f.getTableName(), "part_number")).findFirst();
            if (attributeLine.isPresent()){
                ListingAmazonAttributeLine line = attributeLine.get();

                SaleGoodsDTO query = new SaleGoodsDTO();
                query.setGoodsCode(goodHead.getPdmGoodsCode());
                Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
                ItDemand itDemand = ItDemand.builder()
                        .time(dateTimeNow("yyyyMMdd"))
                        .brandEnName(goodHead.getBrandCode())
                        .sku(goodHead.getPlatformGoodsCode())
                        .productCode(pdmGood.getProductCode())
                        .pn(line.getTableValue())
                        .asin(goodHead.getPlatformGoodsId())
                        .type(pdmGood.getClassificationCode())
                        .country(goodHead.getSiteCode())
                        .build();

                adsService.saveToAdsItDemandA(itDemand);
            }

        }

    }

    @Autowired
    private BaseAmazonProductTask baseAmazonProductTask;

    @Test
    public void uploadTest(){
        AmazonListingFeedVO amazonListingFeedVO = new AmazonListingFeedVO();
        amazonListingFeedVO.setSmcRecordId(String.valueOf(6178));
        amazonListingFeedVO.setSellerCode("US57");
        amazonListingFeedVO.setFileUrl("6178");
//        baseAmazonProductTask.uploadListingExcelToApi(amazonListingFeedVO,new ArrayList<>(),"上架");
    }
    @Autowired
    CreatePlatformCode createPlatformCode;
    @Autowired
    private IMappingGoodsService mappingGoodsService;

    /**
     * 生成平台编码 amazon刷属性
     */
    @Test
    public void CreatePlatformCode(){
        List<ListingAmazonAttributeLine> lineList = listingAmazonAttributeLineService.selectByGoodsId(1101969);
        String ids ="1101259 1101260 1101261 1101262 1101263 1101264 1101265 1101266 1101267 1101268 1101269 1101270 1101271 1101272 1101273 1101274 1101275 1101276 1101277 1101278 1101279 1101280 1101281 1101282 1101283 1101284 1101285 1101286 1101287 1101288 1101289 1101290 1101291 1101292 1101293 1101294 1101295 1101296 1101297 1101298 1101299 1101300 1101301 1101302 1101303 1101304 1101305 1101306 1101307 1101308 1101309 1101310 1101311 1101312 1101313 1101314 1101315 1101316 1101317 1101318 1101319 1101320 1101321 1101322 1101323 1101324 1101325 1101326 1101327 1101328 1101329 1101330 1101331 1101332 1101333 1101334 1101335 1101336 1101337 1101338 1101339 1101340 1101341 1101342 1101343 1101344 1101345 1101346 1101347 1101348 1101349 1101350 1101351 1101352 1101353 1101354 1101355 1101356 1101357 1101358 1101359 1101360 1101361 1101362 1101363 1101364 1101365 1101366 1101367 1101368 1101369 1101370 1101371 1101372 1101373 1101374 1101375 1101376 1101377 1101378 1101379 1101380 1101381 1101382 1101383 1101384";
        List<String> idList = Arrays.asList(ids.split(" "));

        List<GoodsHead> goodsHeadList = goodsHeadService.selectListingGoodsHeadByIds(idList.stream().map(Integer::valueOf).toArray(Integer[]::new));
        for (GoodsHead goods : goodsHeadList) {
            for (ListingAmazonAttributeLine attributeLine : lineList) {
                attributeLine.setId(null);
                attributeLine.setGoodsId(goods.getId());
                attributeLine.setPdmGoodsCode(goods.getPdmGoodsCode());
                attributeLine.setCreateBy("2107");
                attributeLine.setUpdateBy("2107");
                if (Objects.equals("part_number", attributeLine.getTableName())) {
                    continue;
//                    MappingGoods mappingGoods = mappingGoodsService.selectMappingGoodsByPlatformSku(goods.getPlatformGoodsCode(), goods.getShopCode());
//                    if (!Objects.isNull(mappingGoods)) {
//                        attributeLine.setTableValue(mappingGoods.getPlatformPn());
//                    } else {
//                        String platformCode = createPlatformCode.getPlatformCode(goods.getPdmGoodsCode(), goods.getSiteCode());
//                        attributeLine.setTableValue(platformCode);
//                    }
                }
                listingAmazonAttributeLineService.insertListingAmazonAttributeLine(attributeLine);

            }
        }
    }



    @Autowired
    private PDMHttpRequestBiz PDMHttpRequestBiz;

    @Autowired
    protected AmazonListingResultHandler amazonListingResultHandler;

    @Test
    public void test(){
        GoodsHead head = goodsHeadService.selectListingGoodsHeadById(1897425);
        amazonListingResultHandler.addPdmStatus(head);

    }

   @Test
    public void test2(){
       List<AmazonAllListingsReportDTO> us42 = amazonPlatformListingService.getAmazonAllListingsReportDTOSV2("US36",null);
       // to map
       Map<Long, String> collect = us42.stream().collect(Collectors.toMap(AmazonAllListingsReportDTO::getId, AmazonAllListingsReportDTO::getSellerCode));
       System.out.println(us42.size());
   }
   @Autowired
   AmazonVCListingPushTask amazonListingFeedTask;
   @Test
    public void test3(){
       amazonListingFeedTask.amazonVCListingPushTask();
    }
    @Autowired
    IGoodsDescriptionService goodsDescriptionService;
    private static final int BATCH_SIZE = 50000; // 每批处理的数据量
    private static final int THREAD_POOL_SIZE = 10; // 线程池大小
    @Test
    public void validateAndExportGoodsDescriptions() {
        List<GoodsHead> goodsHeadList = goodsHeadService.selectListByPlatformGoodsCodes(Collections.singletonList(PlatformTypeEnum.AM.name()));
        int totalSize = goodsHeadList.size();
        List<List<GoodsHead>> batches = new ArrayList<>();

        for (int i = 0; i < totalSize; i += BATCH_SIZE) {
            int end = Math.min(totalSize, i + BATCH_SIZE);
            batches.add(goodsHeadList.subList(i, end));
        }

        ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(THREAD_POOL_SIZE);

        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Validation Results");
            createHeaderRow(sheet);

            List<List<Object>> results = Collections.synchronizedList(new ArrayList<>());

            for (List<GoodsHead> batch : batches) {
                executor.submit(() -> {
                    List<Integer> goodsIdList = batch.stream().map(GoodsHead::getId).collect(Collectors.toList());
                    List<GoodsDescription> goodsDescriptionList = goodsDescriptionService.selectDescriptionListByGoodsIdList(goodsIdList);

                    for (GoodsDescription description : goodsDescriptionList) {
                        log.info("主键:{}, 正在校验违禁词", description.getGoodsId());
                        List<String> prohibitedWords = ValidationUtils.findProhibitedContent(
                                String.join(" ", Arrays.asList(
                                        description.getItemDescription1(),
                                        description.getItemDescription2(),
                                        description.getItemDescription3(),
                                        description.getItemDescription4(),
                                        description.getItemDescription5()
                                ))
                        );

                        if (!prohibitedWords.isEmpty()) {
                            GoodsHead relatedHead = batch.stream().filter(g -> g.getId().equals(description.getGoodsId())).findFirst().orElse(null);
                            if (relatedHead != null) {
                                List<Object> rowResult = Arrays.asList(
                                        relatedHead.getId(),
                                        relatedHead.getPlatformGoodsCode(),
                                        PublishStatus.getPublishStatusName(relatedHead.getPublishStatus()),
                                        relatedHead.getPlatformGoodsId(),
                                        String.join(", ", Arrays.asList(
                                                description.getItemDescription1(),
                                                description.getItemDescription2(),
                                                description.getItemDescription3(),
                                                description.getItemDescription4(),
                                                description.getItemDescription5()
                                        )),
                                        String.join(", ", prohibitedWords)
                                );
                                results.add(rowResult);
                            }
                        }
                    }
                });
            }

            executor.shutdown();
            executor.awaitTermination(Long.MAX_VALUE, TimeUnit.NANOSECONDS);
            log.info("所有线程处理完成, 共:{} 数据待输出excel", results.size());

            int rowNum = 1;
            for (List<Object> result : results) {
                Row row = sheet.createRow(rowNum++);
                for (int i = 0; i < result.size(); i++) {
                    log.info("第:{} 条数据正在写入excel", i);
                    Cell cell = row.createCell(i);
                    Object value = result.get(i);
                    if (value != null) {
                        cell.setCellValue(value.toString());
                    } else {
                        cell.setCellValue(""); // 设置为空字符串
                    }
                }
            }

            try (FileOutputStream outputStream = new FileOutputStream("C:/Users/<USER>/Downloads/违禁词匹配.xlsx")) {
                workbook.write(outputStream);
            }

        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    private void createHeaderRow(Sheet sheet) {
        Row headerRow = sheet.createRow(0);
        headerRow.createCell(0).setCellValue("主键ID");
        headerRow.createCell(1).setCellValue("平台sku");
        headerRow.createCell(2).setCellValue("平台销售编码");
        headerRow.createCell(3).setCellValue("平台销售编码");
        headerRow.createCell(4).setCellValue("五点内容");
        headerRow.createCell(5).setCellValue("违禁词");
    }


    @Autowired
    private AmazonListingPullLackTask amazonListingPullLackTask;
    @Autowired
    private EbayWebhookTask ebayWebhookTask;
    @Autowired
    private AmazonProductBiz amazonProductBiz;

    @Test
    public void testAmazonListingPullLackTask() {
        amazonListingPullLackTask.submitListingPullLackTask();
    }

    @Test
    public void testEbayListingPullLackTask() {
        ebayWebhookTask.ebayWebhookTask();
    }

    @Test
    public void testAmazonProductBiz() {
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(2382843);
//        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(2382844);
        Map<String, Object> amazonJsonFieldMap = amazonProductBiz.getAmazonJsonFieldMap(goodsHead.getId(), goodsHead.getCategoryId(), Constants.YesOrNo.YES, null, Constants.YesOrNo.NO);
        amazonProductBiz.followAsin(amazonJsonFieldMap, 2382843);
        System.out.println(amazonJsonFieldMap);
    }

    @Test
    public void testAmazonProductUpload() {
        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(1900295);
        baseAmazonProductTask.prodocutTask(Collections.singletonList(goodsHead), "上架", null);
    }

    @Test
    public void testSyncVCSalesData() {
        biDataSyncTask.biDataSyncTask();
    }

    @Autowired
    VcdfReportResultInventoryConsumer vcdfReportResultInventoryConsumer;

    @Test
    public void testVcdfReportResultInventoryConsumer() {
        vcdfReportResultInventoryConsumer.doProcess("成功");
    }


    @Test
    public void testRefreshVCCategory() {
        String userId = "2105";
        amazonProductBiz.refreshVCCategory(userId);
    }

    @Autowired
    AmazonSCListingPushTask amazonSCListingPushTask;

    @Test
    public void test44() {
        amazonSCListingPushTask.amazonSCListingPushTask();
    }

    @Autowired
    IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    /**
     * 查找SC链接回写SC品类配置
     */
    @Test
    public void findSCCategory() {
        String fileUrl = "C:Users\\yiki\\Downloads\\SC V2店铺映射.xlsx";

        try (FileInputStream fileInputStream = new FileInputStream(fileUrl);
             Workbook workbook = new XSSFWorkbook(fileInputStream)) {

            Sheet sheet = workbook.getSheet("Sheet1");
            int lastRowNum = sheet.getLastRowNum();

            for (int rowIndex = 0; rowIndex <= lastRowNum; rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                log.info("当前行:{},还剩:{}待处理", rowIndex, lastRowNum - rowIndex);
                if (row != null) {
                    Cell cellA = row.getCell(0, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    String createBy = String.valueOf(cellA.getNumericCellValue());
                    //去除.0
                    createBy = createBy.substring(0, createBy.indexOf("."));
                    Cell cellB = row.getCell(1, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    String shopCode = cellB.getStringCellValue().trim();
                    Cell cellC = row.getCell(2, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                    String categoryId = String.valueOf(cellC.getNumericCellValue());
                    categoryId = categoryId.substring(0, categoryId.indexOf("."));
                    List<GoodsHead> goodsHeadList = goodsHeadService.selectAMList(createBy, shopCode, categoryId);
                    try {
                        if (ObjectUtil.isEmpty(goodsHeadList)) {
                            throw new BusinessException("没有链接数据");
                        }
                        List<ListingAmazonAttributeLineV2> amazonAttributeLineV2s = listingAmazonAttributeLineV2Service.selecAttrByGoodsIds(goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList())
                                .stream().map(Integer::longValue).collect(Collectors.toList()));

                        Map<Long, List<ListingAmazonAttributeLineV2>> goodsIdMap = amazonAttributeLineV2s.stream()
                                .collect(Collectors.groupingBy(ListingAmazonAttributeLineV2::getHeadId));

                        int maxAttributeCount = goodsIdMap.values().stream()
                                .mapToInt(List::size)
                                .max()
                                .orElse(0);  // 如果没有数据，返回 0
                        if (maxAttributeCount == 0) {
                            throw new BusinessException("没有v2属性数据");
                        }

                        List<Long> goodsIdsWithMaxAttributes = goodsIdMap.entrySet().stream()
                                .filter(entry -> entry.getValue().size() == maxAttributeCount)
                                .map(Map.Entry::getKey)
                                .collect(Collectors.toList());
                        Optional<GoodsHead> latestGoodsHead = goodsHeadList.stream()
                                .filter(g -> goodsIdsWithMaxAttributes.contains(g.getId().longValue()))
                                .max(Comparator.comparing(GoodsHead::getOnlineTime));
                        if (latestGoodsHead.isPresent()) {
                            log.info("创建人:{},店铺:{},分类:{},链接数据:{}", createBy, shopCode, categoryId, latestGoodsHead.get().getPlatformGoodsId());
                            amazonProductBiz.fillCategoryInfoTemplateField(latestGoodsHead.get());
                        }
                    } catch (Exception e) {
                        Cell cellD = row.getCell(3, Row.MissingCellPolicy.CREATE_NULL_AS_BLANK);
                        cellD.setCellValue(e.getMessage());
                    }
                }
            }
            // 写回到同一个文件
            try (FileOutputStream fileOut = new FileOutputStream(fileUrl)) {
                workbook.write(fileOut);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Autowired
    private WebhookBiz webhookBiz;

    @Autowired
    private AmazonVCDFHighCostInventoryTask amazonVCDFHighCostInventoryTask;

    @Test
    public void testSync() {
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);
//        webhookBiz.syncAndUpdateAmazonListingHandler("VC1", "B0DM9D4C9M", "4917LAH561UUS", null);

    }

    @Test
    public void testSync2() {
        amazonVCDFHighCostInventoryTask.amazonVCDFHighCostInventoryTask();
    }

    @Autowired
    ThirdpartyInventoryBiz thirdpartyInventoryBiz;
    @Autowired
    IConfigStoreInfoService configStoreInfoService;
    @Autowired
    IListingLogService listingLogService;

    @Test
    public void testSync3() {
        ConfigStoreInfo configStoreInfo = configStoreInfoService.selectConfigStoreInfoByShopCode("VC1");

        List<GoodsHead> goodsHeads = goodsHeadService.selectVCDFListing(Collections.singletonList("7089LHB437PUS"));
        thirdpartyInventoryBiz.removeExcludeListing(goodsHeads, configStoreInfo);
        log.info("{排除后数量}", goodsHeads.size());

    }

    /**
     * 价格更新测试方法
     *
     * 功能说明：
     * 1. 读取指定的Excel文件，Excel应包含以下列：
     *    - 第1列：主键 (对应GoodsHead的id字段)
     *    - 第2列：平台 (AM等)
     *    - 第3列：商品编码 (PDM商品编码)
     *    - 第4列：平台商品编码 (平台SKU)
     *    - 第5列：平台销售编码 (ASIN等)
     *    - 第6列：刊登类型 (FBA/FBM)
     *    - 第7列：动作 (IT改价格等)
     *    - 第8列：目标价 (要更新的standardPrice值)
     *
     * 2. 根据主键ID修改对应的目标价到standardPrice
     * 3. 构建调用接口的API对象：AmazonJSONParamDTO，op为"replace"，path为"/attributes/purchasable_offer"
     *
     * Excel实际格式：
     * | 主键 | 平台 | 商品编码 | 平台商品编码 | 平台销售编码 | 刊登类型 | 动作 | 目标价 |
     * |------|------|----------|--------------|--------------|----------|------|--------|
     * | 4178 | AM   | KX2CFK14200 | 2496CFK1424US | B0BX9CW9C3 | FBM | IT改价格 | 61.79 |
     * | 10700| AM   | ZC4PSP09201 | 7771PSP092MUS | B0BPM5L3WH | FBA | IT改价格 | 62.19 |
     */
    @Test
    public void testUpdatePrice() {
        // Excel文件路径 - 请根据实际情况修改
        String excelFilePath = "C:\\Users\\<USER>\\Desktop\\亚马逊一部低于红线价链接操作需求20250606.xlsx";

        try {
            // 1. 读取Excel文件
            List<PriceUpdateDTO> priceUpdateList = readPriceUpdateExcel(excelFilePath);
            log.info("读取到{}条价格更新数据", priceUpdateList.size());

            // 2. 批量更新价格并调用接口
            int successCount = 0;
            int failCount = 0;

            for (PriceUpdateDTO priceUpdate : priceUpdateList) {
                try {
                    log.info("开始处理商品: {}", priceUpdate.toString());

                    // 2.1 根据主键ID查询GoodsHead
                    GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(priceUpdate.getId());
                    if (goodsHead == null) {
                        log.warn("未找到ID为{}的商品", priceUpdate.getId());
                        failCount++;
                        continue;
                    }
                    PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(goodsHead.getCategoryId().longValue());

                    // 验证商品信息是否匹配
                    if (!StringUtils.isEmpty(priceUpdate.getPlatformGoodsId()) &&
                        !priceUpdate.getPlatformGoodsId().equals(goodsHead.getPlatformGoodsId())) {
                        log.warn("商品ID:{}的ASIN不匹配，Excel中:{}, 数据库中:{}",
                                priceUpdate.getId(), priceUpdate.getPlatformGoodsId(), goodsHead.getPlatformGoodsId());
                    }

                    String originalPrice = goodsHead.getStandardPrice();

                    // 2.2 更新standardPrice
                    goodsHead.setStandardPrice(priceUpdate.getTargetPrice());
                    int updateResult = goodsHeadService.updateListingGoodsHead(goodsHead);

                    if (updateResult > 0) {
                        log.info("✅ 更新商品成功 - ID:{}, ASIN:{}, 原价格:{}, 新价格:{}",
                                goodsHead.getId(), goodsHead.getPlatformGoodsId(), originalPrice, priceUpdate.getTargetPrice());


                        // 2.3 构建API调用对象 - 参考AmazonPriceV2ListingUpdateModuleResolver
                        List<AmazonListingJSONFeedVO.Attributes> attributesList = buildPriceUpdateAttributes(goodsHead);

                        // 2.4 模拟调用价格更新接口
                        if (!attributesList.isEmpty()) {
                            log.info("🔧 构建价格更新API参数成功 - 商品ID:{}, 目标价格:{}, 属性数量:{}",
                                    goodsHead.getId(), priceUpdate.getTargetPrice(), attributesList.size());

                            // 打印构建的API参数用于调试
                            log.info("📋 API参数 - op:{}", attributesList);

                            // 这里可以调用实际的API接口
                            AmazonListingJSONFeedVO feed = AmazonListingJSONFeedVO.builder()
                                    .sku(goodsHead.getPlatformGoodsCode())
                                    .productType(platformCategory.getProductType())
                                    .sellerCode(goodsHead.getShopCode())
                                    .attributeList(attributesList)
                                    .build();

                            AjaxResult ajaxResult = amazonApiHttpRequestBiz.updateSkuV2(feed,goodsHead.getShopCode(),goodsHead.getPublishType(),goodsHead.getPlatformGoodsCode());
                            if(!ajaxResult.isSuccess()){
                                log.warn("❌ 更新商品ID:{}的价格失败", priceUpdate.getId());

                                // 记录失败日志
                                String errorDetails = String.format("产品调价失败，商品ID:%s,目标价格:%s",
                                        priceUpdate.getId(), priceUpdate.getTargetPrice());
                                try {
                                    listingLogService.insertErrorListingLog(errorDetails, "-1",
                                            priceUpdate.getId(), ajaxResult.get(AjaxResult.MSG_TAG).toString());
                                } catch (Exception logEx) {
                                    log.error("记录失败日志异常", logEx);
                                }
                            }else {
                                successCount++;
                                log.info("✅ 更新商品ID:{}的价格成功", priceUpdate.getId());
                                // 构建详细的日志信息
                                String logDetails = String.format("产品调价成功，ASIN:%s,商品编码:%s,平台商品编码:%s,Your Price从%s-->%s",
                                        goodsHead.getPlatformGoodsId(),
                                        goodsHead.getPdmGoodsCode(),
                                        goodsHead.getPlatformGoodsCode(),
                                        originalPrice,
                                        priceUpdate.getTargetPrice());
                                try {
                                    listingLogService.insertSuccessListingLog(logDetails, "-1",
                                            priceUpdate.getId());
                                } catch (Exception logEx) {
                                    log.error("记录成功日志异常", logEx);
                                }
                            }
                        }
                    } else {
                        failCount++;
                    }

                } catch (Exception e) {
                    log.error("❌ 处理商品ID:{}价格更新失败", priceUpdate.getId(), e);
                    failCount++;
                }
            }

            log.info("🎉 价格更新完成 - 成功:{}, 失败:{}, 总计:{}", successCount, failCount, priceUpdateList.size());

        } catch (Exception e) {
            log.error("价格更新测试失败", e);
        }
    }

    /**
     * 读取Excel文件中的价格更新数据
     */
    private List<PriceUpdateDTO> readPriceUpdateExcel(String filePath) {
        List<PriceUpdateDTO> resultList = new ArrayList<>();

        try {
            log.info("开始读取Excel文件: {}", filePath);

            // 使用EasyExcel读取文件
            EasyExcel.read(filePath, PriceUpdateDTO.class, new AnalysisEventListener<PriceUpdateDTO>() {
                @Override
                public void invoke(PriceUpdateDTO data, AnalysisContext context) {
                    // 验证必要字段
                    if (data.getId() != null && StringUtils.isNotEmpty(data.getTargetPrice()) && !"删链接".equalsIgnoreCase(data.getTargetPrice())){
                        // 清理目标价格中的空格
                        String cleanPrice = data.getTargetPrice().trim();
                        data.setTargetPrice(cleanPrice);

                        resultList.add(data);
                        log.debug("读取数据: ID={}, 平台={}, ASIN={}, 目标价={}",
                                data.getId(), data.getPlatform(), data.getPlatformGoodsId(), cleanPrice);
                    } else {
                        log.warn("跳过无效数据行: ID={}, 目标价={}", data.getId(), data.getTargetPrice());
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel读取完成，共{}条有效数据", resultList.size());
                }
            }).sheet().doRead();

        } catch (Exception e) {
            log.error("读取Excel文件失败: {}", filePath, e);
        }

        return resultList;
    }

    /**
     * 构建价格更新属性列表 - 参考AmazonPriceV2ListingUpdateModuleResolver
     * 构建正确的our_price结构
     */
    private List<AmazonListingJSONFeedVO.Attributes> buildPriceUpdateAttributes(GoodsHead goodsHead) {
        List<AmazonListingJSONFeedVO.Attributes> attributesList = new ArrayList<>();

        try {
            String shopCode = goodsHead.getShopCode();
            String targetPrice = goodsHead.getStandardPrice();

            // 构建purchasable_offer属性
            AmazonListingJSONFeedVO.Attributes priceAttributes = new AmazonListingJSONFeedVO.Attributes();
            priceAttributes.setOp("REPLACE");
            priceAttributes.setPath("/attributes/purchasable_offer");

            // 构建复杂的价格结构
            Map<String, Object> priceStructure = buildPriceStructure(targetPrice);
            priceAttributes.setValue(Lists.newArrayList(priceStructure));

            attributesList.add(priceAttributes);

            log.info("构建价格更新属性成功 - 商品ID:{}, 店铺:{}, 价格:{}",
                    goodsHead.getId(), shopCode, targetPrice);

        } catch (Exception e) {
            log.error("构建价格更新属性失败 - 商品ID:{}", goodsHead.getId(), e);
        }

        return attributesList;
    }

    /**
     * 构建价格结构
     * 构建格式：
     * {
     *   "our_price": [
     *     {
     *       "schedule": [
     *         {
     *           "value_with_tax": 57.19
     *         }
     *       ]
     *     }
     *   ]
     * }
     */
    private Map<String, Object> buildPriceStructure(String targetPrice) {
        Map<String, Object> priceStructure = new HashMap<>();

        // 转换价格为数值
        double priceValue = Double.parseDouble(targetPrice);

        // 构建schedule结构
        Map<String, Object> scheduleItem = new HashMap<>();
        scheduleItem.put("value_with_tax", priceValue);

        // 构建our_price数组中的对象
        Map<String, Object> ourPriceItem = new HashMap<>();
        ourPriceItem.put("schedule", Lists.newArrayList(scheduleItem));

        // 构建最外层结构
        priceStructure.put("our_price", Lists.newArrayList(ourPriceItem));

        priceStructure.put("marketplace_id", "marketplace_id");
        priceStructure.put("currency", "USD");

        log.debug("构建价格结构成功: {}", priceStructure);


        return priceStructure;
    }

    /**
     * 创建示例Excel文件用于测试
     * 可以调用此方法生成一个示例Excel文件，包含实际数据格式
     */
    @Test
    public void createSamplePriceUpdateExcel() {
        String filePath = "C:/Users/<USER>/Downloads/price_update_sample.xlsx";

        List<PriceUpdateDTO> sampleData = new ArrayList<>();

        // 添加基于实际数据的示例
        PriceUpdateDTO sample1 = new PriceUpdateDTO();
        sample1.setId(4178);
        sample1.setPlatform("AM");
        sample1.setGoodsCode("KX2CFK14200");
        sample1.setPlatformGoodsCode("2496CFK1424US");
        sample1.setPlatformGoodsId("B0BX9CW9C3");
        sample1.setListingType("FBM");
        sample1.setAction("IT改价格");
        sample1.setTargetPrice("61.79");
        sampleData.add(sample1);

        PriceUpdateDTO sample2 = new PriceUpdateDTO();
        sample2.setId(10700);
        sample2.setPlatform("AM");
        sample2.setGoodsCode("ZC4PSP09201");
        sample2.setPlatformGoodsCode("7771PSP092MUS");
        sample2.setPlatformGoodsId("B0BPM5L3WH");
        sample2.setListingType("FBA");
        sample2.setAction("IT改价格");
        sample2.setTargetPrice("62.19");
        sampleData.add(sample2);

        PriceUpdateDTO sample3 = new PriceUpdateDTO();
        sample3.setId(104822);
        sample3.setPlatform("AM");
        sample3.setGoodsCode("KX0RSR06600U1");
        sample3.setPlatformGoodsCode("KX0RSR06600U1");
        sample3.setPlatformGoodsId("B09635DVYY");
        sample3.setListingType("FBA");
        sample3.setAction("IT改价格");
        sample3.setTargetPrice("33.43");
        sampleData.add(sample3);

        try {
            EasyExcel.write(filePath, PriceUpdateDTO.class)
                    .sheet("价格更新")
                    .doWrite(sampleData);
            log.info("示例Excel文件创建成功: {}", filePath);
            log.info("文件包含{}条示例数据", sampleData.size());
        } catch (Exception e) {
            log.error("创建示例Excel文件失败", e);
        }
    }

    /**
     * 使用提供的实际数据进行价格更新测试
     * 直接使用硬编码的数据，无需Excel文件
     */
    @Test
    public void testUpdatePriceWithActualData() {
        log.info("开始使用实际数据进行价格更新测试");

        // 创建实际数据列表
        List<PriceUpdateDTO> actualDataList = createActualDataList();

        int successCount = 0;
        int failCount = 0;

        for (PriceUpdateDTO priceUpdate : actualDataList) {
            try {
                log.info("开始处理商品: {}", priceUpdate.toString());

                // 根据主键ID查询GoodsHead
                GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(priceUpdate.getId());
                if (goodsHead == null) {
                    log.warn("未找到ID为{}的商品", priceUpdate.getId());
                    failCount++;
                    continue;
                }

                // 验证商品信息
                log.info("找到商品 - ID:{}, PDM编码:{}, 平台编码:{}, ASIN:{}, 当前价格:{}",
                        goodsHead.getId(), goodsHead.getPdmGoodsCode(), goodsHead.getPlatformGoodsCode(),
                        goodsHead.getPlatformGoodsId(), goodsHead.getStandardPrice());

                String originalPrice = goodsHead.getStandardPrice();

                // 更新standardPrice
                goodsHead.setStandardPrice(priceUpdate.getTargetPrice());
                int updateResult = goodsHeadService.updateListingGoodsHead(goodsHead);

                if (updateResult > 0) {
                    log.info("✅ 更新商品成功 - ID:{}, ASIN:{}, 原价格:{}, 新价格:{}",
                            goodsHead.getId(), goodsHead.getPlatformGoodsId(), originalPrice, priceUpdate.getTargetPrice());

                    // 构建详细的日志信息
                    String logDetails = String.format("产品调价，ASIN:%s,商品编码:%s,平台商品编码:%s,Your Price从%s-->%s",
                            goodsHead.getPlatformGoodsId(),
                            goodsHead.getPdmGoodsCode(),
                            goodsHead.getPlatformGoodsCode(),
                            originalPrice,
                            priceUpdate.getTargetPrice());

                    // 使用listingLogService记录成功日志
                    try {
                        listingLogService.insertSuccessListingLog(logDetails, "价格更新测试", goodsHead.getId());
                        log.info("📝 已记录到ListingLog: {}", logDetails);
                    } catch (Exception e) {
                        log.error("记录ListingLog失败", e);
                    }

                    // 构建API调用对象
                    List<AmazonListingJSONFeedVO.Attributes> attributesList = buildPriceUpdateAttributes(goodsHead);

                    if (!attributesList.isEmpty()) {
                        log.info("🔧 构建价格更新API参数成功");
                        for (AmazonListingJSONFeedVO.Attributes attr : attributesList) {
                            log.info("📋 API参数 - op:{}, path:{}", attr.getOp(), attr.getPath());
                            log.info("📋 API参数值 JSON: {}", JSON.toJSONString(attr.getValue(), true));
                        }
                    }
                    successCount++;
                } else {
                    log.warn("❌ 更新商品ID:{}的价格失败", priceUpdate.getId());

                    // 记录失败日志
                    String errorDetails = String.format("价格更新失败，商品ID:%s,目标价格:%s",
                            priceUpdate.getId(), priceUpdate.getTargetPrice());
                    try {
                        listingLogService.insertErrorListingLog(errorDetails, "价格更新测试",
                                priceUpdate.getId(), "数据库更新失败");
                    } catch (Exception logEx) {
                        log.error("记录失败日志异常", logEx);
                    }
                    failCount++;
                }

            } catch (Exception e) {
                log.error("❌ 处理商品ID:{}价格更新失败", priceUpdate.getId(), e);

                // 记录异常日志
                String errorDetails = String.format("价格更新异常，商品ID:%s,目标价格:%s",
                        priceUpdate.getId(), priceUpdate.getTargetPrice());
                try {
                    listingLogService.insertErrorListingLog(errorDetails, "价格更新测试",
                            priceUpdate.getId(), e.getMessage());
                } catch (Exception logEx) {
                    log.error("记录异常日志失败", logEx);
                }
                failCount++;
            }
        }

        log.info("🎉 实际数据价格更新完成 - 成功:{}, 失败:{}, 总计:{}", successCount, failCount, actualDataList.size());
    }

    /**
     * 创建实际数据列表
     */
    private List<PriceUpdateDTO> createActualDataList() {
        List<PriceUpdateDTO> dataList = new ArrayList<>();

        // 基于提供的实际数据创建对象
        dataList.add(createPriceUpdateDTO(4178, "AM", "KX2CFK14200", "2496CFK1424US", "B0BX9CW9C3", "FBM", "IT改价格", "61.79"));
        dataList.add(createPriceUpdateDTO(10700, "AM", "ZC4PSP09201", "7771PSP092MUS", "B0BPM5L3WH", "FBA", "IT改价格", "62.19"));
        dataList.add(createPriceUpdateDTO(104822, "AM", "KX0RSR06600U1", "KX0RSR06600U1", "B09635DVYY", "FBA", "IT改价格", "33.43"));
        dataList.add(createPriceUpdateDTO(342538, "AM", "KX3PWR27401", "8998PWR274AUS", "B0BQR7D33B", "FBA", "IT改价格", "42.51"));
        dataList.add(createPriceUpdateDTO(477488, "AM", "KX2CFK27800", "9844CFK2788US", "B0C4TTPZK1", "FBM", "IT改价格", "86.29"));
        dataList.add(createPriceUpdateDTO(522323, "AM", "ZC4PSP19600", "9272PSP1967US", "B0C61P2Y3P", "FBA", "IT改价格", "52.15"));
        dataList.add(createPriceUpdateDTO(709814, "AM", "KX4WBK28400", "3254WBK284QUS", "B0CCRSPWQ1", "FBA", "IT改价格", "49.86"));
        dataList.add(createPriceUpdateDTO(825876, "AM", "OO2ESP00200", "8594ESP0029US", "B0CF9MBWS3", "FBA", "IT改价格", "92.55"));
        dataList.add(createPriceUpdateDTO(828778, "AM", "KC4SGE01400", "9771SGE014HUS", "B0CFFHSG45", "FBM", "IT改价格", "251.9895"));
        dataList.add(createPriceUpdateDTO(829041, "AM", "KX2CFK24600", "1021CFK2469US", "B0CFFPDJK6", "FBM", "IT改价格", "74.38"));
        dataList.add(createPriceUpdateDTO(842889, "AM", "ZC4PSP09000", "9459PSP090NUS", "B0CG9CMLQ6", "FBA", "IT改价格", "59.39"));
        dataList.add(createPriceUpdateDTO(884359, "AM", "KC2EXP18200", "3567EXP182BUS", "B0CGR878HT", "FBM", "IT改价格", "62.74"));

        log.info("创建了{}条实际数据", dataList.size());
        return dataList;
    }

    /**
     * 创建PriceUpdateDTO对象的辅助方法
     */
    private PriceUpdateDTO createPriceUpdateDTO(Integer id, String platform, String goodsCode,
                                               String platformGoodsCode, String platformGoodsId,
                                               String listingType, String action, String targetPrice) {
        PriceUpdateDTO dto = new PriceUpdateDTO();
        dto.setId(id);
        dto.setPlatform(platform);
        dto.setGoodsCode(goodsCode);
        dto.setPlatformGoodsCode(platformGoodsCode);
        dto.setPlatformGoodsId(platformGoodsId);
        dto.setListingType(listingType);
        dto.setAction(action);
        dto.setTargetPrice(targetPrice.trim()); // 清理空格
        return dto;
    }

    /**
     * 测试价格结构构建
     * 验证构建的JSON结构是否正确
     */
    @Test
    public void testBuildPriceStructure() {
        log.info("开始测试价格结构构建");

        // 测试不同的价格值
        String[] testPrices = {"57.19", "61.79", "33.43", "251.9895"};

        for (String price : testPrices) {
            log.info("测试价格: {}", price);

            Map<String, Object> priceStructure = buildPriceStructure(price);
            String jsonString = JSON.toJSONString(priceStructure, true);

            log.info("构建的价格结构 JSON:");
            log.info("{}", jsonString);
            log.info("---");
        }

        // 测试完整的API参数构建
        log.info("测试完整API参数构建:");

        // 创建一个模拟的GoodsHead对象
        GoodsHead mockGoodsHead = new GoodsHead();
        mockGoodsHead.setId(12345);
        mockGoodsHead.setStandardPrice("57.19");
        mockGoodsHead.setShopCode("US12");

        List<AmazonListingJSONFeedVO.Attributes> attributes = buildPriceUpdateAttributes(mockGoodsHead);

        for (AmazonListingJSONFeedVO.Attributes attr : attributes) {
            log.info("完整API参数:");
            log.info("op: {}", attr.getOp());
            log.info("path: {}", attr.getPath());
            log.info("value JSON: {}", JSON.toJSONString(attr.getValue(), true));
        }
    }

    /**
     * 价格更新DTO类 - 匹配实际Excel格式
     */
    public static class PriceUpdateDTO {
        @ExcelProperty(value = "主键", index = 0)
        private Integer id;

        @ExcelProperty(value = "平台", index = 1)
        private String platform;

        @ExcelProperty(value = "商品编码", index = 2)
        private String goodsCode;

        @ExcelProperty(value = "平台商品编码", index = 3)
        private String platformGoodsCode;

        @ExcelProperty(value = "平台销售编码", index = 4)
        private String platformGoodsId;

        @ExcelProperty(value = "刊登类型", index = 5)
        private String listingType;

        @ExcelProperty(value = "动作", index = 6)
        private String action;

        @ExcelProperty(value = "目标价", index = 7)
        private String targetPrice;

        // Getters and Setters
        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getPlatform() {
            return platform;
        }

        public void setPlatform(String platform) {
            this.platform = platform;
        }

        public String getGoodsCode() {
            return goodsCode;
        }

        public void setGoodsCode(String goodsCode) {
            this.goodsCode = goodsCode;
        }

        public String getPlatformGoodsCode() {
            return platformGoodsCode;
        }

        public void setPlatformGoodsCode(String platformGoodsCode) {
            this.platformGoodsCode = platformGoodsCode;
        }

        public String getPlatformGoodsId() {
            return platformGoodsId;
        }

        public void setPlatformGoodsId(String platformGoodsId) {
            this.platformGoodsId = platformGoodsId;
        }

        public String getListingType() {
            return listingType;
        }

        public void setListingType(String listingType) {
            this.listingType = listingType;
        }

        public String getAction() {
            return action;
        }

        public void setAction(String action) {
            this.action = action;
        }

        public String getTargetPrice() {
            return targetPrice;
        }

        public void setTargetPrice(String targetPrice) {
            this.targetPrice = targetPrice;
        }

        @Override
        public String toString() {
            return "PriceUpdateDTO{" +
                    "id=" + id +
                    ", platform='" + platform + '\'' +
                    ", goodsCode='" + goodsCode + '\'' +
                    ", platformGoodsCode='" + platformGoodsCode + '\'' +
                    ", platformGoodsId='" + platformGoodsId + '\'' +
                    ", listingType='" + listingType + '\'' +
                    ", action='" + action + '\'' +
                    ", targetPrice='" + targetPrice + '\'' +
                    '}';
        }
    }

    /**
     * 同步前端链接
     */
    @Test
    public void syncAMForntListing() {

        amazonApiHttpRequestBiz.getFrontDetailResult("VC1", 5, "B0DWSBTZ4X", null);

    }
}