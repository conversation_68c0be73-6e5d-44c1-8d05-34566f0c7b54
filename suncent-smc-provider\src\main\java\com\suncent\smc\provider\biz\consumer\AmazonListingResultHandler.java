package com.suncent.smc.provider.biz.consumer;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.enums.AdaptationStatusEnum;
import com.suncent.smc.common.enums.PublishStatus;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.ads.domain.ItDemand;
import com.suncent.smc.persistence.ads.service.impl.AdsServiceImpl;
import com.suncent.smc.persistence.cdp.domain.entity.Brand;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.IBrandService;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.configuration.platformCategory.domain.entity.PlatformCategory;
import com.suncent.smc.persistence.configuration.platformCategory.service.IPlatformCategoryService;
import com.suncent.smc.persistence.pdm.domain.dto.CreateMappingDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.product.service.ProductDocumentRecordService;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.publication.service.IListingAmazonAttributeLineService;
import com.suncent.smc.persistence.publication.service.IListingAmazonAttributeLineV2Service;
import com.suncent.smc.provider.biz.mq.MQpushBiz;
import com.suncent.smc.provider.biz.publication.PDMHttpRequestBiz;
import com.suncent.smc.provider.biz.publication.service.AbstractBaseListingService;
import com.suncent.smc.provider.biz.todo.SMCTodoBiz;
import com.suncent.smc.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.suncent.smc.common.utils.DateUtils.dateTimeNow;

/**
 * TODO
 *
 * <AUTHOR>
 * @since 2023-08-08 11:44:00
 */
@Component
@Slf4j
public class AmazonListingResultHandler {
    @Autowired
    ProductDocumentRecordService productDocumentRecordService;
    @Autowired
    IGoodsHeadService goodsHeadService;
    @Value("${pdm.fix_pdm_goods_status}")
    private String FIX_PDM_GOODS_STATUS;
    @Value("${pdm.add_pdm_goods_status}")
    private String ADD_PDM_GOODS_STATUS;
    @Autowired
    private AdsServiceImpl adsService;
    @Autowired
    private ISysUserService userService;
    @Autowired
    private IListingAmazonAttributeLineService listingAmazonAttributeLineService;
    @Autowired
    private IGoodsService goodsService;
    @Autowired
    private PDMHttpRequestBiz PDMHttpRequestBiz;
    @Autowired
    MQpushBiz mqpushBiz;
    @Resource
    @Lazy
    protected AbstractBaseListingService abstractBaseListingService;
    @Autowired
    SMCTodoBiz smcTodoBiz;
    @Autowired
    protected IListingAmazonAttributeLineV2Service listingAmazonAttributeLineV2Service;
    @Autowired
    protected IBrandService brandService;
    @Autowired
    protected IShopService shopService;
    @Autowired
    private IPlatformCategoryService platformCategoryService;


    public boolean listingReportConsumerHandle(String text, String mqFlag) {
        if (!EnvUtils.isProdProfile()) {
            log.info("非生产环境,不处理listingReportConsumerHandle");
            return true;
        }
        List<GoodsHead> goodsHeadList = JSONObject.parseArray(text, GoodsHead.class);
        if (CollectionUtils.isEmpty(goodsHeadList)) {
            return true;
        }
        goodsHeadList.forEach(a -> {
            if (ObjectUtils.isEmpty(a.getPlatformGoodsId())) {
                return;
            }
            log.info("amazon-listings-report结果消费者-保存适配数据成功,商品主表id:{},name:{},asin:{}", a.getId(), a.getTitle(), a.getPlatformGoodsId());
            //增加pdm映射
            addPdmStatus(a);
            //推送mq
            if (ObjUtil.equals(mqFlag, "publish")) {
                GoodsHead update = new GoodsHead();
                update.setId(a.getId());
                if (ObjUtil.equals(a.getPublishStatus(), PublishStatus.PUBLISH_FAIL.getType())) {
                    update.setPublishStatus(PublishStatus.SALEING.getType());
                }
                update.setAdaptationStatus("待适配");
                update.setPlatformGoodsId(a.getPlatformGoodsId());
                goodsHeadService.updateListingGoodsHead(update);
                //上传适配
                saveAdapt(a);

                //ifs 推送消息去除
                mqpushBiz.publishPush2MQ(a.getPlatform(), a.getSiteCode(), a.getPdmGoodsCode(), a.getPlatformGoodsCode(), a.getPlatformGoodsId());
                //库存待办注释
                //smcTodoBiz.updateTodoStatusBySku(a.getPdmGoodsCode(), TodoStatusEnum.FINISH_STATUS);
                //处理ads自动刊登平台销售编码回写
                abstractBaseListingService.updateAdsPlatformSaleCode(a);
            }

        });
        return false;
    }


    public void addPdmStatus(GoodsHead a) {
        if (ObjUtil.equals(String.valueOf(a.getPdmStatus()), "1")) {
            return;
        }
        String pn = listingAmazonAttributeLineV2Service.getPn(a.getId());

//        String json = "[{\"platformPn\": \"" + pn + "\",\"asin\": \"" + a.getPlatformGoodsId() + "\",\"platformSku\": \"" + a.getPlatformGoodsCode() + "\",\"shopCode\": \"" + a.getShopCode() + "\"}]";
        Long operators = 738L;
        String deptId = "380";

        try {
            operators = Long.valueOf(a.getCreateBy());
        } catch (NumberFormatException e) {
        }
        SysUser sysUser = userService.selectUserById(operators);
        if (Objects.nonNull(sysUser)) {
            deptId = String.valueOf(sysUser.getDeptId());
        }

        String goodsCode = a.getPdmGoodsCode();
//        addPdmStatus(json, String.valueOf(operators), deptId, goodsCode,a);

        try {
            CreateMappingDTO createMappingDTO = new CreateMappingDTO();
            createMappingDTO.setGoodsCode(goodsCode);
            createMappingDTO.setShopCode(a.getShopCode());
            createMappingDTO.setPlatformSku(a.getPlatformGoodsCode());
            createMappingDTO.setAsin(a.getPlatformGoodsId());
            createMappingDTO.setPlatformPn(pn);
            createMappingDTO.setOperators(String.valueOf(operators));
            createMappingDTO.setDeptId(deptId);
            createMappingDTO.setPrice(a.getStandardPrice());
            createMappingDTO.setCreateBy(String.valueOf(operators));
            if (StrUtil.isNotEmpty(a.getFnSku())) {
                createMappingDTO.setFnSku(a.getFnSku());
            }
            createMappingDTO.setSystemType("SMC");
            createMappingDTO.setBrandCode(a.getBrandCode());
            createMappingDTO.setPublishType(a.getPublishType());
            PDMHttpRequestBiz.addPdmMapping(createMappingDTO, a);
        } catch (Exception e) {
            log.error("添加PDM状态映射异常,平台销售编码:{}",a.getPlatformGoodsId(), e);
        }

    }


    private void addPdmStatus(String json, String operators, String deptId, String goodsCode, GoodsHead goodsHead) {
        Map<String, Object> pdmMap = new HashMap<>();
        pdmMap.put("json", json);
        pdmMap.put("operators", operators);
        pdmMap.put("deptId", deptId);
        pdmMap.put("goodsCode", goodsCode);
        log.debug("准备添加PDM状态映射:{}", JSON.toJSONString(pdmMap));
        String result = HttpUtils.post(ADD_PDM_GOODS_STATUS, pdmMap);
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);
        if (ajaxResult.isSuccess()) {
            goodsHead.setPdmStatus(1);
            goodsHeadService.updateListingGoodsHead(goodsHead);
        } else {
            //为10000为编码重复 也为已映射
            if ("10000".equals(code.toString())) {
                goodsHead.setPdmStatus(1);
                goodsHeadService.updateListingGoodsHead(goodsHead);
            }
        }
        log.debug("完成添加PDM状态映射:{}", JSON.toJSONString(result));
    }

    /**
     * 保存适配数据
     *
     * @param goodHead
     */
    private void saveAdapt(GoodsHead goodHead) {
        if (StrUtil.isBlank(goodHead.getPdmGoodsCode())) {
            return;
        }
//        if(!goodHead.getShopCode().contains("VC")) {
//            pn = listingAmazonAttributeLineService.getPn(goodHead.getId());
//        }else {
        String pn = listingAmazonAttributeLineV2Service.getPn(goodHead.getId());
//        }

        if (ObjUtil.isEmpty(pn)){
            return;
        }

        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodsCode(goodHead.getPdmGoodsCode());
        Goods pdmGood = goodsService.selectGoodsByGoodCode(query);

        if (Objects.isNull(pdmGood)) {
            return;
        }
        //无需适配 适配状态改为无需适配 适配数据不保存
        String adaptFlag = pdmGood.getAdaptFlag();
        if (ObjUtil.equals(adaptFlag, "N")) {
            GoodsHead goodsHead = new GoodsHead();
            goodsHead.setId(goodHead.getId());
            goodsHead.setAdaptationStatus(AdaptationStatusEnum.NO.getStatus());
            goodsHeadService.updateListingGoodsHead(goodsHead);
            return;
        }

        // 是否跟卖
        String followAsin = listingAmazonAttributeLineV2Service.getFollowAsin(goodHead.getId());
        if (StrUtil.isNotBlank(followAsin)) {
            // 通过ASIN找到最近一条链接的适配状态
            GoodsHead lastGoodsHeadByAsin = goodsHeadService.selectLastGoodsHeadByAsin(followAsin, goodHead.getId());
            // 如果有最近一条链接的适配状态，直接更新当前商品的适配状态
            if (lastGoodsHeadByAsin != null) {
                GoodsHead head = new GoodsHead();
                head.setId(goodHead.getId());
                head.setAdaptationStatus(lastGoodsHeadByAsin.getAdaptationStatus() );
                goodsHeadService.updateListingGoodsHead(head);
                return;
            }
            // 找不到最近一条链接的适配状态，判断是否已经适配过
            else {
                int i = adsService.countByAsin(followAsin);
                if (i > 0) {
                    return;
                }
            }
        }

        GoodsHead goodsHead = goodsHeadService.selectListingGoodsHeadById(goodHead.getId());
        PlatformCategory platformCategory = platformCategoryService.selectPlatformCategoryById(goodsHead.getCategoryId().longValue());
        if(platformCategory.getCategoryType() != null && ObjUtil.equals(platformCategory.getCategoryType(), 2)) {
            log.info("主键ID:{} 平台类型为非汽配,不进行适配", goodsHead.getId());
            return;
        }

        //重复校验
        int i = adsService.countByPnAndAsin( goodHead.getPlatformGoodsId());
        if (i > 0) {
            return;
        }
        Shop shop = shopService.selectShopByShopCode(goodHead.getShopCode());
        Brand brand = brandService.selectBrandByName(goodHead.getBrandCode());

        String code = pdmGood.getClassificationCode();
        ItDemand itDemand = ItDemand.builder()
                .time(dateTimeNow("yyyyMMdd"))
                .brandEnName(goodHead.getBrandCode())
                .sku(goodHead.getPlatformGoodsCode())
                .productCode(pdmGood.getProductCode())
                .pn(pn)
                .asin(goodHead.getPlatformGoodsId())
                .type(code)
                .demandType("0")
                .country(goodHead.getSiteCode())
                .brandAaiaId(ObjUtil.isNotEmpty(brand) ? brand.getBrandAaiaId() : "")
                .shopCode(goodHead.getShopCode())
                .browserName(ObjUtil.isNotEmpty(shop) ? shop.getBrowserName() : "")
                .currentNumber(1)
                .build();

        //这个入口是新上的链接
        adsService.saveToAdsItDemandA(itDemand);
    }
}