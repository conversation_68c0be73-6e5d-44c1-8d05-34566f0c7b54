package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.http.HttpException;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ebay.soap.eBLBaseComponents.ItemCompatibilityListType;
import com.ebay.soap.eBLBaseComponents.ItemType;
import com.google.common.collect.Lists;
import com.suncent.smc.common.config.SpringTaskRetry;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.domain.LeakyBucket;
import com.suncent.smc.common.domain.UrlReplaceEntity;
import com.suncent.smc.common.enums.*;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.Utils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.api.domain.EbayCompatibilityProperties;
import com.suncent.smc.persistence.common.service.ILogRecordService;
import com.suncent.smc.persistence.configuration.category.service.IGoodsCategoryMappingService;
import com.suncent.smc.persistence.configuration.store.domain.ConfigStoreInfo;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.ebay.domain.EbayListingAdaptiveV2;
import com.suncent.smc.persistence.ebay.domain.EbayListingSpecsItemV2;
import com.suncent.smc.persistence.ebay.service.IEbayListingAdaptiveV2Service;
import com.suncent.smc.persistence.ebay.service.IEbayListingGoodsHeadV2Service;
import com.suncent.smc.persistence.ebay.service.IEbayListingLogV2Service;
import com.suncent.smc.persistence.ebay.service.IEbayListingSpecsItemV2Service;
import com.suncent.smc.persistence.inventory.domain.InventoryPendingListing;
import com.suncent.smc.persistence.inventory.domain.ListingInventoryOperate;
import com.suncent.smc.persistence.inventory.service.IInventoryPendingListingService;
import com.suncent.smc.persistence.inventory.service.IListingInventoryOperateService;
import com.suncent.smc.persistence.pdm.domain.dto.CreateMappingDTO;
import com.suncent.smc.persistence.pdm.domain.dto.GoodsRedPriceDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.dto.ThirdpartyFbmDTO;
import com.suncent.smc.persistence.pdm.domain.entity.Goods;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.pdm.service.IGoodsService;
import com.suncent.smc.persistence.publication.domain.dto.ItemBackUpDTO;
import com.suncent.smc.persistence.publication.domain.dto.ItemDTO;
import com.suncent.smc.persistence.publication.domain.entity.*;
import com.suncent.smc.persistence.publication.service.*;
import com.suncent.smc.provider.biz.configuration.BuildEbayItemBiz;
import com.suncent.smc.provider.biz.configuration.BuildEbayItemV2Biz;
import com.suncent.smc.provider.biz.error.PlatformErrorBiz;
import com.suncent.smc.provider.biz.inventory.ThirdpartyInventoryBiz;
import com.suncent.smc.provider.biz.liteflow.entity.EbayItemDTO;
import com.suncent.smc.provider.biz.mq.MQpushBiz;
import com.suncent.smc.provider.biz.publication.domain.EbayInventoryVO;
import com.suncent.smc.provider.biz.publication.util.MemoryCleanupUtil;
import com.suncent.smc.provider.biz.todo.SMCTodoBiz;
import com.suncent.smc.system.service.ISysUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.suncent.smc.common.constant.UserConstants.SYS_USER_ID;
import static com.suncent.smc.common.constant.UserConstants.YES;
import static org.apache.commons.lang3.StringUtils.isBlank;

@Component
@Slf4j
public class BaseEbayProductBiz {

    @Value("${api.ebay-addItem-url}")
    protected String ADD_ITEM_URL;

    @Value("${api.ebay-fixItem-url}")
    protected String FIX_ITEM_URL;

    @Value("${api.ebay-endItem-url}")
    protected String END_ITEM_URL;

    @Value("${api.ebay-verifyAddItem-url}")
    protected String VERIFY_ITEM_URL;

    @Value("${api.ebay-reviseInventoryStatus-url}")
    private String REVISE_INVENTORY_STATUS_URL;


    @Value("${pdm.fix_pdm_goods_status}")
    private String FIX_PDM_GOODS_STATUS;

    @Resource
    protected IGoodsHeadService goodsHeadService;
    @Resource
    protected ISysUserService userService;
    @Resource
    protected IListingLogService listingLogService;
    @Resource
    protected IGoodsHeadBackupService goodsHeadBackupService;
    @Resource
    protected IListingEbayLineService ebayGoodsLineService;
    @Resource
    protected IGoodsService goodsService;

    @Resource
    protected RedisService redisService;
    @Autowired
    private DingdingMonitorInfoBiz dingdingMonitorInfoBiz;
    @Autowired
    private ITaskConfigurationService taskConfigurationService;
    @Autowired
    protected IListingInventoryOperateService listingInventoryOperateService;
    @Autowired
    protected ThirdpartyInventoryBiz inventoryBiz;
    @Autowired
    IInventoryPendingListingService inventoryPendingListingService;
    @Autowired
    private IGoodsCategoryMappingService goodsCategoryMappingService;
    @Autowired
    private PlatformErrorBiz platformErrorBiz;
    @Resource
    protected IListingEbayAdaptiveService adaptiveService;
    @Autowired
    private PDMHttpRequestBiz PDMHttpRequestBiz;

    @Autowired
    MQpushBiz mqpushBiz;

    @Autowired
    protected SMCTodoBiz smcTodoBiz;
    @Autowired
    ILogRecordService logRecordService;
    @Autowired
    private SpringTaskRetry retryable;
    @Autowired
    private IEbayListingSpecsItemV2Service ebayListingSpecsItemV2Service;
    @Resource
    protected IEbayListingAdaptiveV2Service ebayListingAdaptiveV2Service;
    @Autowired
    IEbayListingGoodsHeadV2Service ebayListingGoodsHeadV2Service;
    @Autowired
    private IEbayListingLogV2Service ebayListingLogV2Service;
    @Autowired
    @Qualifier("buildEbayItemBiz")
    private BuildEbayItemBiz ebayItemBiz;
    @Autowired
    @Qualifier("buildEbayItemV2Biz")
    private BuildEbayItemV2Biz buildEbayItemV2Biz;
    @Autowired
    private IListingAdapterLogService listingAdapterLogService;



    /**
     * ebay刊登
     *
     * @param itemDTO
     * @param operationName
     */
    public void addEbay(ItemDTO itemDTO, String operationName) {
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        ItemType ebayItem = itemDTO.getEbayItem();
        if (ObjectUtils.isEmpty(ebayItem)){
            throw new BusinessException("ebay刊登商品失败,ebayItem为空");
        }
        ebayItem.setUUID(itemDTO.getEbayUUID());
        EbayItemDTO ebayItemDTO = new EbayItemDTO();
        ebayItemDTO.setItemType(ebayItem);
        String s = JSON.toJSONString(ebayItemDTO);

        try {
            //从redis获取当前商品是否刊登过
            String redisKey = RedisKeyEnum.EBAY_PUSH_Listing.getKey() + goodsHead.getId();
            if (redisService.exists(redisKey)) {
                log.info("ebay刊登商品---商品id: {} 已经刊登过,请勿重复刊登", goodsHead.getId());
                return;
            }
            log.info("ebay刊登商品---商品id: {}", goodsHead.getId());
            //发送请求进行 对接api服务进行刊登
            String post = addItem(goodsHead.getId(),goodsHead.getShopCode(), s,1);
            log.info("ebay刊登商品结束---商品id:{},result:{}", goodsHead.getId(),post);
            AjaxResult handleResult = JSON.parseObject(post, AjaxResult.class);
            if (!handleResult.isSuccess()) {
                String errorMsg = String.valueOf(handleResult.get(AjaxResult.MSG_TAG));
                // 使用platformErrorBiz处理错误
                platformErrorBiz.handlePlatformError(goodsHead, errorMsg);
                if (PublishStatus.PUBLISH_FAIL.getType().equals(goodsHead.getPublishStatus())
                        || PublishStatus.UPDATING_FAIL.getType().equals(goodsHead.getPublishStatus())
                        || PublishStatus.DELETE_SUCCESS.getType().equals(goodsHead.getPublishStatus())) {
                    throw new BusinessException(errorMsg);
                }

            }
            //写入redis 用于防止重复刊登
            redisService.setCacheObject(redisKey, String.valueOf(handleResult.get(AjaxResult.MSG_TAG)), 6L, TimeUnit.HOURS);
            //为重售则还需要插入平台销售编码更新日志以及定时任务表更新状态
            if (ObjUtil.equals(operationName, OperationTypeEnum.RELIST.getName())) {
                syncRelistLog(goodsHead, String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));
            } else {
                goodsCategoryMappingService.addNumByPdmGoodsCode(PlatformTypeEnum.EB.name(), goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode(), String.valueOf(goodsHead.getCategoryId()));
                listingLogService.insertSuccessListingLog("Listing成功刊登至平台，返回平台销售编码[" + handleResult.get(AjaxResult.MSG_TAG) + "]",
                        StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(),
                        goodsHead.getId());
            }
            //更新listing状态
            goodsHead.setSmcFlag(ObjectUtils.equals(goodsHead.getSmcFlag(), 2) ? goodsHead.getSmcFlag() : 0);
            goodsHead.setPublishStatus(PublishStatus.SALEING.getType());
            goodsHead.setOnlineTime(new Date());
            goodsHead.setPlatformGoodsId(String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));
            goodsHead.setPublishingHandler("处理完成");
            ItemCompatibilityListType compatibilityList = ebayItem.getItemCompatibilityList();
            getAdaptationStatus(goodsHead, compatibilityList, true);
            goodsHeadService.updateListingGoodsHead(goodsHead);
            //回写pdm
            addPdmStatus(goodsHead, false);
            //记适配日志
//            listingAdapterLogService.insertSuccessListingLog(goodsHead.getPlatform(),Long.valueOf(goodsHead.getId()), goodsHead.getPlatformGoodsId(), "添加至适配管控池", goodsHead.getCreateBy(), DateUtils.getNowDate());
            //推送mq
            mqpushBiz.publishPush2MQ(goodsHead.getPlatform(), goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode(), goodsHead.getPlatformGoodsCode(), goodsHead.getPlatformGoodsId());
        } finally {
            // 使用内存清理工具类清理内存
            MemoryCleanupUtil.cleanupAll(ebayItem, ebayItemDTO, s);
        }
    }

    private String addItem(Integer id,String accountCode, String param, Integer count) {
        String post = null;
        try {
            UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
            urlReplaceEntity.setUrl(ADD_ITEM_URL);
            urlReplaceEntity.setAccountCode(accountCode);
            post = HttpUtils.post(Utils.replaceUrl(urlReplaceEntity), param, 1000 * 60 * 10);
            if (count > 2) {
                return post;
            }
            if (StringUtils.isNotBlank(post) && PublishUpdateErrorEnum.containsErrorMsg("EB",post)) {
                log.error("ebay刊登商品失败,id:{},重试次数:{},错误信息:{}",id,count, post);
                return addItem(id,accountCode, param, count + 1);
            }
        } catch (Exception e) {
            // e 是Read timed out异常  不进行重试了  防止重复刊登
            if (e instanceof HttpException) {
                log.error("ebay刊登商品异常接口超时,id:{},重试次数:{},错误信息:{}",id,count, e.getMessage());
                return JSON.toJSONString(AjaxResult.error("请求接口超时，请稍后重试."));
            }
            log.error("ebay刊登商品异常,id:{}",id, e);
            return JSON.toJSONString(AjaxResult.error("请求ebay接口异常："+e.getMessage()));
        }
        return post;
    }



    /**
     * 重售的listing需要插入更新日志以及更新定时表状态
     *
     * @param goodsHead
     * @param newItemID
     */
    private void syncRelistLog(GoodsHead goodsHead, String newItemID) {
        listingLogService.insertSuccessListingLog("重上架主键[" + goodsHead.getId() + "] 商品任务上架成功，平台销售编码变更为[" + newItemID + "]",
                StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(),
                goodsHead.getId());

        GoodsHead updateHead = new GoodsHead();
        updateHead.setId(goodsHead.getId());
        updateHead.setPlatformGoodsId(newItemID);
        goodsHeadService.updateListingGoodsHead(updateHead);

        //修改任务表状态
        TaskConfiguration taskConfiguration = new TaskConfiguration();
        taskConfiguration.setHeadId(String.valueOf(goodsHead.getId()));
        taskConfiguration.setIsSuccess(String.valueOf(SMCCommonEnum.VICTORY.getValue()));
        taskConfigurationService.updateTaskConfigurationByHeadId(taskConfiguration);
    }

    /**
     * 重售的listing需要插入更新日志以及更新定时表状态
     *
     * @param goodsHead
     * @param newItemId
     */
    private void syncRelistLogV2(EbayGoodsHeadV2 goodsHead, String newItemId) {
        ebayListingLogV2Service.insertSuccessListingLog("重上架主键[" + goodsHead.getId() + "] 商品任务上架成功，平台销售编码变更为[" + newItemId + "]",
                StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(),
                goodsHead.getId());

        EbayGoodsHeadV2 updateHead = new EbayGoodsHeadV2();
        updateHead.setId(goodsHead.getId());
        updateHead.setPlatformGoodsId(newItemId);
        ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateHead);

        //修改任务表状态
        TaskConfiguration taskConfiguration = new TaskConfiguration();
        taskConfiguration.setHeadId(String.valueOf(goodsHead.getId()));
        taskConfiguration.setIsSuccess(String.valueOf(SMCCommonEnum.VICTORY.getValue()));
        taskConfigurationService.updateTaskConfigurationByHeadId(taskConfiguration);
    }


    /**
     * ebay更新
     *
     * @param itemDTO
     * @param adaptiveFlag ture:适配数据更新(全量适配) false:其他数据更新
     */
    public String updateEbay(ItemDTO itemDTO, boolean adaptiveFlag) {
        GoodsHead goodsHead = itemDTO.getGoodsHead();
        ItemType ebayItem = itemDTO.getEbayItem();
        if (ObjectUtils.isEmpty(ebayItem)){
            throw new BusinessException("ebay更新商品失败,ebayItem为空");
        }
        //更新适配数据 组装适配数据
        if (adaptiveFlag){
            ebayItem= buildAdapterData(goodsHead,ebayItem,itemDTO.getEbayCompatibilityProperties());

        }
        EbayItemDTO ebayItemDTO = new EbayItemDTO();
        ebayItemDTO.setItemType(ebayItem);
        String s = JSON.toJSONString(ebayItemDTO);
        try {
            //        13、发送请求进行 对接api服务进行刊登
            log.info("ebay修改商品---商品id: {}", goodsHead.getId());
            String post = updateItem(goodsHead.getId(),goodsHead.getShopCode(), s,1);
            log.info("ebay修改商品结束---商品id: {},result:{}", goodsHead.getId(),post);
            AjaxResult handleResult = JSON.parseObject(post, AjaxResult.class);
            //14、获取对应的反馈结果进行处理，成功失败修改对应的smc中的状态，以及pdm中的状态
            if (handleResult.isSuccess()) {
                //修改smc中的状态
                goodsHead.setPublishStatus(PublishStatus.SALEING.getType());
                goodsHead.setPublishingHandler("处理完成");
                if (adaptiveFlag){
                    //修改 适配状态
                    ItemCompatibilityListType compatibilityList = ebayItem.getItemCompatibilityList();
                    getAdaptationStatus(goodsHead, compatibilityList, true);
                }
                goodsHeadService.updateListingGoodsHead(goodsHead);
                //处理mapping 映射
                updateMapping(goodsHead);
                //处理待办数据->已处理
                smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(),TodoStatusEnum.FINISH_STATUS);

                listingLogService.insertSuccessListingLog("ebay商品更新成功到ebay平台", isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(),
                        goodsHead.getId());
                return "";
            }
            //该listing 如果下架直接扭转到下架去
            if (!handleResult.isSuccess()) {
                smcTodoBiz.updateTodoStatusByListingUpdate(goodsHead.getId(),TodoStatusEnum.WAIT_STATUS);
                //根据错误进行业务处理
                platformErrorBiz.handlePlatformError(goodsHead, String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));
                return String.valueOf(handleResult.get(AjaxResult.MSG_TAG));
            }
            return "";
        } finally {
            // 使用内存清理工具类清理内存
            MemoryCleanupUtil.cleanupAll(ebayItem, ebayItemDTO, s);
        }
    }

    /**
     * 构建ebay适配数据
     * @param goodsHead
     * @param ebayItem
     * @param ebayCompatibilityProperties
     * @return
     */
    private ItemType buildAdapterData(GoodsHead goodsHead, ItemType ebayItem, EbayCompatibilityProperties ebayCompatibilityProperties) {
        ListingEbayLine listingEbayLine = ebayGoodsLineService.selectListingEbayLineByHeadId(goodsHead.getId());
        if (ObjectUtils.isEmpty(listingEbayLine)) {
            return ebayItem;
        }

        try {
            List<ListingEbayAdaptive> ebayAdaptiveList = adaptiveService.selectListByEbayLineId(listingEbayLine.getId());
            //此处只指定适配更新,所以可以指定适配全量替换设置为true
            if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties)) {
                ebayItem.setItemCompatibilityList(BuildEbayItemBiz.getItemCompatibilityListType(true, ebayCompatibilityProperties, ebayAdaptiveList));
            } else {
                ebayItem.setItemCompatibilityList(BuildEbayItemBiz.getItemCompatibilityListType(true, BuildEbayItemBiz.getDefaultBySite(goodsHead.getSiteCode()), ebayAdaptiveList));
            }
            return ebayItem;
        } catch (Exception e) {
            log.error("构建ebay适配数据异常", e);
            // 确保在异常情况下也能返回原始对象
            return ebayItem;
        }
    }


    public String updateItem(Integer id,String shopCode, String s,Integer count) {
        String post = null;
        try {
            UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
            urlReplaceEntity.setUrl(FIX_ITEM_URL);
            urlReplaceEntity.setAccountCode(shopCode);
            post = HttpUtils.post(Utils.replaceUrl(urlReplaceEntity), s, 30000);
            if (count > 2) {
                return post;
            }
            if (StringUtils.isNotBlank(post) && PublishUpdateErrorEnum.containsErrorMsg("EB",post)) {
                log.error("ebay更新商品失败,id:{},重试次数:{},错误信息:{}",id,count, post);
                return updateItem(id,shopCode, s, count + 1);
            }
        } catch (Exception e) {
            // e 是Read timed out异常
            if (e instanceof HttpException) {
//                if (count > 2) {
                    log.error("ebay更新商品异常接口超时,id:{},重试次数:{},错误信息:{}",id,count, e.getMessage());
                    return JSON.toJSONString(AjaxResult.error("请求接口超时，请稍后重试."));
//                }
//                return updateItem(id,shopCode, s, count + 1);
            }
            log.error("ebay更新商品异常,id:{}",id, e);
            return JSON.toJSONString(AjaxResult.error("请求ebay接口异常："+e.getMessage()));
        }
        return post;
    }

    /**
     * 更新商品映射
     * 用户更新成自定义平台编码 回写pdm 映射
     *
     * @param goodsHead
     */
    public void updateMapping(GoodsHead goodsHead) {

        GoodsHeadBackup goodsHeadBackup = goodsHeadBackupService.selectGoodsHeadBackupByGoodsId(String.valueOf(goodsHead.getId()));
        if (ObjectUtils.isEmpty(goodsHeadBackup)) {
            return;
        }
        ItemBackUpDTO itemBackUpDTO = JSON.parseObject(goodsHeadBackup.getContext(), ItemBackUpDTO.class);
        GoodsHead backUpGoodsHead = itemBackUpDTO.getGoodsHead();
        if (ObjectUtils.isEmpty(goodsHead.getPlatformGoodsCode()) ||
                ObjectUtils.isEmpty(backUpGoodsHead.getPlatformGoodsCode()) ||
                !goodsHead.getPlatformGoodsCode().equals(backUpGoodsHead.getPlatformGoodsCode())) {
            log.info("用户自定义平台商品编码不回写到pdm,goodsCode:{},platformGoodsCode:{}", goodsHead.getPdmGoodsCode(), goodsHead.getPlatformGoodsCode());
            return;
        }
        List<MappingGoods> mappingGoods = PDMHttpRequestBiz.queryPdmMapping(backUpGoodsHead);
        if (ObjectUtils.isEmpty(mappingGoods)) {
            GoodsRedPriceDTO redLinePriceByGoodsCode = PDMHttpRequestBiz.getRedLinePriceByGoodsCode(goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode());
            //分销不进行映射
            if (ObjectUtils.isEmpty(redLinePriceByGoodsCode)) {
                return;
            }
        }
        addPdmStatus(goodsHead, false);
    }

    /**
     * 更新商品映射
     * 用户更新成自定义平台编码 回写pdm 映射
     *
     * @param goodsHead
     */
    public void updateMappingV2(EbayGoodsHeadV2 goodsHead) {

        GoodsHeadBackup goodsHeadBackup = goodsHeadBackupService.selectGoodsHeadBackupByGoodsId(String.valueOf(goodsHead.getId()));
        if (ObjectUtils.isEmpty(goodsHeadBackup)) {
            return;
        }
        ItemBackUpDTO itemBackUpDTO = JSON.parseObject(goodsHeadBackup.getContext(), ItemBackUpDTO.class);
        GoodsHead backUpGoodsHead = itemBackUpDTO.getGoodsHead();
        if (ObjectUtils.isEmpty(goodsHead.getPlatformGoodsCode()) ||
                ObjectUtils.isEmpty(backUpGoodsHead.getPlatformGoodsCode()) ||
                !goodsHead.getPlatformGoodsCode().equals(backUpGoodsHead.getPlatformGoodsCode())) {
            log.info("用户自定义平台商品编码不回写到pdm,goodsCode:{},platformGoodsCode:{}", goodsHead.getPdmGoodsCode(), goodsHead.getPlatformGoodsCode());
            return;
        }
        List<MappingGoods> mappingGoods = PDMHttpRequestBiz.queryPdmMapping(backUpGoodsHead);
        if (ObjectUtils.isEmpty(mappingGoods)) {
            GoodsRedPriceDTO redLinePriceByGoodsCode = PDMHttpRequestBiz.getRedLinePriceByGoodsCode(goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode());
            //分销不进行映射
            if (ObjectUtils.isEmpty(redLinePriceByGoodsCode)) {
                return;
            }
        }
        addPdmStatusV2(goodsHead, false);
    }


    /**
     * ebay下架
     *
     * @param goodsHead
     */
    public boolean offEbay(GoodsHead goodsHead, PublishStatus publishStatus) {
        Integer headId = goodsHead.getId();
        String platformGoodsId = goodsHead.getPlatformGoodsId();
        log.info("ebay下架任务执行开始->listing id:{}", headId);
        AjaxResult handleResult = retryable.retryableToApiMsg(() -> ebayEndItem(goodsHead.getShopCode(), platformGoodsId));
        if (!handleResult.isSuccess()) {
            log.error("ebay下架定时任务执行失败->商品编码:{},错误信息：{}", goodsHead.getPlatformGoodsCode(), handleResult.get(AjaxResult.MSG_TAG));
            //修改smc中的状态
            PublishStatus publishStatusResult = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? PublishStatus.RE_LISTING_OFF_SHELF_FAIL : PublishStatus.OFF_SHELF_FAIL;
            goodsHead.setPublishStatus(publishStatusResult.getType());
            goodsHeadService.updateListingGoodsHead(goodsHead);

            //日志写入
            String msg = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? "重上架主键[" + headId + "],平台销售编码[" + platformGoodsId + "]商品下架失败" : "平台销售编码[" + platformGoodsId + "]商品下架成功";
            listingLogService.insertErrorListingLog(msg, StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(), goodsHead.getId(), String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));

            //重上架任务状态也需置为失败
            if (ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING)) {
                TaskConfiguration taskConfiguration = new TaskConfiguration();
                taskConfiguration.setIsSuccess(String.valueOf(SMCCommonEnum.RE_FAIL.getValue()));
                taskConfiguration.setHeadId(String.valueOf(headId));
                taskConfigurationService.updateTaskConfigurationByHeadId(taskConfiguration);
            }
            return false;
        }
        //修改smc中的状态  下架成功 定时重上架就变回定时重上架,其余的变成在售
        PublishStatus publishStatusResult = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? PublishStatus.TIMED_RE_LISTING : PublishStatus.OFF_SALE;
        goodsHead.setPublishStatus(publishStatusResult.getType());
        goodsHead.setOffTime(new Date());
        goodsHead.setPublishingHandler("处理完成");
        goodsHeadService.updateListingGoodsHead(goodsHead);
        fixPdmStatus(Long.valueOf(goodsHead.getId()), goodsHead.getShopCode(), goodsHead.getPlatformGoodsCode());

        String msg = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? "重上架主键[" + headId + "],平台销售编码[" + platformGoodsId + "]商品下架成功,待重新上架" : "平台销售编码[" + platformGoodsId + "]商品下架成功";
        listingLogService.insertSuccessListingLog(msg, StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(), goodsHead.getId());
        return true;
    }

    private AjaxResult ebayEndItem(String shopCode, String platformGoodsId) {
        UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
        urlReplaceEntity.setUrl(END_ITEM_URL);
        urlReplaceEntity.setAccountCode(shopCode);
        urlReplaceEntity.setItemId(platformGoodsId);
        String post = HttpUtils.post(Utils.replaceUrl(urlReplaceEntity), "");

        AjaxResult handleResult = JSON.parseObject(post, AjaxResult.class);
        return handleResult;
    }

    /**
     * 送检->检测ebay数据是否支持刊登
     *
     * @param itemType
     * @param shopCode
     * @param censorshipInfo
     * @param goodsHead
     */
    public CensorshipInfo pushCheck(ItemType itemType, String shopCode, CensorshipInfo censorshipInfo, GoodsHead goodsHead, Integer count) throws IOException {
        if (ObjectUtils.isEmpty(itemType)){
            throw new BusinessException("ebay送检商品失败,ebayItem为空");
        }

        EbayItemDTO ebayItemDTO = new EbayItemDTO();
        ebayItemDTO.setItemType(itemType);
        String replaces = JSONObject.toJSONString(ebayItemDTO).replace("\"sKU\"", "\"sku\"")
                .replace("\"mPN\"", "\"mpn\"")
                .replace("\"uPC\"", "\"upc\"");
        try {
            //发送请求进行送检
            UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
            urlReplaceEntity.setUrl(VERIFY_ITEM_URL);
            urlReplaceEntity.setAccountCode(shopCode);
            String post = HttpUtils.post(Utils.replaceUrl(urlReplaceEntity), replaces);

            if (StringUtils.isNotBlank(post) && PublishUpdateErrorEnum.containsErrorMsg("EB", post)) {
                log.error("ebay送检商品失败,id:{},重试次数:{},错误信息:{}", goodsHead.getId(), count, post);
                // 清理内存后再重试
                MemoryCleanupUtil.cleanupAll(itemType, ebayItemDTO, replaces);
                return pushCheck(itemType, shopCode, censorshipInfo, goodsHead, count + 1);
            }
            Map dataMap = JSON.parseObject(post, Map.class);
            if ("500".equals(String.valueOf(dataMap.get("code")))) {
                censorshipInfo.setStatus(0);
                censorshipInfo.setReason(String.valueOf(dataMap.get("msg")));
                goodsHead.setCensorship(String.valueOf(SMCCommonEnum.DEFEAT.getValue()));
                return censorshipInfo;
            }
            //处理数据
            Map map = JSON.parseObject(String.valueOf(dataMap.get("data")), Map.class);
            handleFee(map, censorshipInfo, goodsHead);
            return censorshipInfo;
        } finally {
            // 清理内存
            MemoryCleanupUtil.cleanupAll(itemType, ebayItemDTO, replaces);
        }
    }

    /**
     * 处理送检费用
     *
     * @param map
     * @param censorshipInfo
     * @param goodsHead
     */
    private void handleFee(Map map, CensorshipInfo censorshipInfo, GoodsHead goodsHead) {
        BigDecimal fee = new BigDecimal(0);
        String currency = "";
        List<String> feeList = JSON.parseArray(String.valueOf(map.get("fee")), String.class);
        for (int i = 0; i < feeList.size(); i++) {
            //list中的map
            Map valueMap = JSON.parseObject(feeList.get(i), Map.class);
            //value中的fee的map
            Map feeMap = JSON.parseObject(String.valueOf(valueMap.get("fee")), Map.class);
            fee = fee.add(Convert.toBigDecimal(feeMap.get("value")));
            if (i == 0) {
                currency = String.valueOf(feeMap.get("currencyID"));
            }
        }
        censorshipInfo.setFee(fee + " " + currency);
        censorshipInfo.setFeeData(getFeeData(map));
        censorshipInfo.setStatus(1);
        //设置送检成功
        goodsHead.setCensorship(String.valueOf(SMCCommonEnum.VICTORY.getValue()));

    }

    /**
     * 处理送检费用
     *
     * @param map
     * @param censorshipInfo
     * @param goodsHead
     */
    private void handleFeeV2(Map map, CensorshipInfo censorshipInfo, EbayGoodsHeadV2 goodsHead) {
        BigDecimal fee = new BigDecimal(0);
        String currency = "";
        List<String> feeList = JSON.parseArray(String.valueOf(map.get("fee")), String.class);
        for (int i = 0; i < feeList.size(); i++) {
            //list中的map
            Map valueMap = JSON.parseObject(feeList.get(i), Map.class);
            //value中的fee的map
            Map feeMap = JSON.parseObject(String.valueOf(valueMap.get("fee")), Map.class);
            fee = fee.add(Convert.toBigDecimal(feeMap.get("value")));
            if (i == 0) {
                currency = String.valueOf(feeMap.get("currencyID"));
            }
        }
        censorshipInfo.setFee(fee + " " + currency);
        censorshipInfo.setFeeData(getFeeData(map));
        censorshipInfo.setStatus(1);
        //设置送检成功
        goodsHead.setCensorship(String.valueOf(SMCCommonEnum.VICTORY.getValue()));

    }

    /**
     * 获取送检费用明细数据
     * @param map
     * @return
     */
    private String getFeeData(Map map) {
        List<Map> feeMapList = JSON.parseArray(String.valueOf(map.get("fee")), Map.class);
        //过滤出map中size等于5的map集合
        List<Map> costFeeData = feeMapList.stream().filter(m -> m.size() == 5).collect(Collectors.toList());
        StringBuilder feeData = new StringBuilder();
        for (Map costFee : costFeeData) {
            Map feeMap = JSON.parseObject(String.valueOf(costFee.get("fee")), Map.class);
            feeData.append(  costFee.get("name") +":"+feeMap.get("value") + " " + feeMap.get("currencyID") + ";");
        }
        if (feeData.length() <= 0) {
            return null;
        }
        return feeData.toString();
    }

    private void fixPdmStatus(Long goodsId, String shopCode, String platformGoodsCode) {
        try {
            if (StringUtils.isBlank(shopCode) || StringUtils.isBlank(platformGoodsCode)) {
                return;
            }
            Map<String, Object> pdmMap = new HashMap<>();
            pdmMap.put("goodsCode", platformGoodsCode);
            pdmMap.put("shopCode", shopCode);
            log.debug("操作EBAY商品下架完成,准备更新PDM状态映射:{}", JSON.toJSONString(pdmMap));
            String result = HttpUtils.post(FIX_PDM_GOODS_STATUS, pdmMap);
            log.debug("操作EBAY商品下架完成,完成更新PDM状态映射:{}", JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("操作EBAY商品下架完成,完成更新PDM状态映射失败,主键Id:{}", goodsId, e);
        }
    }

    /**
     * 回写pdm
     *
     * @param goodsHead
     * @param isUpdateSku 是否修改了sku
     */
    public void addPdmStatus(GoodsHead goodsHead, Boolean isUpdateSku) {
        if (!isUpdateSku && ObjUtil.equals(String.valueOf(goodsHead.getPdmStatus()), "1")) {
            return;
        }

        String goodsCode = goodsHead.getPdmGoodsCode();
        try {
            //商品编码和平台商品编码不一样  就不回写了
            if (!isUpdateSku && !ObjUtil.equals(goodsCode, goodsHead.getPlatformGoodsCode())) {
                log.info("用户自定义平台商品编码不回写到pdm,goodsCode:{},platformGoodsCode:{}", goodsCode, goodsHead.getPlatformGoodsCode());
                return;
            }
            Long operators = 738L;
            String deptId = "380";
            try {
                operators = Long.valueOf(goodsHead.getCreateBy());
            } catch (NumberFormatException e) {}
            SysUser sysUser = userService.selectUserById(Long.valueOf(operators));
            if (Objects.nonNull(sysUser)) {
                deptId = String.valueOf(sysUser.getDeptId());
            }

            CreateMappingDTO createMappingDTO = new CreateMappingDTO();
            createMappingDTO.setGoodsCode(goodsCode);
            createMappingDTO.setShopCode(goodsHead.getShopCode());
            createMappingDTO.setPlatformSku(goodsHead.getPlatformGoodsCode());
            createMappingDTO.setAsin(goodsHead.getPlatformGoodsId());
            createMappingDTO.setPrice(goodsHead.getStandardPrice());
            createMappingDTO.setOperators(String.valueOf(operators));
            createMappingDTO.setDeptId(deptId);
            createMappingDTO.setCreateBy(String.valueOf(operators));
            createMappingDTO.setSystemType("SMC");
            createMappingDTO.setBrandCode(goodsHead.getBrandCode());
            createMappingDTO.setPublishType(goodsHead.getPublishType());
            PDMHttpRequestBiz.addPdmMapping(createMappingDTO, goodsHead);

            log.debug("操作Ebay商品刊登完成,完成添加PDM状态映射:{}", JSON.toJSONString(createMappingDTO));
        } catch (Exception e) {
            log.error("操作Ebay商品刊登失败,回写PDM失败,主键:{}", goodsHead.getId(),e);
        }
    }


    /**
     * 回写pdm
     *
     * @param goodsHead
     * @param isUpdateSku 是否修改了sku
     */
    public void addPdmStatusV2(EbayGoodsHeadV2 goodsHead, Boolean isUpdateSku) {
        if (!isUpdateSku && ObjUtil.equals(String.valueOf(goodsHead.getPdmStatus()), "1")) {
            return;
        }

        String goodsCode = goodsHead.getPdmGoodsCode();
        try {
            //商品编码和平台商品编码不一样  并不是多变体 就不回写了
            if (!isUpdateSku && !ObjUtil.equals(goodsCode, goodsHead.getPlatformGoodsCode()) && !ObjUtil.equals(goodsHead.getPublishType(), 2)) {
                log.info("用户自定义平台商品编码不回写到pdm,goodsCode:{},platformGoodsCode:{}", goodsCode, goodsHead.getPlatformGoodsCode());
                return;
            }
            Long operators = ObjUtil.isEmpty(goodsHead.getCreateBy()) ? 2L : Long.valueOf(goodsHead.getCreateBy());
            SysUser sysUser = userService.selectUserById(operators);

            List<EbayListingSpecsItemV2> ebayListingSpecsItemV2s = ebayListingSpecsItemV2Service.selectEbayListingSpecsItemV2ListByHeadId(goodsHead.getId());
            if (CollectionUtils.isEmpty(ebayListingSpecsItemV2s)) {
                return;
            }

            for (EbayListingSpecsItemV2 ebayListingSpecsItemV2 : ebayListingSpecsItemV2s) {
                String pdmGoodsCode = ebayListingSpecsItemV2.getPdmGoodsCode();
                String platformGoodsCode = ebayListingSpecsItemV2.getPlatformGoodsCode();
                String price = ebayListingSpecsItemV2.getPrice();

                CreateMappingDTO createMappingDTO = new CreateMappingDTO();
                createMappingDTO.setGoodsCode(pdmGoodsCode);
                createMappingDTO.setShopCode(goodsHead.getShopCode());
                createMappingDTO.setPlatformSku(platformGoodsCode);
                createMappingDTO.setAsin(goodsHead.getPlatformGoodsId());
                createMappingDTO.setPrice(price);
                createMappingDTO.setOperators(String.valueOf(operators));
                createMappingDTO.setDeptId(String.valueOf(sysUser.getDeptId()));
                createMappingDTO.setCreateBy(String.valueOf(operators));
                createMappingDTO.setSystemType("SMC");
                createMappingDTO.setBrandCode(goodsHead.getBrandCode());
                createMappingDTO.setPublishType(goodsHead.getPublishType());
                PDMHttpRequestBiz.addEbayPdmMapping(createMappingDTO, goodsHead);
                log.debug("操作Ebay商品刊登完成,完成添加PDM状态映射:{}", JSON.toJSONString(createMappingDTO));
            }
        } catch (Exception e) {
            log.error("操作Ebay商品刊登失败,回写PDM失败,主键:{}", goodsHead.getId(),e);
        }
    }


    public String getAdaptationStatus(ItemCompatibilityListType compatibilityList) {
        if (ObjectUtils.isEmpty(compatibilityList) || ObjectUtils.isEmpty(compatibilityList.getCompatibility())) {
            //无适配数据 -> 缺适配
            return AdaptationStatusEnum.LOST.getStatus();
        }
        //刊登成功或失败 -> 已适配
        return AdaptationStatusEnum.WAIT.getStatus();
    }


    /**
     * 获取适配状态(刊登)
     *
     * @param goodsHead
     * @param compatibilityList
     * @param flag              true:请求api成功 false 请求失败
     */
    public void getAdaptationStatus(GoodsHead goodsHead, ItemCompatibilityListType compatibilityList, boolean flag) {
        if(StringUtils.isEmpty(goodsHead.getPdmGoodsCode())){
            return;
        }
        GoodsHead updateHead = new GoodsHead();
        updateHead.setId(goodsHead.getId());
        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodsCode(goodsHead.getPdmGoodsCode());
        Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
        if (ObjectUtils.isNotEmpty(pdmGood)) {
            String adaptFlag = pdmGood.getAdaptFlag();
            if (ObjUtil.equals(adaptFlag, "N")) {
                updateHead.setAdaptationStatus(AdaptationStatusEnum.NO.getStatus());
                goodsHeadService.updateListingGoodsHead(updateHead);
                return;
            }
        }
        //是否成功
        if (flag) {
            if (ObjectUtils.isEmpty(compatibilityList) || ObjectUtils.isEmpty(compatibilityList.getCompatibility())) {
                //无适配数据 -> 缺适配
                goodsHead.setAdaptationStatus(AdaptationStatusEnum.LOST.getStatus());
                return;
            }
            //刊登成功 -> 已适配
            goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());

        } else {
            if (ObjectUtils.isEmpty(compatibilityList) || ObjectUtils.isEmpty(compatibilityList.getCompatibility())) {
                //无适配数据 -> 缺适配
                goodsHead.setAdaptationStatus(AdaptationStatusEnum.LOST.getStatus());
                return;
            }
            //刊登失败 ->已适配
            goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());

        }
    }


    /**
     * 获取适配状态(刊登)
     *
     * @param goodsHead
     * @param compatibilityList
     * @param flag              true:请求api成功 false 请求失败
     */
    public void getAdaptationStatusV2(EbayGoodsHeadV2 goodsHead, ItemCompatibilityListType compatibilityList, boolean flag) {
        EbayGoodsHeadV2 updateHead = new EbayGoodsHeadV2();
        updateHead.setId(goodsHead.getId());
        SaleGoodsDTO query = new SaleGoodsDTO();
        query.setGoodsCode(goodsHead.getPdmGoodsCode());
        Goods pdmGood = goodsService.selectGoodsByGoodCode(query);
        if (ObjectUtils.isNotEmpty(pdmGood)) {
            String adaptFlag = pdmGood.getAdaptFlag();
            if (ObjUtil.equals(adaptFlag, "N")) {
                updateHead.setAdaptationStatus(AdaptationStatusEnum.NO.getStatus());
                ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateHead);
                return;
            }
        }
        //是否成功
        if (flag) {
            if (ObjectUtils.isEmpty(compatibilityList) || ObjectUtils.isEmpty(compatibilityList.getCompatibility())) {
                //无适配数据 -> 缺适配
                goodsHead.setAdaptationStatus(AdaptationStatusEnum.LOST.getStatus());
                return;
            }
            //刊登成功 -> 已适配
            goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());

        } else {
            if (ObjectUtils.isEmpty(compatibilityList) || ObjectUtils.isEmpty(compatibilityList.getCompatibility())) {
                //无适配数据 -> 缺适配
                goodsHead.setAdaptationStatus(AdaptationStatusEnum.LOST.getStatus());
                return;
            }
            //刊登失败 ->已适配
            goodsHead.setAdaptationStatus(AdaptationStatusEnum.WAIT.getStatus());

        }
    }

    /**
     * 更新ebay库存
     * ReviseInventoryStatus 支持最大4个listing更新库存
     *
     * @param headList
     * @param shop
     * @param inventoryMap
     */
    public void updateEbayStock(List<GoodsHead> headList, ConfigStoreInfo shop, Map<String, Integer> inventoryMap,String logMsg) {
        List<List<GoodsHead>> lists = Lists.partition(headList, 4);
        //ebay  ReviseInventoryStatus api 限速 6000次/15秒
        int bucketCapacity = lists.size();
        int requestRatePerSeconds = 6000;
        LeakyBucket leakyBucket = new LeakyBucket(bucketCapacity, requestRatePerSeconds / 15);
        Integer errorCount = 0;
        StringBuilder errorMessages = new StringBuilder();
        List<Integer> errorLisitngList = new ArrayList<>();
        List<GoodsHead> errorGoodsHeadList = new ArrayList<>();
        try {
            for (List<GoodsHead> list : lists) {
                while (!leakyBucket.add(1)) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        log.error("ebay"+logMsg+"线程休眠异常", e);
                        Thread.currentThread().interrupt();
                    }
                }
                if (PlatformTypeEnum.EB.name().equals(shop.getPlatform()) && redisService.exists(RedisKeyEnum.EBAY_AUTO_STOCK_UPDATE.getKey())) {
                    log.info("EB"+logMsg+",当前店铺:{},redis开关为关闭状态,不执行自动库存更新", shop.getShopCode());
                    return;
                }
                //更新ebay库存
                errorCount = getErrorCount(shop, inventoryMap, errorCount, errorMessages, errorLisitngList, list, errorGoodsHeadList,logMsg);
            }
            //添加库存备份记录 库存监控会用到
            addInventoryBackUp(headList, errorLisitngList);
            //添加库存操作记录
            addListingInventory(headList, shop, errorCount, errorMessages, errorLisitngList);
            //待更新库存的listing插入库存待更新记录
            addInventoryPending(shop, errorGoodsHeadList);
        } catch (Exception e) {
            log.error("ebay库存更新失败,店铺:{},", shop.getShopCode(), e);
        }
    }

    /**
     * 更新成功的库存记录放到备份表 用作下次更新库存的对比
     *
     * @param headList
     * @param errorLisitngList
     */
    private void addInventoryBackUp(List<GoodsHead> headList, List<Integer> errorLisitngList) {
        List<GoodsHead> collect = headList.stream().filter(e -> !errorLisitngList.contains(e.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(collect)) {
            return;
        }
        List<List<GoodsHead>> backUpList = Lists.partition(collect, 500);
        for (List<GoodsHead> goodsHeadList : backUpList) {
            GoodsHeadBackup backup = new GoodsHeadBackup();
            backup.setShopCode(collect.get(0).getShopCode());
            backup.setGoodsIds(goodsHeadList.stream().map(GoodsHead::getId).map(String::valueOf).collect(Collectors.toList()));
            backup.setRemark(OperTypeEnum.INVENTORY_UPDATE.name());

            //删除备份表昨天的数据
            goodsHeadBackupService.deleteGoodsHeadBackupByTime(backup);
            List<GoodsHeadBackup> headBackupList = goodsHeadBackupService.selectGoodsHeadBackupByGoodsIds(goodsHeadList.stream().map(GoodsHead::getId).map(String::valueOf).collect(Collectors.toList()), OperTypeEnum.INVENTORY_UPDATE.name());

            //清空库存更新上一次的更新记录
            if (ObjectUtils.isNotEmpty(headBackupList)) {
                goodsHeadBackupService.deleteGoodsHeadBackup(backup);
            }

            List<GoodsHeadBackup> goodsHeadBackupList = new ArrayList<>();
            for (GoodsHead goodsHead : goodsHeadList) {
                GoodsHeadBackup goodsHeadBackup = new GoodsHeadBackup();
                goodsHeadBackup.setShopCode(goodsHead.getShopCode());
                goodsHeadBackup.setGoodsId(String.valueOf(goodsHead.getId()));
                goodsHeadBackup.setContext(JSON.toJSONString(goodsHead));
                goodsHeadBackup.setRemark(OperTypeEnum.INVENTORY_UPDATE.name());
                goodsHeadBackup.setCreateBy(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                goodsHeadBackup.setUpdateBy(StringUtils.isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy());
                goodsHeadBackup.setCreateTime(DateUtils.getNowDate());
                goodsHeadBackup.setUpdateTime(DateUtils.getNowDate());
                goodsHeadBackupList.add(goodsHeadBackup);
            }
            //添加库存备份记录
            goodsHeadBackupService.insertGoodsHeadBackupBatch(goodsHeadBackupList);

        }

    }

    /**
     * 添加库存操作记录
     *
     * @param headList
     * @param shop
     * @param errorCount
     * @param errorMessages
     * @param errorLisitngList
     */
    private void addListingInventory(List<GoodsHead> headList, ConfigStoreInfo shop, Integer errorCount, StringBuilder errorMessages, List<Integer> errorLisitngList) {
        listingInventoryOperateService.deleteOperateByShop(shop);
        ListingInventoryOperate operate = new ListingInventoryOperate();
        operate.setPlatform(shop.getPlatform());
        operate.setSite(shop.getSite());
        operate.setShopCode(shop.getShopCode());
        operate.setCount(headList.size());
        operate.setErrorCount(errorCount);
        operate.setErrorMsg(ObjectUtils.isEmpty(errorMessages) ? null : JSON.toJSONString(AjaxResult.error(errorMessages.toString(), errorLisitngList)));
        operate.setCreateBy("1");
        operate.setCreateTime(DateUtils.getNowDate());
        operate.setUpdateBy("1");
        operate.setUpdateTime(DateUtils.getNowDate());
        operate.setDelFlag(SMCCommonEnum.NORMAL.getValue());
        operate.setListing(JSON.toJSONString(headList.stream().map(GoodsHead::getId).collect(Collectors.toList())));
        listingInventoryOperateService.insertListingInventoryOperate(operate);
    }

    /**
     * 待更新库存的listing插入库存待更新记录
     *
     * @param shop
     * @param goodsHeadList
     */
    private void addInventoryPending(ConfigStoreInfo shop, List<GoodsHead> goodsHeadList) {
        if (ObjectUtils.isEmpty(goodsHeadList)) {
            return;
        }
        InventoryPendingListing inventoryPendingListing = new InventoryPendingListing();
        inventoryPendingListing.setShopCode(shop.getShopCode());
        inventoryPendingListing.setPlatform(shop.getPlatform());
        inventoryPendingListing.setSite(shop.getSite());
        List<InventoryPendingListing> pendingListingList = inventoryPendingListingService.selectInventoryPendingListingList(inventoryPendingListing);
        if (CollectionUtils.isEmpty(pendingListingList)) {
            inventoryPendingListing.setListing(JSON.toJSONString(goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList())));
            inventoryPendingListing.setDelFlag(SMCCommonEnum.NORMAL.getValue());
            inventoryPendingListing.setCreateBy("1");
            inventoryPendingListing.setUpdateBy("1");
            inventoryPendingListing.setCreateTime(DateUtils.getNowDate());
            inventoryPendingListing.setUpdateTime(DateUtils.getNowDate());
            inventoryPendingListingService.insertInventoryPendingListing(inventoryPendingListing);
            return;
        }

        InventoryPendingListing pendingListing = pendingListingList.get(0);
        List<Integer> pendingListingGoodsHeadList = JSON.parseArray(pendingListing.getListing(), Integer.class);
        if (ObjUtil.isEmpty(pendingListingGoodsHeadList)){
            pendingListingGoodsHeadList=goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList());
        }else {
            pendingListingGoodsHeadList.addAll(goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList()));
        }
        pendingListing.setListing(JSON.toJSONString(pendingListingGoodsHeadList));
        inventoryPendingListingService.updateInventoryPendingListing(pendingListing);
    }

    /**
     * 更新ebay库存 同时返回错误数量
     * ebay的库存  会影响刊登的额度
     * <p>
     * 还要根据店铺配置的库存上限取值
     *
     * @param shop
     * @param inventoryMap
     * @param errorCount
     * @param errorMessages
     * @param list
     * @param errorLisitngList
     * @param errorGoodsHeadList
     * @param logMsg
     * @return
     */
    private Integer getErrorCount(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, Integer errorCount, StringBuilder errorMessages,
                                  List<Integer> errorLisitngList, List<GoodsHead> list, List<GoodsHead> errorGoodsHeadList,String logMsg) {
        //调api更新库存
        AjaxResult result = updateInventory2Ebay(shop, inventoryMap, list,0);
        if (result.isSuccess()) {
            //同步更新smc库存
            updateEbayLocalStock(shop, list, inventoryMap,logMsg, SYS_USER_ID);
        }
        //部分成功 且错误消息包含了已知的错误消息
        if (!result.isSuccess() && PlatformErrorEnum.containsErrorMsg(PlatformTypeEnum.EB.name(), String.valueOf(result.get(AjaxResult.MSG_TAG)))) {
            List<String> itemIDs = extractItemIDs(String.valueOf(result.get(AjaxResult.MSG_TAG)));
            errorCount = itemIDs.size();
            errorMessages.append(result.get(AjaxResult.MSG_TAG)).append(";");
            errorLisitngList.addAll(list.stream().filter(l -> itemIDs.contains(l.getPlatformGoodsId())).map(GoodsHead::getId).collect(Collectors.toList()));
            errorGoodsHeadList.addAll(list.stream().filter(l -> itemIDs.contains(l.getPlatformGoodsId())).collect(Collectors.toList()));
            //更新库存成功的listing
            List<GoodsHead> updateList = list.parallelStream().filter(l -> !itemIDs.contains(l.getPlatformGoodsId())).collect(Collectors.toList());
            updateEbayLocalStock(shop, updateList, inventoryMap,logMsg, SYS_USER_ID);

            //更新listing 状态为已下架
            list.parallelStream().filter(l -> itemIDs.contains(l.getPlatformGoodsId())).forEach(l -> {
                l.setPublishStatus(PublishStatus.OFF_SALE.getType());
                goodsHeadService.updateListingGoodsHead(l);
            });

        }
        //部分成功 且错误消息不包含了listing已经下架的错误消息
        if (!result.isSuccess() && !PlatformErrorEnum.containsErrorMsg(PlatformTypeEnum.EB.name(), String.valueOf(result.get(AjaxResult.MSG_TAG)))) {
            errorCount += list.size();
            errorMessages.append(result.get(AjaxResult.MSG_TAG)).append(";");
            errorLisitngList.addAll(list.stream().map(GoodsHead::getId).collect(Collectors.toList()));
            errorGoodsHeadList.addAll(list);
            log.error("ebay"+logMsg+",错误信息:{}", result);
            //发送钉钉警告
            String msg = "ebay"+logMsg+",店铺:" + shop.getShopCode()
                    + ",平台销售编码:" + list.stream().map(GoodsHead::getPlatformGoodsId).collect(Collectors.joining(","))
                    + ",错误信息:" + result.get(AjaxResult.MSG_TAG);
            dingdingMonitorInfoBiz.monitorMediumRiskAndSend(MonitorEnum.EB_INVENTORY_UPDATE_FAIL, msg);
        }
        return errorCount;
    }

    /**
     * 调api更新ebay库存
     *
     * @param shop
     * @param inventoryMap
     * @param list
     * @return
     */
    private AjaxResult updateInventory2Ebay(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, List<GoodsHead> list,Integer count){
        EbayInventoryVO vo = new EbayInventoryVO();
        String jsonRequest = null;
        try {
            vo.setAccountCode(shop.getShopCode());
            vo.setEbayInventoryList(list.stream().map(goodsHead -> {
                Integer quantity = getQuantity(shop, inventoryMap, goodsHead);
                EbayInventoryVO.EbayInventory ebayInventory = new EbayInventoryVO.EbayInventory();
                ebayInventory.setItemId(goodsHead.getPlatformGoodsId());
                ebayInventory.setQuantity(quantity);
                return ebayInventory;
            }).collect(Collectors.toList()));

            // 将请求转换为JSON
            jsonRequest = JSON.toJSONString(vo);

            String data = HttpUtils.post(REVISE_INVENTORY_STATUS_URL, jsonRequest, 30000);
            //记录日志
            logRecordService.insertSmcLogRecord(shop.getPlatform(), shop.getShopCode(), shop.getSite(), "库存更新-updateInventory2Ebay", jsonRequest, data, vo.getEbayInventoryList().size(), 0);

            AjaxResult result = JSON.parseObject(data, AjaxResult.class);
            if (count > 2) {
                return result;
            }
            if (ObjectUtils.isNotEmpty(result) && PublishUpdateErrorEnum.containsErrorMsg("EB",JSON.toJSONString(result.get(AjaxResult.MSG_TAG)))) {
                log.error("ebay更新库存失败,请求vo:{},重试次数:{},错误信息:{}", jsonRequest, count, JSON.toJSONString(result.get(AjaxResult.MSG_TAG)));
                // 清理请求JSON的内存
                jsonRequest = MemoryCleanupUtil.cleanupJsonString(jsonRequest);
                return updateInventory2Ebay(shop, inventoryMap, list, count + 1);
            }
            return result;
        } catch (Exception e) {
            // e 是Read timed out异常
            if (e instanceof HttpException) {
                log.error("ebay更新库存异常接口超时,请求vo:{},重试次数:{},错误信息:{}", jsonRequest, count, e.getMessage());
                return AjaxResult.error("请求接口超时，请稍后重试.");
            }
            log.error("ebay更新商品异常,请求vo:{},错误信息:{}", jsonRequest, e.getMessage());
            return AjaxResult.error("请求ebay接口异常：" + e.getMessage());
        } finally {
            // 清理内存
            vo = null;
            jsonRequest = MemoryCleanupUtil.cleanupJsonString(jsonRequest);
        }
    }

    /**
     * 多属性库存更新
     *
     * @param list
     * @param updateInventoryHeadV2Map
     * @param inventoryMap
     * @param shop
     */
    public void ebayPolytropicInventoryUpdate(List<EbayListingSpecsItemV2> list, Map<Long, String> updateInventoryHeadV2Map, Map<String, Integer> inventoryMap, ConfigStoreInfo shop, Integer count) {
        EbayInventoryVO vo = new EbayInventoryVO();
        String jsonRequest = null;
        try {
            vo.setAccountCode(shop.getShopCode());
            vo.setEbayInventoryList(list.stream().map(spec -> {
                Integer quantity = getQuantityV2(shop, inventoryMap, spec);
                EbayInventoryVO.EbayInventory ebayInventory = new EbayInventoryVO.EbayInventory();
                ebayInventory.setItemId(updateInventoryHeadV2Map.get(spec.getHeadId()));
                ebayInventory.setSku(spec.getPlatformGoodsCode());
                ebayInventory.setQuantity(quantity);
                return ebayInventory;
            }).collect(Collectors.toList()));

            // 将请求转换为JSON
            jsonRequest = JSON.toJSONString(vo);

            String data = HttpUtils.post(REVISE_INVENTORY_STATUS_URL, jsonRequest, 30000);
            //记录日志
            logRecordService.insertSmcLogRecord(shop.getPlatform(), shop.getShopCode(), shop.getSite(), "库存更新-updateInventory2Ebay", jsonRequest, data, vo.getEbayInventoryList().size(), 0);

            AjaxResult result = JSON.parseObject(data, AjaxResult.class);
            if (result.isSuccess()) {
                //同步更新smc库存
                updateEbayPolytropicLocalStock(shop, list, inventoryMap, SYS_USER_ID);
                return;
            }
            if (count > 2) {
                return;
            }
            if (ObjectUtils.isNotEmpty(result) && PublishUpdateErrorEnum.containsErrorMsg("EB", JSON.toJSONString(result.get(AjaxResult.MSG_TAG)))) {
                log.error("ebay多属性更新库存失败,请求vo:{},重试次数:{},错误信息:{}", jsonRequest, count, JSON.toJSONString(result.get(AjaxResult.MSG_TAG)));
                // 清理请求JSON的内存
                jsonRequest = MemoryCleanupUtil.cleanupJsonString(jsonRequest);
                ebayPolytropicInventoryUpdate(list, updateInventoryHeadV2Map, inventoryMap, shop, count + 1);
            }
        } catch (Exception e) {
            if (e instanceof HttpException) {
                log.error("ebay多属性更新库存异常接口超时,请求vo:{},重试次数:{},错误信息:{}", jsonRequest, count, e.getMessage());
                return;
            }
            log.error("ebay多属性更新库存异常,请求vo:{},错误信息:{}", jsonRequest, e.getMessage());
        } finally {
            // 清理内存
            vo = null;
            jsonRequest = MemoryCleanupUtil.cleanupJsonString(jsonRequest);
        }
    }

    /**
     * 更新多属性本地库存
     *
     * @param shop
     * @param list
     * @param inventoryMap
     * @param currentUserId
     */
    public void updateEbayPolytropicLocalStock(ConfigStoreInfo shop, List<EbayListingSpecsItemV2> list, Map<String, Integer> inventoryMap, String currentUserId) {
        list.parallelStream().forEach(spec -> {
            StringBuilder log = new StringBuilder();
            Integer quantity = getQuantityV2(shop, inventoryMap, spec);
            log.append("ebay平台商品编码:").append(spec.getPlatformGoodsCode()).append(",库存更新成功,库存由").append(spec.getStock()).append("变为").append(quantity);

            EbayListingSpecsItemV2 updateSpec = new EbayListingSpecsItemV2();
            updateSpec.setId(spec.getId());

            updateSpec.setStock(String.valueOf(quantity));
            ebayListingSpecsItemV2Service.updateEbayListingSpecsItemV2(updateSpec);

            ebayListingLogV2Service.insertSuccessListingLog(log.toString(), currentUserId, spec.getHeadId());

        });

    }

    /**
     * 获取库存 如果库存记录表没有记录则实时查一次库存接口
     *
     * @param shop
     * @param inventoryMap
     * @param goodsHead
     * @return
     */
    public Integer getQuantity(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, GoodsHead goodsHead) {
        //库存上限
        Integer shopQuantity = inventoryBiz.getSkuQuantityAugment(goodsHead.getPdmGoodsCode(), null, shop.getShopCode());
        //库存下限
        Integer quantityFloor = inventoryBiz.getSkuQuantityFloor(goodsHead.getPdmGoodsCode(), null, shop.getShopCode());
        //实时库存
        Integer stock = inventoryMap.get(goodsHead.getSiteCode() + goodsHead.getPdmGoodsCode());
        if (ObjectUtils.isEmpty(stock)) {
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(goodsHead.getPdmGoodsCode()), shop.getSite(), false);
            if (ObjectUtils.isEmpty(thirdpartyFbmDTOList)) {
                //不更新库存
                return goodsHead.getStockOnSalesQty().intValue();
            }
            inventoryMap.put(goodsHead.getSiteCode() + goodsHead.getPdmGoodsCode(), thirdpartyFbmDTOList.get(0).getSellableQty());
            stock=thirdpartyFbmDTOList.get(0).getSellableQty();
        }
        //实际库存大于等于库存上限 直接更新为库存上限值
        if (ObjectUtils.isNotEmpty(shopQuantity) && stock >= shopQuantity) {
            return shopQuantity;
        }
        //实时库存小于等于库存下限 直接更新为0
        if (ObjectUtils.isNotEmpty(quantityFloor) && stock <= quantityFloor) {
            return 0;
        }
        return stock;
    }

    /**
     * 通过错误信息提取itemID
     *
     * @param errorMessage
     * @return
     */
    public static List<String> extractItemIDs(String errorMessage) {
        List<String> itemIDs = new ArrayList<>();
        Pattern pattern = Pattern.compile("\"(\\d+)\"");
        Matcher matcher = pattern.matcher(errorMessage);

        while (matcher.find()) {
            String itemIDStr = matcher.group(1);
            itemIDs.add(itemIDStr);
        }
        return itemIDs;
    }

    /**
     * 更新ebay本地库存
     *
     * @param goodsHeadList
     * @param inventoryMap
     * @param shop
     */
    public void updateEbayLocalStock(ConfigStoreInfo shop, List<GoodsHead> goodsHeadList, Map<String, Integer> inventoryMap,String logMsg, String currentUserId) {
        goodsHeadList.parallelStream().forEach(l -> {
            StringBuilder log = new StringBuilder();
            // logMsg+",更新成功,库存由" + l.getStockOnSalesQty() + "变为" + nowStock, currentUserId, l.getId()
            log.append(logMsg).append(",更新成功,库存由").append(l.getStockOnSalesQty()).append("变为").append(getQuantity(shop, inventoryMap, l));
            if(YES.equals(l.getIndependentSiteOnly())) {
                log.append(",SKU:").append(l.getPdmGoodsCode()).append("是独立站专卖，当前listing的库存更新为0");
            }

            GoodsHead update = new GoodsHead();
            BigDecimal nowStock = BigDecimal.valueOf(getQuantity(shop, inventoryMap, l));
            listingLogService.insertSuccessListingLog(log.toString(), currentUserId, l.getId());
            update.setStockOnSalesQty(nowStock);
            update.setId(l.getId());
            goodsHeadService.updateListingGoodsHead(update);
        });
    }

    public CensorshipInfo pushCheckV2(ItemType itemType, String shopCode, CensorshipInfo censorshipInfo, EbayGoodsHeadV2 goodsHead, int count) throws IOException {
        if (ObjectUtils.isEmpty(itemType)) {
            throw new BusinessException("ebay送检商品失败,ebayItem为空");
        }

        EbayItemDTO ebayItemDTO = new EbayItemDTO();
        ebayItemDTO.setItemType(itemType);
        String replaces = JSONObject.toJSONString(ebayItemDTO).replace("\"sKU\"", "\"sku\"")
                .replace("\"mPN\"", "\"mpn\"")
                .replace("\"uPC\"", "\"upc\"");
        try {
            //发送请求进行送检
            UrlReplaceEntity urlReplaceEntity = new UrlReplaceEntity();
            urlReplaceEntity.setUrl(VERIFY_ITEM_URL);
            urlReplaceEntity.setAccountCode(shopCode);
            String post = HttpUtils.post(Utils.replaceUrl(urlReplaceEntity), replaces);

            if (StringUtils.isNotBlank(post) && PublishUpdateErrorEnum.containsErrorMsg("EB", post)) {
                log.error("ebay送检商品失败,id:{},重试次数:{},错误信息:{}", goodsHead.getId(), count, post);
                // 清理内存后再重试
                MemoryCleanupUtil.cleanupAll(itemType, ebayItemDTO, replaces);
                return pushCheckV2(itemType, shopCode, censorshipInfo, goodsHead, count + 1);
            }
            Map dataMap = JSON.parseObject(post, Map.class);
            if ("500".equals(String.valueOf(dataMap.get("code")))) {
                censorshipInfo.setStatus(0);
                censorshipInfo.setReason(String.valueOf(dataMap.get("msg")));
                goodsHead.setCensorship(String.valueOf(SMCCommonEnum.DEFEAT.getValue()));
                return censorshipInfo;
            }
            //处理数据
            Map map = JSON.parseObject(String.valueOf(dataMap.get("data")), Map.class);
            handleFeeV2(map, censorshipInfo, goodsHead);
            return censorshipInfo;
        } finally {
            // 清理内存
            MemoryCleanupUtil.cleanupAll(itemType, ebayItemDTO, replaces);
        }
    }

    /**
     * 下架v2
     *
     * @param ebayGoodsHead
     * @param publishStatus
     */
    public boolean offEbayV2(EbayGoodsHeadV2 ebayGoodsHead, PublishStatus publishStatus) {
        Integer headId = Math.toIntExact(ebayGoodsHead.getId());
        String platformGoodsId = ebayGoodsHead.getPlatformGoodsId();
        log.info("ebay下架任务执行开始->listing id:{}", headId);
        AjaxResult handleResult = retryable.retryableToApiMsg(() -> ebayEndItem(ebayGoodsHead.getShopCode(), platformGoodsId));
        if (!handleResult.isSuccess()) {
            log.error("ebay下架定时任务执行失败->商品编码:{},错误信息：{}", ebayGoodsHead.getPlatformGoodsCode(), handleResult.get(AjaxResult.MSG_TAG));
            //修改smc中的状态
            PublishStatus publishStatusResult = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? PublishStatus.RE_LISTING_OFF_SHELF_FAIL : PublishStatus.OFF_SHELF_FAIL;
            ebayGoodsHead.setPublishStatus(String.valueOf(publishStatusResult.getType()));
            ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(ebayGoodsHead);

            //日志写入
            String msg = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? "重上架主键[" + headId + "],平台销售编码[" + platformGoodsId + "]商品下架失败" : "平台销售编码[" + platformGoodsId + "]商品下架成功";
            ebayListingLogV2Service.insertErrorListingLog(msg, StringUtils.isBlank(ebayGoodsHead.getUpdateBy()) ? ebayGoodsHead.getCreateBy() : ebayGoodsHead.getUpdateBy(), ebayGoodsHead.getId(), String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));

            //重上架任务状态也需置为失败
            if (ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING)) {
                TaskConfiguration taskConfiguration = new TaskConfiguration();
                taskConfiguration.setIsSuccess(String.valueOf(SMCCommonEnum.RE_FAIL.getValue()));
                taskConfiguration.setHeadId(String.valueOf(headId));
                taskConfigurationService.updateTaskConfigurationByHeadId(taskConfiguration);
            }
            return false;
        }
        //修改smc中的状态  下架成功 定时重上架就变回定时重上架,其余的变成在售
        PublishStatus publishStatusResult = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? PublishStatus.TIMED_RE_LISTING : PublishStatus.OFF_SALE;
        ebayGoodsHead.setPublishStatus(String.valueOf(publishStatusResult.getType()));
        ebayGoodsHead.setOffTime(new Date());
        ebayGoodsHead.setPublishingHandler("处理完成");
        ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(ebayGoodsHead);
        fixPdmStatus(ebayGoodsHead.getId(), ebayGoodsHead.getShopCode(), ebayGoodsHead.getPlatformGoodsCode());

        String msg = ObjUtil.equals(publishStatus, PublishStatus.TIMED_RE_LISTING) ? "重上架主键[" + headId + "],平台销售编码[" + platformGoodsId + "]商品下架成功,待重新上架" : "平台销售编码[" + platformGoodsId + "]商品下架成功";
        ebayListingLogV2Service.insertSuccessListingLog(msg, StringUtils.isBlank(ebayGoodsHead.getUpdateBy()) ? ebayGoodsHead.getCreateBy() : ebayGoodsHead.getUpdateBy(), ebayGoodsHead.getId());
        return true;
    }

    /**
     * ebay刊登V2
     *
     * @param itemDTO
     * @param operationName
     */
    public void addEbayV2(ItemDTO itemDTO, String operationName) {
        EbayGoodsHeadV2 goodsHead = itemDTO.getEbayGoodsHead();
        ItemType ebayItem = itemDTO.getEbayItem();
        if (ObjectUtils.isEmpty(ebayItem)) {
            throw new BusinessException("ebay刊登商品失败,ebayItem为空");
        }
        ebayItem.setUUID(itemDTO.getEbayUUID());
        EbayItemDTO ebayItemDTO = new EbayItemDTO();
        ebayItemDTO.setItemType(ebayItem);
        String s = JSON.toJSONString(ebayItemDTO);

        try {
            //从redis获取当前商品是否刊登过
            String redisKey = RedisKeyEnum.EBAY_PUSH_Listing.getKey() + goodsHead.getId();
            if (redisService.exists(redisKey)) {
                log.info("ebay刊登商品---商品id: {} 已经刊登过,请勿重复刊登", goodsHead.getId());
                return;
            }
            log.info("ebay刊登商品---商品id: {}", goodsHead.getId());
            //发送请求进行 对接api服务进行刊登
            String post = addItem(Math.toIntExact(goodsHead.getId()), goodsHead.getShopCode(), s, 1);
            log.info("ebay刊登商品结束---商品id:{},result:{}", goodsHead.getId(), post);
            AjaxResult handleResult = JSON.parseObject(post, AjaxResult.class);
            if (!handleResult.isSuccess()) {
                String errorMsg = String.valueOf(handleResult.get(AjaxResult.MSG_TAG));
                // 使用platformErrorBiz处理错误
                platformErrorBiz.handleEbayErrorV2(goodsHead, errorMsg);

                if (PublishStatus.PUBLISH_FAIL.getType().equals(Integer.valueOf(goodsHead.getPublishStatus()))
                        || PublishStatus.UPDATING_FAIL.getType().equals(Integer.valueOf(goodsHead.getPublishStatus()))) {
                    throw new BusinessException(errorMsg);
                }

            }
            //写入redis 用于防止重复刊登
            redisService.setCacheObject(redisKey, String.valueOf(handleResult.get(AjaxResult.MSG_TAG)), 6L, TimeUnit.HOURS);
            //为重售则还需要插入平台销售编码更新日志以及定时任务表更新状态
            if (ObjUtil.equals(operationName, OperationTypeEnum.RELIST.getName())) {
                syncRelistLogV2(goodsHead, String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));
            } else {
                goodsCategoryMappingService.addNumByPdmGoodsCode(PlatformTypeEnum.EB.name(), goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode(), String.valueOf(goodsHead.getCategoryId()));
            }
            //更新listing状态
            goodsHead.setSmcFlag(ObjectUtils.equals(goodsHead.getSmcFlag(), 2) ? goodsHead.getSmcFlag() : 0);
            goodsHead.setPublishStatus(String.valueOf(PublishStatus.SALEING.getType()));
            goodsHead.setOnlineTime(new Date());
            goodsHead.setPlatformGoodsId(String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));
            goodsHead.setPublishingHandler("处理完成");
            ItemCompatibilityListType compatibilityList = ebayItem.getItemCompatibilityList();
            getAdaptationStatusV2(goodsHead, compatibilityList, true);
            ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(goodsHead);
            //回写pdm
            addPdmStatusV2(goodsHead, false);
            //推送mq
            mqpushBiz.publishPush2MQ(goodsHead.getPlatform(), goodsHead.getSiteCode(), goodsHead.getPdmGoodsCode(), goodsHead.getPlatformGoodsCode(), goodsHead.getPlatformGoodsId());
        } finally {
            // 使用内存清理工具类清理内存
            MemoryCleanupUtil.cleanupAll(ebayItem, ebayItemDTO, s);
        }
    }

    /**
     * ebay更新
     *
     * @param itemDTO
     * @param adaptiveFlag ture:适配数据更新(全量适配) false:其他数据更新
     */
    public String updateEbayV2(ItemDTO itemDTO, boolean adaptiveFlag) {
        EbayGoodsHeadV2 goodsHead = itemDTO.getEbayGoodsHead();
        ItemType ebayItem = itemDTO.getEbayItem();
        if (ObjectUtils.isEmpty(ebayItem)) {
            throw new BusinessException("ebay更新商品失败,ebayItem为空");
        }
        //更新适配数据 组装适配数据
        if (adaptiveFlag) {
            ebayItem = buildAdapterDataV2(goodsHead, ebayItem, itemDTO.getEbayCompatibilityProperties());

        }
        EbayItemDTO ebayItemDTO = new EbayItemDTO();
        ebayItemDTO.setItemType(ebayItem);
        String s = JSON.toJSONString(ebayItemDTO);
        try {
            //        13、发送请求进行 对接api服务进行刊登
            log.info("ebay修改商品---商品id: {}", goodsHead.getId());
            String post = updateItem(Math.toIntExact(goodsHead.getId()), goodsHead.getShopCode(), s, 1);
            log.info("ebay修改商品结束---商品id: {},result:{}", goodsHead.getId(), post);
            AjaxResult handleResult = JSON.parseObject(post, AjaxResult.class);
            //14、获取对应的反馈结果进行处理，成功失败修改对应的smc中的状态，以及pdm中的状态
            if (handleResult.isSuccess()) {
                //修改smc中的状态
                goodsHead.setPublishStatus(String.valueOf(PublishStatus.SALEING.getType()));
                goodsHead.setPublishingHandler("处理完成");
                if (adaptiveFlag) {
                    //修改 适配状态
                    ItemCompatibilityListType compatibilityList = ebayItem.getItemCompatibilityList();
                    getAdaptationStatusV2(goodsHead, compatibilityList, true);
                }
                ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(goodsHead);
                //处理mapping 映射
                updateMappingV2(goodsHead);
                //处理待办数据->已处理
                smcTodoBiz.updateTodoStatusByListingUpdate(Math.toIntExact(goodsHead.getId()), TodoStatusEnum.FINISH_STATUS);

                ebayListingLogV2Service.insertSuccessListingLog("ebay商品更新成功到ebay平台", isBlank(goodsHead.getUpdateBy()) ? goodsHead.getCreateBy() : goodsHead.getUpdateBy(),
                        goodsHead.getId());
                return "";
            }
            //该listing 如果下架直接扭转到下架去
            if (!handleResult.isSuccess()) {
                smcTodoBiz.updateTodoStatusByListingUpdate(Math.toIntExact(goodsHead.getId()), TodoStatusEnum.WAIT_STATUS);
                //根据错误进行业务处理
                platformErrorBiz.handleEbayErrorV2(goodsHead, String.valueOf(handleResult.get(AjaxResult.MSG_TAG)));
                return String.valueOf(handleResult.get(AjaxResult.MSG_TAG));
            }
            return "";
        } finally {
            // 使用内存清理工具类清理内存
            MemoryCleanupUtil.cleanupAll(ebayItem, ebayItemDTO, s);
        }
    }

    private ItemType buildAdapterDataV2(EbayGoodsHeadV2 goodsHead, ItemType ebayItem, EbayCompatibilityProperties ebayCompatibilityProperties) {
        try {
            List<EbayListingAdaptiveV2> ebayListingAdaptiveV2s = ebayListingAdaptiveV2Service.selectListByEbayHeadId(goodsHead.getId());
            //此处只指定适配更新,所以可以指定适配全量替换设置为true
            if (ObjectUtils.isNotEmpty(ebayCompatibilityProperties)) {
                ebayItem.setItemCompatibilityList(buildEbayItemV2Biz.getItemCompatibilityListTypeV2(true, ebayCompatibilityProperties, ebayListingAdaptiveV2s));
            } else {
                ebayItem.setItemCompatibilityList(buildEbayItemV2Biz.getItemCompatibilityListTypeV2(true, BuildEbayItemBiz.getDefaultBySite(goodsHead.getSiteCode()), ebayListingAdaptiveV2s));
            }
            return ebayItem;
        } catch (Exception e) {
            log.error("构建ebay适配数据V2异常", e);
            // 确保在异常情况下也能返回原始对象
            return ebayItem;
        }
    }

    /**
     * 获取库存 如果库存记录表没有记录则实时查一次库存接口
     *
     * @param shop
     * @param inventoryMap
     * @param spec
     * @return
     */
    public Integer getQuantityV2(ConfigStoreInfo shop, Map<String, Integer> inventoryMap, EbayListingSpecsItemV2 spec) {
        //库存上限
        Integer shopQuantity = inventoryBiz.getSkuQuantityAugment(spec.getPdmGoodsCode(), null, shop.getShopCode());
        //库存下限
        Integer quantityFloor = inventoryBiz.getSkuQuantityFloor(spec.getPdmGoodsCode(), null, shop.getShopCode());
        //实时库存
        Integer stock = inventoryMap.get(shop.getSite() + spec.getPdmGoodsCode());
        if (ObjectUtils.isEmpty(stock)) {
            List<ThirdpartyFbmDTO> thirdpartyFbmDTOList = inventoryBiz.selectStockShareAndPartGoodsCode(Collections.singletonList(spec.getPdmGoodsCode()), shop.getSite(), false);
            if (ObjectUtils.isEmpty(thirdpartyFbmDTOList)) {
                //不更新库存
                return Integer.valueOf(spec.getStock());
            }
            inventoryMap.put(shop.getSite() + spec.getPdmGoodsCode(), thirdpartyFbmDTOList.get(0).getSellableQty());
            stock = thirdpartyFbmDTOList.get(0).getSellableQty();
        }
        //实际库存大于等于库存上限 直接更新为库存上限值
        if (ObjectUtils.isNotEmpty(shopQuantity) && stock >= shopQuantity) {
            return shopQuantity;
        }
        //实时库存小于等于库存下限 直接更新为0
        if (ObjectUtils.isNotEmpty(quantityFloor) && stock <= quantityFloor) {
            return 0;
        }
        return stock;
    }
}

