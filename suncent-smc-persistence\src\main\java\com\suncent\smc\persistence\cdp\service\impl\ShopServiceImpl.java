package com.suncent.smc.persistence.cdp.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.annotation.DataSource;
import com.suncent.smc.common.core.redis.RedisService;
import com.suncent.smc.common.domain.KeyValueEntity;
import com.suncent.smc.common.enums.DataSourceType;
import com.suncent.smc.common.enums.PlatformSiteEnum;
import com.suncent.smc.common.utils.ShiroUtils;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.cdp.domain.dto.ShopDTO;
import com.suncent.smc.persistence.cdp.domain.dto.ShopSiteNameDto;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.mapper.ShopMapper;
import com.suncent.smc.persistence.cdp.service.IShopService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 公共数据-店铺Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-04
 */
@Service("shopService")
@DataSource(DataSourceType.PDM)
@Slf4j
public class ShopServiceImpl implements IShopService {
    @Autowired
    private ShopMapper shopMapper;
    @Value("${oms.get_shopCode_url}")
    private String getShopCodeUrl;

    @Value("${oms.get_user_url}")
    private String getUserUrl;
    @Resource
    RedisService redisService;

    /**
     * 获取全部的店铺
     *
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<KeyValueEntity> selectShopAllList() {
        List<ShopSiteNameDto> shopList = shopMapper.getShopSiteNameList();

        //通过userId获取用户的店铺权限
        List<ShopSiteNameDto> shopCodeList = getShopSiteNameDtos(shopList);
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return new ArrayList<>();
        }
        return keyValueListByShopSiteNameDto(shopCodeList);
    }

    private List<ShopSiteNameDto> getShopSiteNameDtos(List<ShopSiteNameDto> shopList) {
        //通过userId获取用户的店铺权限
        Long userId = ShiroUtils.getUserId();
        List<String> shopCode = getShopCodeByUserId(userId);
        if (CollectionUtils.isEmpty(shopCode)) {
            return new ArrayList<>();
        }
        List<ShopSiteNameDto> shopCodeList = shopList.stream().filter(shop -> shopCode.contains(shop.getShopCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return new ArrayList<>();
        }
        List<String> nameList = PlatformSiteEnum.getNameList();
        shopCodeList = shopCodeList.stream().filter(shop -> nameList.contains(shop.getSiteCode())).collect(Collectors.toList());

//        shopCodeList = shopCodeList.stream().filter(shop -> Objects.equals(shop.getSiteName(), "美国")).collect(Collectors.toList());
        return shopCodeList;
    }

    /**
     * 根据平台编码查询商铺
     *
     * @param platformCode
     * @return
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<KeyValueEntity> selectShopByPlatformCodeList(String platformCode) {
        List<ShopSiteNameDto> shopList = shopMapper.selectShopByPlatformCodeList(platformCode);
        //通过userId获取用户的店铺权限
        List<ShopSiteNameDto> shopCodeList = getShopSiteNameDtos(shopList);
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return new ArrayList<>();
        }

        return keyValueListByShopSiteNameDto(shopCodeList);
    }


    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<ShopSiteNameDto> selectShopByPlatformCodeBySiteCode(String siteCode, String platformCode) {
        List<ShopSiteNameDto> shopCodeList = shopMapper.selectShopByPlatformCodeBySiteCode(siteCode,platformCode);
        //通过userId获取用户的店铺权限
        List<ShopSiteNameDto> shopCodeResult = getShopSiteNameDtos(shopCodeList);
        if (CollectionUtils.isEmpty(shopCodeResult)) {
            return new ArrayList<>();
        }
        return shopCodeResult;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<ShopSiteNameDto> selectShopByPlatformCode(String platformCode) {
        List<ShopSiteNameDto> shopList = shopMapper.selectShopByPlatformCodeList(platformCode);
        return shopList;
    }

    /**
     * 获取全部的店铺数据
     *
     * @return 全部的店铺数据
     */
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<KeyValueEntity> listShop() {
        List<ShopDTO> shopList = shopMapper.listShop();
        return keyValueListByShopDto(shopList);
    }

    private List<KeyValueEntity> keyValueListByShopSiteNameDto(List<ShopSiteNameDto> shopList) {
        if (CollectionUtils.isEmpty(shopList)) {
            return new ArrayList<>();
        }
        return shopList.stream().map(shop -> {
            KeyValueEntity keyValueEntity = new KeyValueEntity();
            String shopName = StringUtils.isEmpty(shop.getShopName()) ? "" : shop.getShopName();
//            String siteName = StringUtils.isEmpty(shop.getSiteName()) ? "" : shop.getSiteName();
            String entityValue = shopName + " [" + shop.getShopCode() + "]";
            keyValueEntity.setKey(shop.getShopCode());
            keyValueEntity.setValue(entityValue);
            return keyValueEntity;
        }).collect(Collectors.toList());
    }

    private List<KeyValueEntity> keyValueListByShopDto(List<ShopDTO> shopList) {
        if (CollectionUtils.isEmpty(shopList)) {
            return new ArrayList<>();
        }

        return shopList.stream().map(shop -> {
            KeyValueEntity keyValueEntity = new KeyValueEntity();
            String shopCode = StringUtils.isEmpty(shop.getShopCode()) ? "" : shop.getShopCode();
            String shopName = StringUtils.isEmpty(shop.getShopName()) ? "" : shop.getShopName();
            keyValueEntity.setKey(shopCode);
            keyValueEntity.setValue(shopName);
            return keyValueEntity;
        }).collect(Collectors.toList());
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public Shop selectShopByShopCode(String shopCode) {
        return shopMapper.selectShopByShopCode(shopCode);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String selectShopNameByShopCode(String shopCode) {
        return shopMapper.selectShopNameByShopCode(shopCode);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public String selectSiteCodeByShopCode(String shopCode) {
        return shopMapper.selectSiteCodeByShopCode(shopCode);
    }

    @Override
    public List<String> getShopCodeByUserId(Long userId) {
        if (ObjectUtils.isEmpty(userId)){
            return new ArrayList<>();
        }
        String key="SMC:USEROMS:SHOPCODE:"+userId;
        try {
            if (redisService.exists(key)) {
                Object cacheObjectStr = redisService.getCacheObject(key);
                if (ObjectUtils.isNotEmpty(cacheObjectStr)){
                    List<String> shopCodes = JSON.parseArray(String.valueOf(cacheObjectStr), String.class);
                    return shopCodes;
                }
            }
            String url=getShopCodeUrl+userId;
            String resultStr = HttpUtils.get(url);
            JSONObject jsonObject = JSONObject.parseObject(resultStr);
            if (ObjectUtils.isNotEmpty(jsonObject)){
                Integer code = jsonObject.getInteger("code");
                if (code==0){
                    JSONArray data = jsonObject.getJSONArray("data");
                    if (ObjectUtils.isNotEmpty(data)){
                        List<String> shopCodes = data.stream().map(p -> {
                            JSONObject jsonObject1 = (JSONObject) p;
                            String shopCode = jsonObject1.getString("shopCode");
                            return shopCode;
                        }).collect(Collectors.toList());
                        redisService.setCacheObject(key,JSON.toJSONString(shopCodes),10L, TimeUnit.MINUTES);
                        return shopCodes;
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取店铺code失败",e);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<Shop> selectShopByShopCodes(String[] shopCodes) {
        return shopMapper.selectShopByShopCodes(shopCodes);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<Shop> selectShopListByPlatformCode(String platformCode) {
        return shopMapper.selectShopListByPlatformCode(platformCode);
    }
    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<Shop> selectShopListByPlatform(String platformCode) {
        return shopMapper.selectShopListByPlatform(platformCode);
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<Shop> selectShopList() {
        return shopMapper.selectShopList();
    }

    @Override
    public List<String> selectUserByShopCode(String shopCode) {
        if (ObjectUtils.isEmpty(shopCode)){
            return new ArrayList<>();
        }
        try {
            String url=getUserUrl+shopCode;
            String resultStr = HttpUtils.get(url);
            JSONObject jsonObject = JSONObject.parseObject(resultStr);
            if (ObjectUtils.isNotEmpty(jsonObject)){
                Integer code = jsonObject.getInteger("code");
                if (code==0){
                    JSONArray data = jsonObject.getJSONArray("data");
                    if (ObjectUtils.isNotEmpty(data)){
                        return  JSONArray.parseArray(data.toJSONString(), String.class);
                    }
                }
            }
        } catch (Exception e) {
            log.warn("获取用户失败",e);
        }
        return new ArrayList<>();
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRES_NEW)
    public List<KeyValueEntity> getVCShopAllKVList() {
        List<ShopSiteNameDto> shopList = shopMapper.getVCShopAllKVList();

        //通过userId获取用户的店铺权限
        List<ShopSiteNameDto> shopCodeList = getShopSiteNameDtos(shopList);
        if (CollectionUtils.isEmpty(shopCodeList)) {
            return new ArrayList<>();
        }
        return keyValueListByShopSiteNameDto(shopCodeList);
    }


}
