package com.suncent.smc.provider.biz.publication;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.exception.BusinessException;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationRequestDTO;
import com.suncent.smc.provider.biz.publication.dto.EbayTitleGenerationResponseDTO;
import com.suncent.smc.provider.biz.publication.dto.EbayDescriptionGenerationRequestDTO;
import com.suncent.smc.provider.biz.publication.dto.EbayDescriptionGenerationResponseDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Ebay商品内容生成API HTTP请求业务类
 * 用于处理与Ebay标题和描述生成API的HTTP通信
 *
 * <AUTHOR>
 * @date 2025-06-20
 */
@Component
@Slf4j
public class EbayContentGenerationHttpRequestBiz {

    /**
     * Ebay内容生成API基础URL（标题和描述共用）
     */
    @Value("${api.ebay-content-generation-url:https://u242675-a03f-da8b7148.westc.gpuhub.com:8443}")
    private String apiBaseUrl;

    /**
     * API端点
     */
    private static final String ENDPOINT_GEN_TITLES = "/gen_titles/";
    private static final String ENDPOINT_GEN_PD = "/gen_pd/";

    /**
     * 授权Token（标题和描述共用）
     */
    @Value("${api.ebay-content-generation-token:123789}")
    private String authToken;

    /**
     * 请求超时时间（毫秒）
     */
    private static final int REQUEST_TIMEOUT = 120000; // 120秒

    /**
     * 生成Ebay商品标题
     *
     * @param requestDTO 请求参数
     * @return 标题生成结果
     */
    public AjaxResult generateTitles(EbayTitleGenerationRequestDTO requestDTO) {
        if (ObjUtil.isEmpty(requestDTO)) {
            return AjaxResult.error("请求参数不能为空");
        }

        if (ObjUtil.isEmpty(requestDTO.getGoods()) || requestDTO.getGoods().isEmpty()) {
            return AjaxResult.error("商品列表不能为空");
        }

        try {
            String url = getFullApiUrl(ENDPOINT_GEN_TITLES);
            String requestBody = JSON.toJSONString(convertToTitleApiRequest(requestDTO));

            log.info("调用Ebay标题生成API，URL: {}, 请求参数: {}", url, requestBody);

            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + authToken)
                    .body(requestBody)
                    .timeout(REQUEST_TIMEOUT)
                    .execute();

            String responseBody = response.body();
            log.info("Ebay标题生成API响应: {}", responseBody);

            if (!response.isOk()) {
                log.error("Ebay标题生成API调用失败，状态码: {}, 响应: {}", response.getStatus(), responseBody);
                return handleErrorResponse(response.getStatus(), responseBody);
            }

            // 解析响应
            EbayTitleGenerationResponseDTO responseDTO = parseTitleApiResponse(responseBody);
            return AjaxResult.success(responseDTO);

        } catch (Exception e) {
            log.error("调用Ebay标题生成API异常", e);
            return AjaxResult.error("调用标题生成服务失败: " + e.getMessage());
        }
    }

    /**
     * 生成Ebay商品描述
     *
     * @param requestDTO 请求参数
     * @return 描述生成结果
     */
    public AjaxResult generateDescriptions(EbayDescriptionGenerationRequestDTO requestDTO) {
        if (ObjUtil.isEmpty(requestDTO)) {
            return AjaxResult.error("请求参数不能为空");
        }

        if (ObjUtil.isEmpty(requestDTO.getGoods()) || requestDTO.getGoods().isEmpty()) {
            return AjaxResult.error("商品列表不能为空");
        }
        if (EnvUtils.isDevProfile()) {
            return AjaxResult.success();
        }
        try {
            String url = getFullApiUrl(ENDPOINT_GEN_PD);
            Map<String, Object> apiRequest = convertToDescriptionApiRequest(requestDTO);
            String requestBody = JSON.toJSONString(apiRequest);

            log.info("调用Ebay描述生成API，URL: {}, 转换后的请求参数: {}", url, requestBody);

            HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json")
                    .header("Authorization", "Bearer " + authToken)
                    .body(requestBody)
                    .timeout(REQUEST_TIMEOUT)
                    .execute();

            String responseBody = response.body();
            log.info("Ebay描述生成API响应: {}", responseBody);

            if (!response.isOk()) {
                log.error("Ebay描述生成API调用失败，状态码: {}, 响应: {}", response.getStatus(), responseBody);
                return handleErrorResponse(response.getStatus(), responseBody);
            }

            // 解析响应
            EbayDescriptionGenerationResponseDTO responseDTO = parseDescriptionApiResponse(responseBody);
            return AjaxResult.success(responseDTO);

        } catch (Exception e) {
            log.error("调用Ebay描述生成API异常", e);
            return AjaxResult.error("调用描述生成服务失败: " + e.getMessage());
        }
    }

    /**
     * 转换为标题API请求格式，将驼峰命名转换为下划线风格
     */
    private Map<String, Object> convertToTitleApiRequest(EbayTitleGenerationRequestDTO requestDTO) {
        Map<String, Object> apiRequest = new HashMap<>();

        // 店铺参数保持不变
        apiRequest.put("shop", requestDTO.getShop());
        apiRequest.put("brand", requestDTO.getBrand());

        // 转换商品列表参数，将goodsCode转换为goods_code
        List<Map<String, Object>> goodsList = new ArrayList<>();
        if (requestDTO.getGoods() != null) {
            for (EbayTitleGenerationRequestDTO.GoodsItem item : requestDTO.getGoods()) {
                Map<String, Object> goodsItem = new HashMap<>();
                goodsItem.put("goods_code", item.getGoodsCode()); // goodsCode -> goods_code
                goodsItem.put("n", item.getN()); // n 保持不变
                goodsList.add(goodsItem);
            }
        }
        apiRequest.put("goods", goodsList);

        return apiRequest;
    }

    /**
     * 转换为描述API请求格式，将驼峰命名转换为下划线风格
     */
    private Map<String, Object> convertToDescriptionApiRequest(EbayDescriptionGenerationRequestDTO requestDTO) {
        Map<String, Object> apiRequest = new HashMap<>();

        // 店铺参数保持不变
        apiRequest.put("shop", requestDTO.getShop());
        apiRequest.put("brand", requestDTO.getBrand());

        // 转换商品列表参数，将goodsCode转换为goods_code，并包含属性数据
        List<Map<String, Object>> goodsList = new ArrayList<>();
        if (requestDTO.getGoods() != null) {
            for (EbayDescriptionGenerationRequestDTO.GoodsItem item : requestDTO.getGoods()) {
                Map<String, Object> goodsItem = new HashMap<>();
                goodsItem.put("goods_code", item.getGoodsCode()); // goodsCode -> goods_code

                // 如果有属性数据，则添加到商品项中
                if (item.getAttribute() != null && !item.getAttribute().isEmpty()) {
                    goodsItem.put("attribute", item.getAttribute());
                }

                goodsList.add(goodsItem);
            }
        }
        apiRequest.put("goods", goodsList);

        return apiRequest;
    }

    /**
     * 解析标题API响应
     */
    private EbayTitleGenerationResponseDTO parseTitleApiResponse(String responseBody) {
        try {
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            EbayTitleGenerationResponseDTO responseDTO = new EbayTitleGenerationResponseDTO();

            Map<String, EbayTitleGenerationResponseDTO.GoodsTitleInfo> dataMap = new HashMap<>();

            for (String goodsCode : jsonResponse.keySet()) {
                JSONObject goodsData = jsonResponse.getJSONObject(goodsCode);
                EbayTitleGenerationResponseDTO.GoodsTitleInfo titleInfo =
                    new EbayTitleGenerationResponseDTO.GoodsTitleInfo();

                titleInfo.setTitles(goodsData.getJSONArray("titles").toJavaList(String.class));
                titleInfo.setSellerDesc(goodsData.getString("seller_desc"));

                dataMap.put(goodsCode, titleInfo);
            }

            responseDTO.setData(dataMap);
            return responseDTO;

        } catch (Exception e) {
            log.error("解析标题API响应失败", e);
            throw new BusinessException("解析标题生成结果失败");
        }
    }

    /**
     * 解析描述API响应
     */
    private EbayDescriptionGenerationResponseDTO parseDescriptionApiResponse(String responseBody) {
        try {
            JSONObject jsonResponse = JSON.parseObject(responseBody);
            EbayDescriptionGenerationResponseDTO responseDTO = new EbayDescriptionGenerationResponseDTO();

            Map<String, EbayDescriptionGenerationResponseDTO.GoodsDescriptionInfo> dataMap = new HashMap<>();

            for (String goodsCode : jsonResponse.keySet()) {
                JSONObject goodsData = jsonResponse.getJSONObject(goodsCode);
                EbayDescriptionGenerationResponseDTO.GoodsDescriptionInfo descInfo =
                    new EbayDescriptionGenerationResponseDTO.GoodsDescriptionInfo();

                descInfo.setPdContent(goodsData.getString("pd_content"));

                dataMap.put(goodsCode, descInfo);
            }

            responseDTO.setData(dataMap);
            return responseDTO;

        } catch (Exception e) {
            log.error("解析描述API响应失败", e);
            throw new BusinessException("解析描述生成结果失败");
        }
    }

    /**
     * 处理错误响应
     */
    private AjaxResult handleErrorResponse(int statusCode, String responseBody) {
        try {
            if (StrUtil.isNotBlank(responseBody) && JSONUtil.isJson(responseBody)) {
                JSONObject errorJson = JSON.parseObject(responseBody);
                String detail = errorJson.getString("detail");
                
                switch (statusCode) {
                    case 401:
                        return AjaxResult.error("认证失败，请检查授权Token");
                    case 422:
                        return AjaxResult.error("请求参数格式错误: " + detail);
                    case 500:
                        return AjaxResult.error("服务器内部错误: " + detail);
                    default:
                        return AjaxResult.error("API调用失败: " + detail);
                }
            }
        } catch (Exception e) {
            log.warn("解析错误响应失败", e);
        }
        
        return AjaxResult.error("API调用失败，状态码: " + statusCode);
    }

    /**
     * 获取完整的API URL
     *
     * @param endpoint API端点路径
     * @return 完整的API URL
     */
    private String getFullApiUrl(String endpoint) {
        return apiBaseUrl + endpoint;
    }

    /**
     * 获取API基础URL
     */
    public String getApiBaseUrl() {
        return apiBaseUrl;
    }
}
