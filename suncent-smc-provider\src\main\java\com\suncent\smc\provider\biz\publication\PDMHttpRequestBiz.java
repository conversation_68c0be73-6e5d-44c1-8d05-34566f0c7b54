package com.suncent.smc.provider.biz.publication;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import com.google.common.collect.Lists;
import com.suncent.smc.common.core.domain.AjaxResult;
import com.suncent.smc.common.utils.StringUtils;
import com.suncent.smc.common.utils.http.HttpUtils;
import com.suncent.smc.persistence.ebay.domain.EbayGoodsHeadV2;
import com.suncent.smc.persistence.ebay.service.IEbayListingGoodsHeadV2Service;
import com.suncent.smc.persistence.pdm.domain.dto.CreateMappingDTO;
import com.suncent.smc.persistence.pdm.domain.dto.GetGoodsDetailQueryDTO;
import com.suncent.smc.persistence.pdm.domain.dto.GoodsPriceDTO;
import com.suncent.smc.persistence.pdm.domain.dto.PageQueryGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.dto.SaleGoodsDTO;
import com.suncent.smc.persistence.pdm.domain.dto.*;
import com.suncent.smc.persistence.pdm.domain.dto.goodsInfoDto.GoodsInfoDTO;
import com.suncent.smc.persistence.pdm.domain.entity.MappingGoods;
import com.suncent.smc.persistence.publication.domain.dto.GoodsDetailDTO;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.service.IGoodsHeadService;
import com.suncent.smc.persistence.temu.domain.entity.TemuGoodsHead;
import com.suncent.smc.persistence.temu.service.ITemuGoodsHeadService;
import com.suncent.smc.provider.biz.publication.domain.GoodsCategoryAttributeVO;
import com.suncent.smc.provider.biz.publication.domain.GoodsNewAttributeVO;
import com.suncent.smc.provider.biz.publication.dto.PriceCountResultDTO;
import lombok.extern.log4j.Log4j2;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: TODO
 * @date 2023/11/01 11:22
 */
@Slf4j
@Component
public class PDMHttpRequestBiz {

//    @Value("${pdm.getPriceByGoodsCode}")
//    private String getPriceByGoodsCode;
    @Value("${pdm.getRedLinePriceByGoodsCode}")
    private String getRedLinePriceByGoodsCode;
    @Value("${pdm.add_update_pdm_goods_mapping_v2}")
    private String ADD_UPDATE_PDM_GOODS_MAPPING_V2;
//    @Value("${pdm.pageQueryGoodsDetail}")
//    private String pageQueryGoodsDetail;
    @Value("${pdm.pageQueryGoodsDetailV2}")
    private String pageQueryGoodsDetailV2;
    @Value("${pdm.listPDMGoodsDetail}")
    private String listPDMGoodsDetail;
    @Value("${pdm.query_pdm_goods_mapping}")
    private String queryPdmGoodsMapping;
    @Value("${pdm.getDetail}")
    private String getDetail;
    @Value("${pdm.getAllParentPartList}")
    private String getAllParentPartList;
    @Value("${pdm.getAllMainGoodsCodes}")
    private String getAllMainGoodsCodes;
    @Value("${pdm.query_pdm_goods_mapping_list}")
    private String queryPdmGoodsMappingList;
    @Value("${pdm.queryGoodsAttribute}")
    private String queryGoodsAttribute;
    @Value("${pdm.queryGoodsCategoryAttribute}")
    private String queryGoodsCategoryAttribute;
    @Value("${pdm.queryGoodsListInfo}")
    private String queryGoodsListInfo;
    @Value("${pdm.execPythonUrl}")
    private String execPythonUrl;
    @Value("${pdm.updateGoodsBrandImage}")
    private String updateGoodsBrandImageUrl;
    @Value("${pdm.fix_pdm_goods_status}")
    private String FIX_PDM_GOODS_STATUS;

    @Autowired
    private IGoodsHeadService goodsHeadService;
    @Autowired
    private ITemuGoodsHeadService temuGoodsHeadService;
    @Autowired
    private IEbayListingGoodsHeadV2Service ebayListingGoodsHeadV2Service;

    /**
     * PDM
     * 单个商品价格查询
     * @param siteCode
     * @param sku
     * @return
     */
//    public GoodsPriceDTO queryGoodsPrice(String siteCode, String sku) {
//        try {
//            Map<String, Object> param = new HashMap<>();
//            param.put("site", siteCode);
//            param.put("skus", CollUtil.newArrayList(sku));
//            String result = HttpUtils.post(getPriceByGoodsCode, JSON.toJSONString(param));
//            if(ObjUtil.isEmpty(result)){
//                return null;
//            }
//            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
//            if(!ajaxResult.isSuccess()){
//                return null;
//            }
//            Object o = ajaxResult.get(AjaxResult.DATA_TAG);
//            if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
//                return null;
//            }
//            List<GoodsPriceDTO> goodsPriceDTOS = JSONObject.parseArray(String.valueOf(o), GoodsPriceDTO.class);
//            if (CollUtil.isEmpty(goodsPriceDTOS)) {
//                return null;
//            }
//            return goodsPriceDTOS.get(0);
//        } catch (Exception e) {
//            log.error("sku:{},查询商品价格异常:{}",sku,e.getMessage());
//            return null;
//        }
//
//    }


    /**
     * 单个获取红线价格
     * @param sku
     * @return
     */
    public GoodsRedPriceDTO getRedLinePriceByGoodsCode(String siteCode,String sku) {
        if (StrUtil.isEmpty(sku)){
            return null;
        }
        List<GoodsRedPriceDTO> redLinePriceByGoodsCode = getRedLinePriceByGoodsCode(siteCode, CollUtil.newArrayList(sku));
        if (CollUtil.isEmpty(redLinePriceByGoodsCode)) {
            return null;
        }
        return redLinePriceByGoodsCode.get(0);
    }


    /**
     * 批量获取红线价格
     * @param skusLists
     * @return
     */
    public List<GoodsRedPriceDTO> getRedLinePriceByGoodsCode(String siteCode,List<String> skusLists) {
        List<GoodsRedPriceDTO> returnList = new ArrayList<>();
        if (CollUtil.isEmpty(skusLists)) {
            return returnList;
        }
        try {
            List<List<String>> skusListsPart = Lists.partition(skusLists, 200);
            for (List<String> skuss : skusListsPart) {
                Map<String, Object> param = new HashMap<>();
//              param.put("site", siteCode);
                param.put("skus", skuss);
                String result = HttpUtils.post(getRedLinePriceByGoodsCode, JSON.toJSONString(param));
                if(ObjUtil.isEmpty(result)){
                    continue;
                }
                AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
                if(!ajaxResult.isSuccess()){
                    continue;
                }
                Object o = ajaxResult.get(AjaxResult.DATA_TAG);
                if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
                    continue;
                }
                List<GoodsRedPriceDTO> goodsPriceDTOS = JSONObject.parseArray(String.valueOf(o), GoodsRedPriceDTO.class);
                if (CollUtil.isEmpty(goodsPriceDTOS)) {
                    continue;
                }
                returnList.addAll(goodsPriceDTOS);
            }
            return returnList;
        } catch (Exception e) {
            log.error("sku:{},查询商品价格异常:{}",skusLists,e.getMessage());
            return returnList;
        }

    }

    /**
     * PDM
     * 批量商品价格查询
     * @param siteCode
     * @param skusLists
     * @return
     */
//    public List<GoodsPriceDTO> queryGoodsPrice(String siteCode, List<String> skusLists) {
//        List<GoodsPriceDTO> goodsPriceDTOS=new ArrayList<>();
//        try {
//            if (CollUtil.isEmpty(skusLists)) {
//                return goodsPriceDTOS;
//            }
//            skusLists = CollUtil.distinct(skusLists);
//            //sku 切片
//            List<List<String>> skusList = Lists.partition(skusLists, 200);
//            for (List<String> skus : skusList) {
//                Map<String, Object> param = new HashMap<>();
//                param.put("site", siteCode);
//                param.put("skus", skus);
//                String result = HttpUtils.post(getPriceByGoodsCode, JSON.toJSONString(param));
//                if(ObjUtil.isEmpty(result)){
//                    continue;
//                }
//                AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
//                if(!ajaxResult.isSuccess()){
//                    continue;
//                }
//                Object o = ajaxResult.get(AjaxResult.DATA_TAG);
//                if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
//                    continue;
//                }
//                List<GoodsPriceDTO> goodsPriceResultDTOS = JSONObject.parseArray(String.valueOf(o), GoodsPriceDTO.class);
//                goodsPriceDTOS.addAll( goodsPriceResultDTOS );
//            }
//        } catch (Exception e) {
//            log.error("skus:{},查询商品价格异常:{}",skusLists,e.getMessage());
//        }
//        return goodsPriceDTOS;
//    }

    /**
     * PDM
     * 查询PDM映射
     * @param goodsHead
     * @return
     */
    public  List<MappingGoods> queryPdmMapping(GoodsHead goodsHead){
        try {
            Map<String,String> param=new HashMap<>();
            param.put("platformSku",goodsHead.getPlatformGoodsCode());
            param.put("shopCode",goodsHead.getShopCode());
            String result = HttpUtils.post(queryPdmGoodsMapping,JSON.toJSONString(param));
            if(ObjUtil.isEmpty(result)){
                log.error("查询PDM状态映射失败:{}",JSON.toJSONString(result));
                 return null;
            }
            AjaxResult ajaxResult = JSON.parseObject(result, AjaxResult.class);
            if(ajaxResult.isError()){
                log.error("查询PDM状态映射失败:{}",JSON.toJSONString(result));
                return null;
            }
            return  JSON.parseArray(ajaxResult.get(AjaxResult.DATA_TAG).toString(), MappingGoods.class);
        } catch (Exception e) {
            log.error("查询PDM状态映射异常:{}",e);
            return null;
        }

    }

    /**
     * PDM
     * 查询PDM映射集合
     *
     * @param shopCode
     * @param platformSkuList
     * @return
     */
    public List<MappingGoods> queryPdmMappingList(String shopCode, List<String> platformSkuList) {
        Map<String, Object> param = new HashMap<>();
        param.put("platformSkuList", platformSkuList);
        param.put("shopCode", shopCode);
        String result = HttpUtils.post(queryPdmGoodsMappingList, JSON.toJSONString(param));
        if (ObjUtil.isEmpty(result)) {
            log.error("查询PDM状态映射集合失败:{}", JSON.toJSONString(result));
            return null;
        }
        AjaxResult ajaxResult = JSON.parseObject(result, AjaxResult.class);
        if (ajaxResult.isError()) {
            log.error("查询PDM状态映射集合失败:{}", JSON.toJSONString(result));
            return null;
        }
        return JSON.parseArray(ajaxResult.get(AjaxResult.DATA_TAG).toString(), MappingGoods.class);

    }

    /**
     * PDM
     * 创建PDM映射
     * @param createMappingDTO
     * @param goodsHead
     */
    public void addPdmMapping(CreateMappingDTO createMappingDTO, GoodsHead goodsHead) {
        log.debug("准备添加PDM状态映射:{}", JSON.toJSONString(createMappingDTO));
        String result = HttpUtils.post(ADD_UPDATE_PDM_GOODS_MAPPING_V2, JSON.toJSONString(createMappingDTO));
        if(ObjUtil.isEmpty(result)){
            log.error("添加PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(ajaxResult.isError()){
            log.error("添加PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }

        Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);
        //为10000为编码重复 也为已映射
        if ("10000".equals(String.valueOf(code))){
            updatePdmMapping(createMappingDTO, goodsHead);
            log.debug("完成更新PDM状态映射:{}",JSON.toJSONString(result));
        }else {
            GoodsHead updateHead = new GoodsHead();
            updateHead.setId(goodsHead.getId());
            updateHead.setPdmStatus(1);
            goodsHeadService.updateListingGoodsHead(updateHead);
            log.debug("完成添加PDM状态映射:{}",JSON.toJSONString(result));
        }
    }

    /**
     * temu 添加pdm映射
     * @param createMappingDTO
     * @param goodsHead
     */
    public void addTemuPdmMapping(CreateMappingDTO createMappingDTO, TemuGoodsHead goodsHead) {
        log.debug("准备添加PDM状态映射:{}", JSON.toJSONString(createMappingDTO));
        String result = HttpUtils.post(ADD_UPDATE_PDM_GOODS_MAPPING_V2, JSON.toJSONString(createMappingDTO));
        if(ObjUtil.isEmpty(result)){
            log.error("添加PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(ajaxResult.isError()){
            log.error("添加PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }

        Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);
        //为10000为编码重复 也为已映射
        if ("10000".equals(String.valueOf(code))){
            updateTemuPdmMapping(createMappingDTO, goodsHead);
            log.debug("完成更新PDM状态映射:{}",JSON.toJSONString(result));
        }else {
            TemuGoodsHead update = new TemuGoodsHead();
            update.setId(goodsHead.getId());
            update.setPdmStatus(1L);
            temuGoodsHeadService.updateTemuGoodsHead(update);
            log.debug("完成添加PDM状态映射:{}",JSON.toJSONString(result));
        }
    }


    /**
     * PDM
     * 创建PDM映射
     * @param createMappingDTO
     * @param goodsHead
     */
    public void addEbayPdmMapping(CreateMappingDTO createMappingDTO, EbayGoodsHeadV2 goodsHead) {
        log.debug("准备添加PDM状态映射:{}", JSON.toJSONString(createMappingDTO));
        String result = HttpUtils.post(ADD_UPDATE_PDM_GOODS_MAPPING_V2, JSON.toJSONString(createMappingDTO));
        if(ObjUtil.isEmpty(result)){
            log.error("添加PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }
        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(ajaxResult.isError()){
            log.error("添加PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }

        Integer code = (Integer) ajaxResult.get(AjaxResult.CODE_TAG);
        //为10000为编码重复 也为已映射
        if ("10000".equals(String.valueOf(code))){
            updateEbayPdmMapping(createMappingDTO, goodsHead);
            log.debug("完成更新PDM状态映射:{}",JSON.toJSONString(result));
        }else {
            EbayGoodsHeadV2 updateHead = new EbayGoodsHeadV2();
            updateHead.setId(goodsHead.getId());
            updateHead.setPdmStatus(1);
            ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateHead);
            log.debug("完成添加PDM状态映射:{}",JSON.toJSONString(result));
        }
    }


    public void updateEbayPdmMapping(CreateMappingDTO createMappingDTO, EbayGoodsHeadV2 goodsHead) {
        String result =  HttpRequest.put(ADD_UPDATE_PDM_GOODS_MAPPING_V2)
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(createMappingDTO))
                .execute().body();

        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("更新PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }
        EbayGoodsHeadV2 updateHead = new EbayGoodsHeadV2();
        updateHead.setId(goodsHead.getId());
        updateHead.setPdmStatus(1);
        ebayListingGoodsHeadV2Service.updateEbayListingGoodsHeadV2(updateHead);
    }


    /**
     * PDM
     * 更新PDM映射
     * @param createMappingDTO
     * @param goodsHead
     */
    public void updateTemuPdmMapping(CreateMappingDTO createMappingDTO, TemuGoodsHead goodsHead) {
        String result =  HttpRequest.put(ADD_UPDATE_PDM_GOODS_MAPPING_V2)
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(createMappingDTO))
                .execute().body();

        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("更新PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }

        TemuGoodsHead update = new TemuGoodsHead();
        update.setId(goodsHead.getId());
        update.setPdmStatus(1L);
        temuGoodsHeadService.updateTemuGoodsHead(update);
    }


    /**
     * PDM
     * 更新PDM映射
     * @param createMappingDTO
     * @param goodsHead
     */
    public void updatePdmMapping(CreateMappingDTO createMappingDTO, GoodsHead goodsHead) {
        String result =  HttpRequest.put(ADD_UPDATE_PDM_GOODS_MAPPING_V2)
                .header("Content-Type", "application/json")
                .body(JSON.toJSONString(createMappingDTO))
                .execute().body();

        AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("更新PDM状态映射失败:{}",JSON.toJSONString(result));
            return;
        }
        GoodsHead updateHead = new GoodsHead();
        updateHead.setId(goodsHead.getId());
        updateHead.setPdmStatus(1);
        goodsHeadService.updateListingGoodsHead(updateHead);
    }


    /**
     * PDM
     * 单个商品价格查询
     * @return
     */
//    public PageInfo<SaleGoodsDTO> pageQueryGoodsDetail(PageQueryGoodsDTO queryGoodsDTO) {
//        try {
//            String result = HttpUtils.post(pageQueryGoodsDetail, JSON.toJSONString(queryGoodsDTO));
//            if(ObjUtil.isEmpty(result)){
//                log.error("查询商品列表失败param:{},result:{}",JSON.toJSONString(queryGoodsDTO),JSON.toJSONString(result));
//                return null;
//            }
//            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
//            if(!ajaxResult.isSuccess()){
//                throw new RuntimeException("查询商品列表失败"+ajaxResult.get(AjaxResult.MSG_TAG));
//            }
//            Object o = ajaxResult.get(AjaxResult.DATA_TAG);
//            if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
//                log.error("查询商品列表失败param:{},result:{}",JSON.toJSONString(queryGoodsDTO),JSON.toJSONString(result));
//                return null;
//            }
//            PageInfo pageInfo = JSONObject.parseObject(String.valueOf(o), PageInfo.class);
//            return pageInfo;
//        } catch (Exception e) {
//            log.error("queryGoodsDTO:{},查询商品价格异常:{}",JSON.toJSONString(queryGoodsDTO),e.getMessage());
//            return null;
//        }
//
//    }

    /**
     * PDM
     * 单个商品价格查询
     * @return
     */
    public PageInfo<SaleGoodsDTO> pageQueryGoodsDetailV2(PageQueryGoodsDTO queryGoodsDTO) {
        try {
            String result = HttpUtils.post(pageQueryGoodsDetailV2, JSON.toJSONString(queryGoodsDTO));
            if(ObjUtil.isEmpty(result)){
                log.error("查询商品列表失败param:{},result:{}",JSON.toJSONString(queryGoodsDTO),JSON.toJSONString(result));
                return null;
            }
            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
            if(!ajaxResult.isSuccess()){
                throw new RuntimeException("查询商品列表失败"+ajaxResult.get(AjaxResult.MSG_TAG));
            }
            Object o = ajaxResult.get(AjaxResult.DATA_TAG);
            if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
                log.error("查询商品列表失败param:{},result:{}",JSON.toJSONString(queryGoodsDTO),JSON.toJSONString(result));
                return null;
            }
            PageInfo pageInfo = JSONObject.parseObject(String.valueOf(o), PageInfo.class);
            if(ObjUtil.isNotEmpty(pageInfo) && CollUtil.isNotEmpty(pageInfo.getList())){
                List<SaleGoodsDTO> saleGoodsDTOS = JSON.parseArray(JSON.toJSONString(pageInfo.getList()), SaleGoodsDTO.class);
                for (SaleGoodsDTO saleGoodsDTO : saleGoodsDTOS) {
                    GoodsRedPriceDTO goodsRedPriceDTO = new GoodsRedPriceDTO();
                    goodsRedPriceDTO.setSku(saleGoodsDTO.getGoodsCode());
                    goodsRedPriceDTO.setGoodsPrices(saleGoodsDTO.getGoodsPrices());
                    saleGoodsDTO.setGoodsPriceInfo(goodsRedPriceDTO);

                    GoodsRedPriceDTO goodsPriceInfo = saleGoodsDTO.getGoodsPriceInfo();
                    if (ObjUtil.isNotEmpty(goodsPriceInfo)){
                        saleGoodsDTO.setEbayRedLinePrice(goodsPriceInfo.getEbayRedLinePrice()+"");
                        saleGoodsDTO.setFbaRedLinePrice(goodsPriceInfo.getFbaRedLinePrice()+"");
                        saleGoodsDTO.setFbmRedLinePrice(goodsPriceInfo.getFbmRedLinePrice()+"");
                        saleGoodsDTO.setVcRedLinePrice(goodsPriceInfo.getVcRedLinePrice()+"");
                        saleGoodsDTO.setTemuRedLinePrice(goodsPriceInfo.getTemuRedLinePrice()+"");
                    }
                }
                pageInfo.setList(saleGoodsDTOS);
            }

            return pageInfo;
        } catch (Exception e) {
            log.error("queryGoodsDTO:{},查询商品价格异常:{}",JSON.toJSONString(queryGoodsDTO),e.getMessage());
            return null;
        }

    }


    public List<SaleGoodsDTO> listGoodsDetail(GetGoodsDetailQueryDTO queryGoodsDetailDTO) {
        List<SaleGoodsDTO> returnList = new ArrayList<>();
        try {
            //model 一定会传值 其他参数为空则返回空
            if (ObjUtil.isEmpty(queryGoodsDetailDTO.getMainGoodsCode())
                    &&ObjUtil.isEmpty(queryGoodsDetailDTO.getProductCodes())
                    &&ObjUtil.isEmpty(queryGoodsDetailDTO.getGoodsIdList())
                    &&ObjUtil.isEmpty(queryGoodsDetailDTO.getGoodsCodes())
                    &&ObjUtil.isEmpty(queryGoodsDetailDTO.getProductCategoryCode())
                    &&ObjUtil.isEmpty(queryGoodsDetailDTO.getBrandCode())) {
                return returnList;
            }
            List<String> models = queryGoodsDetailDTO.getModels();
            if (CollUtil.isEmpty(models)) {
                queryGoodsDetailDTO.setModels(Lists.newArrayList("GoodsImage","GoodsPrice","GoodsFactoryNo","GoodsSpecifications","PartList","GoodsMainList"));
            }
            String result = HttpUtils.post(listPDMGoodsDetail, JSON.toJSONString(queryGoodsDetailDTO));
            if(ObjUtil.isEmpty(result)){
                return returnList;
            }
            AjaxResult ajaxResult = JSONObject.parseObject(result, AjaxResult.class);
            if(!ajaxResult.isSuccess()){
                throw new RuntimeException("查询商品详情失败"+ajaxResult.get(AjaxResult.MSG_TAG));
            }
            Object o = ajaxResult.get(AjaxResult.DATA_TAG);
            if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
                return returnList;
            }
            List<SaleGoodsDTO> saleGoodsDTOS = JSONObject.parseArray(String.valueOf(o), SaleGoodsDTO.class);
            returnList.addAll(saleGoodsDTOS);

            postNext(returnList, ajaxResult.get("nextToken"));
            return returnList;
        } catch (Exception e) {
            log.error("queryGoodsDTO:{},查询商品价格异常:{}",JSON.toJSONString(queryGoodsDetailDTO),e.getMessage());
            return returnList;
        }
    }

    private boolean postNext(List<SaleGoodsDTO> returnList, Object nextToken) {
        if (ObjUtil.isEmpty(nextToken)){
            return true;
        }
        GetGoodsDetailQueryDTO nextDTO=new GetGoodsDetailQueryDTO();
        nextDTO.setNextToken(String.valueOf(nextToken));
        String post = HttpUtils.post(listPDMGoodsDetail, JSON.toJSONString(nextDTO));
        AjaxResult ajaxResult1 = JSONObject.parseObject(post, AjaxResult.class);
        if(!ajaxResult1.isSuccess()){
            throw new RuntimeException("查询商品详情失败"+ajaxResult1.get(AjaxResult.MSG_TAG));
        }
        Object o1 = ajaxResult1.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o1) || Objects.equals(o1,"null")) {
            return true;
        }
        List<SaleGoodsDTO> saleGoodsDTOS = JSONObject.parseArray(String.valueOf(o1), SaleGoodsDTO.class);
        returnList.addAll(saleGoodsDTOS);

        postNext(returnList, ajaxResult1.get("nextToken"));
        return true;
    }

    public GoodsDetailDTO getGoodsDetail(String goodCode) {
        if (ObjUtil.isEmpty(goodCode)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("goodsCode", goodCode);
        String resp = HttpUtils.get(getDetail, param);
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("查询pdm详情失败{}",JSON.toJSONString(resp));
            return null;
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            log.error("查询pdm详情失败sku:{},result:{}",goodCode,JSON.toJSONString(resp));
            return null;
        }
        GoodsDetailDTO dto = JSON.parseObject(String.valueOf(o), GoodsDetailDTO.class);
        return dto;
    }

    public List<GoodsDetailDTO> getGoodsDetail(List<String> goodCodes) {
        if (ObjUtil.isEmpty(goodCodes)) {
            return null;
        }
        List<GoodsDetailDTO> goodsDetailDTOS = new ArrayList<>();
        for (String goodCode : goodCodes) {
            Map<String, Object> param = new HashMap<>();
            param.put("goodsCode", goodCode);
            String resp = HttpUtils.get(getDetail, param);
            AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
            if(!ajaxResult.isSuccess()){
                log.error("查询pdm详情失败{}",JSON.toJSONString(resp));
                continue;
            }
            Object o = ajaxResult.get(AjaxResult.DATA_TAG);
            if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
                log.error("查询pdm详情失败sku:{},result:{}",goodCode,JSON.toJSONString(resp));
                continue;
            }
            GoodsDetailDTO dto = JSON.parseObject(String.valueOf(o), GoodsDetailDTO.class);
            goodsDetailDTOS.add(dto);
        }
        return goodsDetailDTOS;
    }
    public List<String> getAllParentPartList(){
        String resp = HttpUtils.get(getAllParentPartList);
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);

        if(!ajaxResult.isSuccess()){
            log.error("获取所有父级商品失败{}",JSON.toJSONString(resp));
            return null;
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            log.error("获取所有父级商品失败,result:{}",JSON.toJSONString(resp));
            return null;
        }
        return JSON.parseArray(String.valueOf(o), String.class);
    }

    public List<String> getAllMainGoodsCodes(){
        String resp = HttpUtils.get(getAllMainGoodsCodes);
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);

        if(!ajaxResult.isSuccess()){
            log.error("获取所有主商品编码失败{}",JSON.toJSONString(resp));
            return null;
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            log.error("获取所有主商品编码失败,result:{}",JSON.toJSONString(resp));
            return null;
        }
        return JSON.parseArray(String.valueOf(o), String.class);
    }

    public List<GoodsNewAttributeVO> queryGoodsAttribute(List<String> goodsCodes) {
        Map<String, Object> param = new HashMap<>();
        param.put("skus", goodsCodes);
        String resp = HttpUtils.post(queryGoodsAttribute, JSON.toJSONString(param));
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("查询pdm商品属性失败{}",JSON.toJSONString(resp));
            return null;
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            log.error("查询pdm详情失败sku:{},result:{}", goodsCodes, JSON.toJSONString(resp));
            return null;
        }
        List<GoodsNewAttributeVO> dtos = JSON.parseArray(String.valueOf(o), GoodsNewAttributeVO.class);
        return dtos;
    }

    public List<GoodsCategoryAttributeVO> queryGoodsCategoryAttribute(String productCate) {
        Map<String, Object> param = new HashMap<>();
        param.put("categoryCodeList", Collections.singleton(productCate));

        String resp = HttpUtils.post(queryGoodsCategoryAttribute, JSON.toJSONString(param));
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("查询pdm商品属性失败{}",JSON.toJSONString(resp));
            return null;
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            return null;
        }
        List<GoodsCategoryAttributeVO> dtos = JSON.parseArray(String.valueOf(o), GoodsCategoryAttributeVO.class);
        return dtos;
    }

    public List<GoodsInfoDTO> queryGoodsListInfo(String goodsCode) {
        Map<String, Object> param = new HashMap<>();
        param.put("skus", Collections.singleton(goodsCode));
        String resp = HttpUtils.post(queryGoodsListInfo, JSON.toJSONString(param));
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            log.error("查询pdm商品关联信息失败{}",JSON.toJSONString(resp));
            return null;
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            return null;
        }
        return  JSON.parseArray(String.valueOf(o), GoodsInfoDTO.class);
    }


    /**
     * 执行python脚本
     * @param pythonPath
     * @param PythonParams
     * @return
     */
    public List<PriceCountResultDTO> execPython(String pythonPath,List<String> PythonParams) {
        Map<String, Object> param = new HashMap<>();
        param.put("pythonPath", pythonPath);
        param.put("params" ,PythonParams);
        String resp = HttpUtils.post(execPythonUrl, JSON.toJSONString(param));
        AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
        if(!ajaxResult.isSuccess()){
            throw new RuntimeException("python脚本执行错误:"+ajaxResult.get(AjaxResult.MSG_TAG));
        }
        Object o = ajaxResult.get(AjaxResult.DATA_TAG);
        if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
            throw new RuntimeException("python脚本执行错误:"+ajaxResult.get(AjaxResult.MSG_TAG));
        }
        return   JSON.parseArray(o.toString(), PriceCountResultDTO.class);
    }


    /**
     * 适配变动，同步至PDM
     * @param pdmGoodsCode
     */
    public void updateGoodsBrandImage(List<String> pdmGoodsCode) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("skus", pdmGoodsCode);
            String resp = HttpUtils.post(updateGoodsBrandImageUrl, JSON.toJSONString(param));
            AjaxResult ajaxResult = JSON.parseObject(resp, AjaxResult.class);
            if(!ajaxResult.isSuccess()){
                log.error("适配变动，同步至PDM{}",JSON.toJSONString(resp));
                return;
            }
            Object o = ajaxResult.get(AjaxResult.DATA_TAG);
            if (ObjectUtils.isEmpty(o) || Objects.equals(o,"null")) {
                return;
            }
        } catch (Exception e) {
            log.error("适配变动，同步至PDM失败",e);
        }
    }



    public void fixedStatus(GoodsHead goodsHead) {
        if (StringUtils.isBlank(goodsHead.getPlatformGoodsCode()) || StringUtils.isBlank(goodsHead.getShopCode())) {
            return;
        }
        Map<String, Object> pdmMap = new HashMap<>();
        pdmMap.put("goodsCode", goodsHead.getPlatformGoodsCode());
        pdmMap.put("shopCode", goodsHead.getShopCode());
        log.info("操作Amazon商品下架完成,准备更新PDM状态映射:{}",JSON.toJSONString(pdmMap));
        String result = HttpUtils.post(FIX_PDM_GOODS_STATUS, pdmMap);
        log.info("操作Amazon商品下架完成,完成更新PDM状态映射:{}",JSON.toJSONString(result));
    }

}
