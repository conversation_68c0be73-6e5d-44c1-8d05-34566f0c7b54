package com.suncent.smc.quartz.task;

import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.publication.domain.entity.AmUpdateAvailabilityTask;
import com.suncent.smc.persistence.publication.domain.entity.ListingGoodsHeadDetail;
import com.suncent.smc.persistence.publication.service.IAmUpdateAvailabilityTaskService;
import com.suncent.smc.persistence.publication.service.IListingGoodsHeadDetailService;
import com.suncent.smc.persistence.publication.service.IListingLogService;
import com.xxl.job.core.handler.annotation.XxlJob;

import cn.hutool.core.util.StrUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * RPA更新VC链接可用状态同步任务
 * 
 * <AUTHOR>
 * @date 2023-07-15
 */
@Slf4j
@Component
public class RpaAvailabilityStatusSyncTask {

    @Autowired
    private IAmUpdateAvailabilityTaskService amUpdateAvailabilityTaskService;
    
    @Autowired
    private IListingGoodsHeadDetailService listingGoodsHeadDetailService;
    @Autowired
    protected IListingLogService listingLogService;

    /**
     * 同步RPA处理结果到商品可用状态详情
     */
    @XxlJob("rpaAvailabilityStatusSync")
    public void syncRpaTaskStatus() {
        log.info("开始同步RPA处理结果到商品可用状态详情...");
        
        try {
            // 查询已完成（成功或失败）的RPA任务，且对应的detail的rpa_status为0的数据
            List<AmUpdateAvailabilityTask> tasksNeedSync = amUpdateAvailabilityTaskService.selectTasksNeedSyncToDetail();
            
            if (tasksNeedSync == null || tasksNeedSync.isEmpty()) {
                log.info("没有需要同步的RPA任务，任务结束。");
                return;
            }
            
            log.info("找到 {} 个需要同步的RPA任务。", tasksNeedSync.size());
            
            // 按头表ID分组
            List<Long> headIds = tasksNeedSync.stream()
                    .map(AmUpdateAvailabilityTask::getHeadId)
                    .collect(Collectors.toList());
            
            // 查询对应的商品可用状态详情
            List<ListingGoodsHeadDetail> details = listingGoodsHeadDetailService.selectListingGoodsHeadDetailByHeadIds(headIds);

            if (details == null || details.isEmpty()) {
                log.warn("未找到需要更新的商品可用状态详情，任务结束。");
                return;
            }
            
            // 准备要更新的详情列表
            List<ListingGoodsHeadDetail> updateList = new ArrayList<>();
            List<AmUpdateAvailabilityTask> updateTasks = new ArrayList<>();
            
            for (AmUpdateAvailabilityTask task : tasksNeedSync) {
                for (ListingGoodsHeadDetail detail : details) {
                    if (detail.getHeadId().equals(task.getHeadId()) && (!Objects.equals(detail.getRpaStatus(), task.getStatus()))) {
                        // 如果RPA任务处理成功
                        if (task.getStatus().equals(2)) {
                            String logContent = "RPA处理成功，可用状态从[" + getAvailabilityText(task.getCurrentAvailability()) + "]更新为[" + getAvailabilityText(task.getTargetAvailability()) + "]";

                            detail.setCurrentAvailability(task.getTargetAvailability());
                            detail.setStartDate(task.getStartDate());
                            detail.setNextDate(task.getNextDate());
                            detail.setRpaStatus(task.getStatus());
                            listingLogService.insertSuccessListingLog(logContent, "-1", detail.getHeadId().intValue());
                        } else if (task.getStatus().equals(99)) { // 处理失败
                            detail.setRpaStatus(task.getStatus());
                            detail.setRpaErrorMsg(task.getRpaErrorMsg());
                            listingLogService.insertErrorListingLog( "RPA处理失败", "-1", detail.getHeadId().intValue(), task.getRpaErrorMsg());
                        } else{ // 处理中
                            detail.setRpaStatus(task.getStatus());
                        }
                        
                        updateList.add(detail);

                        task.setSyncFlag(1);
                        updateTasks.add(task);
                        break;
                    }
                }
            }
            
            // 批量更新商品可用状态详情
            if (!updateList.isEmpty()) {
                listingGoodsHeadDetailService.batchUpdateListingGoodsHeadDetail(updateList, updateTasks);
            }
        } catch (Exception e) {
            log.error("同步RPA处理结果失败: ", e);
        }
        
        log.info("同步RPA处理结果到商品可用状态详情完成。");
    }

    private String getAvailabilityText(Integer availability) {
        if (availability == null) {
            return "";
        }

        switch (availability) {
            case 1: return "available";
            case 2: return "temporarily unavailable";
            case 3: return "permanently unavailable";
            default: return String.valueOf(availability);
        }
    }
} 