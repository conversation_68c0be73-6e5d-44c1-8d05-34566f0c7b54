package com.suncent.smc.common.enums;

import cn.hutool.core.util.ObjUtil;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 商品刊登类型枚举
 *
 * <AUTHOR>
 * @since 2023-01-13 10:42:00
 */
@Getter
@AllArgsConstructor
public enum PublishType {

    /**
     * FBM
     */
    FBM(0, "FBM"),

    /**
     * FBA
     */
    FBA(1, "FBA"),

    /**
     * 多变
     */
    POLYTROPIC(2, "多变"),

    /**
     * 固定
     */
    FIXED(3, "固定"),

    /**
     * 拍卖
     */
    CHINESE(4, "拍卖"),

    /**
     * WM741:DF
     */
    VCDF(5, "VCDF"),

    /**
     * IH75B:PO
     */
    VCPO(6, "VCPO"),

    /**
     * TEMU
     */
    TEMU(7, "TEMU")

    ;

    /**
     * 刊登类型
     */
    private final Integer type;

    /**
     * 类型名
     */
    private final String TypeName;

    /**
     * 根据类型获取类型名
     *
     * @param type 类型
     * @return 类型名
     */
    public static String getTypeName(Integer type) {
        for (PublishType publishType : PublishType.values()) {
            if (publishType.getType().equals(type)) {
                return publishType.getTypeName();
            }
        }
        return String.valueOf(type);
    }

    /**
     * 通过name获取类型
     * @param type
     * @return
     */
    public static Integer getType(String type) {
        for (PublishType publishType : PublishType.values()) {
            if (publishType.getTypeName().equals(type)) {
                return publishType.getType();
            }
        }
        return null;
    }


    public static Boolean isAmazon(Integer type) {
        return PublishType.FBA.getType().equals(type) || PublishType.FBM.getType().equals(type)
                || PublishType.VCDF.getType().equals(type) || PublishType.VCPO.getType().equals(type);
    }

    public static Boolean isEbay(Integer type) {
        return PublishType.FIXED.getType().equals(type) || PublishType.CHINESE.getType().equals(type) || PublishType.POLYTROPIC.getType().equals(type);
    }

    public static boolean isTemu(Integer publishType) {
        return PublishType.TEMU.getType().equals(publishType);
    }

    public static boolean isEbayAuction(Integer publishType) {
        return PublishType.CHINESE.getType().equals(publishType);
    }

    public static boolean isVC(Integer publishType) {
        return PublishType.VCDF.getType().equals(publishType) || PublishType.VCPO.getType().equals(publishType);
    }


    public static Integer ebayCopyPublishType(Integer publishTypeOld,Integer publishTypeNew) {
        if (PublishType.POLYTROPIC.getType().equals(publishTypeOld)){
            return publishTypeOld;
        }
        if (PublishType.POLYTROPIC.getType().equals(publishTypeNew)){
            return publishTypeOld;
        }
        if (ObjUtil.isEmpty(publishTypeNew)){
            return publishTypeOld;
        }
        return publishTypeNew;
    }
}