package com.suncent.smc.quartz.task.listing;

import com.suncent.smc.persistence.publication.service.IMonitorListingAvailabilitySnapshotService;
import com.xxl.job.core.context.XxlJobHelper;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Date;

/**
 * 初始化商品可用性快照数据任务
 * 
 * <AUTHOR>
 * @date 2024-06-21
 */
@Component
public class MonitorListingAvailabilitySnapshotInitTask {
    private static final Logger log = LoggerFactory.getLogger(MonitorListingAvailabilitySnapshotInitTask.class);

    private static final int DEFAULT_BATCH_SIZE = 2000;

    @Autowired
    private IMonitorListingAvailabilitySnapshotService monitorListingAvailabilitySnapshotService;

    /**
     * 初始化商品可用性快照数据
     * 任务参数：可选，批量大小
     */
    @XxlJob("initMonitorListingAvailabilitySnapshotData")
    public void initMonitorListingAvailabilitySnapshotData() {
        log.info("开始初始化商品可用性快照数据");
        
        try {
            // 获取任务参数（批量大小）
            String param = XxlJobHelper.getJobParam();
            int batchSize = DEFAULT_BATCH_SIZE;
            
            if (param != null && !param.trim().isEmpty()) {
                try {
                    batchSize = Integer.parseInt(param.trim());
                } catch (NumberFormatException e) {
                    log.warn("无效的批量大小参数: {}, 使用默认值: {}", param, DEFAULT_BATCH_SIZE);
                }
            }
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -5);
            Date date = calendar.getTime();

            monitorListingAvailabilitySnapshotService.cleanOldData(date);
            // 执行初始化
            int totalProcessed = monitorListingAvailabilitySnapshotService.initSnapshotData(batchSize);
            
            log.info("商品可用性快照数据初始化完成，处理数据条数：{}", totalProcessed);
            XxlJobHelper.handleSuccess("处理数据条数：" + totalProcessed);
        } catch (Exception e) {
            log.error("商品可用性快照数据初始化失败", e);
            XxlJobHelper.handleFail("初始化失败：" + e.getMessage());
        }
    }
} 