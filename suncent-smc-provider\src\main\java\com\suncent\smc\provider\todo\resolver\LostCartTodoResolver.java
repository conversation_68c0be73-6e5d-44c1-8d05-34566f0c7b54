package com.suncent.smc.provider.todo.resolver;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjUtil;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.suncent.smc.common.core.domain.entity.SysUser;
import com.suncent.smc.common.core.text.Convert;
import com.suncent.smc.common.enums.PlatformTypeEnum;
import com.suncent.smc.common.enums.PublishType;
import com.suncent.smc.common.enums.TodoStatusEnum;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.common.utils.EnvUtils;
import com.suncent.smc.framework.thread.ThreadPoolForMonitorManager;
import com.suncent.smc.persistence.ads.domain.AdsAmazonListingDataVIO;
import com.suncent.smc.persistence.ads.domain.AdsRecordData;
import com.suncent.smc.persistence.ads.mapper.AdsItemFitCompareMapper;
import com.suncent.smc.persistence.ads.service.IAdsService;
import com.suncent.smc.persistence.cdp.domain.entity.Shop;
import com.suncent.smc.persistence.cdp.service.IShopService;
import com.suncent.smc.persistence.publication.domain.dto.GoodsDetailDTO;
import com.suncent.smc.persistence.publication.domain.dto.TodoDataDTO;
import com.suncent.smc.persistence.publication.domain.entity.AmazonMonitoringBelongShop;
import com.suncent.smc.persistence.publication.domain.entity.GoodsHead;
import com.suncent.smc.persistence.publication.domain.entity.ListingLabel;
import com.suncent.smc.persistence.publication.service.IListingLabelService;
import com.suncent.smc.persistence.todo.domain.dto.MatchTodoDataDTO;
import com.suncent.smc.persistence.todo.domain.dto.MatchTodoDataDetailDTO;
import com.suncent.smc.persistence.todo.domain.entity.LoseCartTodo;
import com.suncent.smc.persistence.todo.service.ILoseCartTodoService;
import com.suncent.smc.provider.todo.HandlerTodoComposite;
import com.suncent.smc.provider.todo.TodoResolver;
import com.suncent.smc.provider.todo.domain.TodoEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

import static com.suncent.smc.common.enums.MonitorEnum.ARRAY_GROUP_CART_DATA_NOT_GENERATED;

import java.util.*;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

/**
 * 掉购物车执行器
 */
@Slf4j
@Component
public class LostCartTodoResolver extends HandlerTodoComposite implements TodoResolver {
    @Resource
    private HandlerTodoComposite handlerTodoComposite;
    @Autowired
    IListingLabelService listingLabelService;
    @Autowired
    ILoseCartTodoService loseCartTodoService;
    @Autowired
    IAdsService adsService;
    @Autowired
    protected ThreadPoolForMonitorManager threadPoolForMonitorManager;
    private static final String default_shop_code = "VC1";

    @PostConstruct
    public void init() {
        handlerTodoComposite.moduleResolverMap.put(TodoEnum.LOST_CART.name(), this);
    }

    /**
     * 执行器
     *
     * @param todoData
     * @return
     */
    @Override
    public TodoDataDTO execute(TodoDataDTO todoData) {
        log.info("开始处理掉购物车数据");
        try {
            String today = DateUtils.getDate();
            // 昨天
            String yesterday = DateUtils.getYesterday();

            // 监控今天是否有购物车数据生成
            boolean isDataGenerated = monitorCartDataGeneration(today);
            if (!isDataGenerated) {
                return todoData;
            }

            Integer lastId = 0;
            int batchSize = 1000;
            boolean hasMoreData = true;

            while (hasMoreData) {
                // 获取需要更新发布状态的商品列表
                List<GoodsHead> goodsHeadList = goodsHeadService.listNeedUpdatePublishStatus(lastId);

                if (CollUtil.isEmpty(goodsHeadList)) {
                    hasMoreData = false;
                    break;
                }

                // 处理当前批次数据
                processCartBatch(goodsHeadList, today, yesterday);

                // 更新lastId为当前批次最后一个记录的ID
                lastId = goodsHeadList.get(goodsHeadList.size() - 1).getId();

                // 如果返回的数据少于批次大小，说明没有更多数据了
                if (goodsHeadList.size() < batchSize) {
                    hasMoreData = false;
                }
            }

            log.info("掉购物车数据处理完成");
        } catch (Exception e) {
            log.error("处理掉购物车数据时发生错误", e);
        }
        return todoData;
    }

    private void processCartBatch(List<GoodsHead> goodsHeadList, String today, String yesterday) {
        List<String> asinList = goodsHeadList.stream().map(GoodsHead::getPlatformGoodsId).distinct().collect(Collectors.toList());
        //昨天购物车数据
        List<AdsRecordData> yesterdayList = adsService.selectTodayCartList(yesterday, default_shop_code, asinList);
        Map<String, AdsRecordData> yesterdayMap = yesterdayList.stream().collect(Collectors.toMap(AdsRecordData::getPageAsin, data -> data, (existing, replacement) -> existing));
        //今天购物车数据
        List<AdsRecordData> todayList = adsService.selectTodayCartList(today, default_shop_code, asinList);
        Map<String, AdsRecordData> todayMap = todayList.stream().collect(Collectors.toMap(AdsRecordData::getPageAsin, data -> data, (existing, replacement) -> existing));

        // 找出昨天有购物车数据，今天没有购物车数据的数据
        List<String> missingAsins = asinList.stream().filter(asin -> yesterdayMap.containsKey(asin) && !todayMap.containsKey(asin)).collect(Collectors.toList());
        if (CollUtil.isEmpty(missingAsins)) {
            return;
        }
        List<AdsRecordData> cartList = missingAsins.stream().map(yesterdayMap::get).collect(Collectors.toList());
        // 生成待办
        generatedData(missingAsins, "VC1", cartList);
    }



    private boolean monitorCartDataGeneration(String today) {
        Integer count = adsService.countCartListByDate(today, default_shop_code, null);
        if (count == 0) {
            sendCartDataGenerationAlert(today);
        }
        return count != 0;
    }

      /**
     * 发送ItemTempDay数据未生成的钉钉通知
     * @param date 查询日期
     */
    private void sendCartDataGenerationAlert(String date) {
        try {
            // 构建钉钉消息内容
            Map<String, String> featureMap = new HashMap<>();
            featureMap.put("查询日期", date);
            featureMap.put("数据状态", "未检测到任何购物车数据"); 
            featureMap.put("建议操作", "请检查外部数据源是否正常运行");

            // 发送钉钉通知
            String title = "购物车数据生成监控";
            String context = String.format("检测到今天(%s)没有生成任何购物车数据，请及时检查外部数据源", date);

            if (EnvUtils.isProdProfile()) {
                dingdingMonitorInfoBiz.monitorSend(title, ARRAY_GROUP_CART_DATA_NOT_GENERATED.getMonitorType(), context, featureMap);
            }
            log.warn("发送购物车数据未生成钉钉通知：日期={}", date);
        } catch (Exception e) {
            log.error("发送购物车数据未生成钉钉通知时发生错误", e);
        }
    }
    /**
     * 执行
     *
     * @param shopCode
     * @param goods
     * @param currentDate
     */
    private void dohandle(String shopCode, List<GoodsHead> goods, String currentDate) {
        List<String> asinList = goods.stream().map(GoodsHead::getPlatformGoodsId).distinct().collect(Collectors.toList());

        List<AdsRecordData> todayList = adsService.selectTodayCartList(currentDate, null, asinList);
        if (CollUtil.isEmpty(todayList)) {
            return;
        }
        //去重
        todayList = new ArrayList<>(todayList.stream()
                .collect(Collectors.toMap(
                        data -> data.getShopCode() + "_" + data.getPageAsin(),
                        data -> data,
                        (existing, replacement) -> existing
                ))
                .values());
        if (CollUtil.isEmpty(todayList)) {
            return;
        }

        //相同asin 不是该店铺的数据->抢购物车的链接
        List<AdsRecordData> cartList = todayList.stream().filter(record -> !record.getShopCode().equals(shopCode)).collect(Collectors.toList());

        List<AdsRecordData> finalTodayList = todayList;
        //该店铺没有购物车数据
        List<String> missingAsins = asinList.stream().filter(asin -> finalTodayList.stream().noneMatch(record -> record.getShopCode().equals(shopCode) && record.getPageAsin().equals(asin))).collect(Collectors.toList());

        if (ObjUtil.isEmpty(missingAsins)) {
            return;
        }
        //生成待办
        generatedData(missingAsins, shopCode, cartList);
    }

    /**
     * 生成待办
     *
     * @param missingAsins
     */
    private void generatedData(List<String> missingAsins, String shopCode, List<AdsRecordData> cartList) {
        //找到所有的asin数据
        List<GoodsHead> allGoodsList = goodsHeadService.selectAmListingByPlatformGoodsIdList(missingAsins);
        if (ObjUtil.isEmpty(allGoodsList)) {
            log.error("掉购物车执行器在smc查询数据没有在售的链接,没有可生成的待办数据,asin集合:{}", missingAsins);
            return;
        }
        //掉购物车待办数据
        List<GoodsHead> todoList = allGoodsList.stream().filter(goodsHead -> Objects.equals(shopCode, goodsHead.getShopCode())).collect(Collectors.toList());
        if (ObjUtil.isEmpty(todoList)) {
            log.error("掉购物车执行器在smc查询数据没有在售的链接,没有可生成的待办数据,asin集合:{}", missingAsins);
            return;
        }
        //抢购物车的数据
        Map<String, GoodsHead> goodsHeadMap = new HashMap<>();
        for (AdsRecordData data : cartList) {
            GoodsHead head = allGoodsList.stream().filter(goodsHead -> Objects.equals(data.getShopCode(), goodsHead.getShopCode()) && Objects.equals(data.getPageAsin(), goodsHead.getPlatformGoodsId())).findFirst().orElse(null);
            if (ObjUtil.isEmpty(head)) {
                continue;
            }
            goodsHeadMap.put(head.getPlatformGoodsId(), head);
        }

        intoCartTodo(todoList, goodsHeadMap);
    }


    /**
     * 校验是否掉购物车 昨天有购物车、今天没购物车
     * - 该链接是vc类型的需要生成待办
     * -如果该链接不是vc类型的则需要判断在smc是否含有vc类型的链接, 有vc类型则不生成待办
     *
     * @param todoGoodsHead 需要生成掉购物车待办的数据
     * @param cartGoodsHead 抢购物车的数据
     */
    private Boolean checkLostCart(GoodsHead todoGoodsHead, GoodsHead cartGoodsHead) {
        if (ObjUtil.isEmpty(todoGoodsHead)) {
            return false;
        }
        if (ObjUtil.isEmpty(cartGoodsHead)) {
            return true;
        }
        Integer publishType = todoGoodsHead.getPublishType();
        Integer cartPublishType = cartGoodsHead.getPublishType();
        //vc链接被sc链接抢购物车
        if ((Objects.equals(publishType, PublishType.VCPO.getType()) || Objects.equals(publishType, PublishType.VCDF.getType()))
                && (Objects.equals(cartPublishType, PublishType.FBA.getType()) || Objects.equals(cartPublishType, PublishType.FBM.getType()))) {
            return true;
        }
        //sc链接被sc抢购物车
        else if ((Objects.equals(publishType, PublishType.FBA.getType()) || Objects.equals(publishType, PublishType.FBM.getType()))
                && (Objects.equals(cartPublishType, PublishType.FBA.getType()) || Objects.equals(cartPublishType, PublishType.FBM.getType()))) {
            return true;
        }
        return false;
    }

    /**
     * 插入掉购物车待办表
     *
     * @param goodsHeadList
     * @param asinMap
     */
    private void intoCartTodo(List<GoodsHead> goodsHeadList, Map<String, GoodsHead> asinMap) {
        List<String> skus = goodsHeadList.stream().map(GoodsHead::getPdmGoodsCode).distinct().collect(Collectors.toList());
//        // 过滤核心链接
//        List<AdsAmazonListingDataVIO> adsAmazonListingDataList = (CollectionUtils.isEmpty(skus)) ? Collections.emptyList() : adsService.selectAdsItemFitCompareListBySkuList(skus);
//        Map<String, AdsAmazonListingDataVIO> adsAmazonMap = Optional.ofNullable(adsAmazonListingDataList).orElse(Collections.emptyList()).stream()
//                .collect(Collectors.toMap(
//                        AdsAmazonListingDataVIO::getGoodsCode,
//                        amazonListingDataVIO -> amazonListingDataVIO,
//                        (existing, replacement) -> existing
//                ));
        List<GoodsDetailDTO> goodsDetail = pdmHttpRequestBiz.getGoodsDetail(skus);
        if (ObjUtil.isEmpty(goodsDetail)) {
            goodsDetail = new ArrayList<>();
        }
        List<LoseCartTodo> loseCartTodoList = new ArrayList<>();
        Integer batch = Convert.toInt(DateUtils.parseDateToStr("yyyyMMdd", new Date()));
        for (GoodsHead goodsHead : goodsHeadList) {
//            // 过滤非核心链接
//            AdsAmazonListingDataVIO listingDataVIO = adsAmazonMap.get(goodsHead.getPdmGoodsCode());
//            if (listingDataVIO == null) {
//                // 不存在，则直接过滤
//                continue;
//            }
//            if (!Objects.equals(goodsHead.getPlatformGoodsId(), listingDataVIO.getAsin())
//                    || !Objects.equals(goodsHead.getShopCode(), listingDataVIO.getShopCode())) {
//                // 存在，但是 Asin 和 shopCode 不一致 也过滤
//                continue;
//            }
            GoodsHead cartHead = asinMap.get(goodsHead.getPlatformGoodsId());
            //校验是否需要生成待办
//            if (!checkLostCart(goodsHead, cartHead)) {
//                log.error("掉购物车执行器校验掉购物车数据逻辑执行完毕之后,没有可生成的待办数据,需要生成待办的主键:{},抢购物车数据主键:{}", goodsHead.getId(), cartHead.getId());
//                continue;
//            }
            if (ObjUtil.isEmpty(cartHead)){
                log.error("掉购物车执行器校验是否需要生成待办完毕之后,smc没有抢购物车的链接需要生成待办,主键:{}", goodsHead.getId());
                continue;
            }
            LoseCartTodo loseCartTodo = buildTodoData(goodsHead, goodsDetail, cartHead,batch);
            loseCartTodoList.add(loseCartTodo);
        }

        if (ObjUtil.isEmpty(loseCartTodoList)) {
            log.error("掉购物车执行器校验掉购物车数据逻辑执行完毕之后,没有可生成的待办数据,需要生成待办的主键集合:{}", goodsHeadList.stream().map(GoodsHead::getId).collect(Collectors.toList()));
            return;
        }
        loseCartTodoList.forEach(e -> {
            loseCartTodoService.insertLoseCartTodo(e);
        });
    }

    /**
     * 构建入库待办数据
     *
     * @param goodsHead
     * @param goodsDetail
     * @param cartHead
     * @return
     */
    private static LoseCartTodo buildTodoData(GoodsHead goodsHead, List<GoodsDetailDTO> goodsDetail, GoodsHead cartHead, Integer batch) {
        LoseCartTodo loseCartTodo = new LoseCartTodo();
        loseCartTodo.setBatch(batch);
        loseCartTodo.setShopCode(goodsHead.getShopCode());
        loseCartTodo.setPlatformCode(goodsHead.getPlatform());
        loseCartTodo.setSku(goodsHead.getPdmGoodsCode());
        loseCartTodo.setPlatformSku(goodsHead.getPlatformGoodsCode());
        loseCartTodo.setHeadId(goodsHead.getId());
        loseCartTodo.setCategoryId(goodsHead.getCategoryId());
        GoodsDetailDTO goodsDetailDTO = goodsDetail.stream().filter(goods -> Objects.equals(goods.getGoodsCode(), goodsHead.getPdmGoodsCode())).findFirst().orElse(null);
        if (ObjUtil.isNotEmpty(goodsDetailDTO)) {
            loseCartTodo.setProductCategoryCode(goodsDetailDTO.getProductCategoryCode());
            loseCartTodo.setProductCategoryName(goodsDetailDTO.getProductCategoryName());
        }
        if (ObjUtil.isNotEmpty(cartHead)) {
            loseCartTodo.setCartHeadId(cartHead.getId());
            loseCartTodo.setCartPlatformSku(cartHead.getPlatformGoodsCode());
        }
        loseCartTodo.setAsin(goodsHead.getPlatformGoodsId());
        loseCartTodo.setPublishType(goodsHead.getPublishType());
        loseCartTodo.setOperator(goodsHead.getCreateBy());
        loseCartTodo.setStatus(TodoStatusEnum.WAIT_STATUS.getCode());
        loseCartTodo.setCreateBy("1");
        loseCartTodo.setCreateTime(DateUtils.getNowDate());
        loseCartTodo.setUpdateBy("1");
        loseCartTodo.setUpdateTime(DateUtils.getNowDate());
        return loseCartTodo;
    }

    @Override
    public void matchTodoData(MatchTodoDataDTO needMatchTodoData) {
        List<LoseCartTodo> todoList = loseCartTodoService.selectDingTalkList(needMatchTodoData.getSysUser().getUserId());

        if (ObjUtil.isEmpty(todoList)) {
            return;
        }

        doHandleAdapter(todoList);

        MatchTodoDataDetailDTO detailDTO = new MatchTodoDataDetailDTO();
        detailDTO.setTodoName(TodoEnum.LOST_CART.getTodoName());
        detailDTO.setTodoData(todoList);
        detailDTO.setTodoDataClass(LoseCartTodo.class);
        needMatchTodoData.getTodoList().add(detailDTO);
    }

    private void doHandleAdapter(List<LoseCartTodo> todoList) {
        List<Integer> operatorIds = todoList.stream()
                .map(LoseCartTodo::getOperator)
                .distinct()
                .map(operator -> Optional.ofNullable(operator)
                        .map(Integer::valueOf)
                        .orElse(null))
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        //运营人员
        if (ObjUtil.isNotEmpty(operatorIds)) {
            List<SysUser> sysUsers = sysUserService.selectUserListByUserIds(operatorIds);
            Map<String, String> operatorMap = sysUsers.stream().collect(Collectors.toMap(e -> e.getUserId().toString(), SysUser::getUserName, (k1, k2) -> k1));
            todoList.forEach(data -> {
                if (operatorMap.containsKey(data.getOperator())) {
                    data.setOperator(operatorMap.get(data.getOperator()));
                }
            });
        }
    }
}
